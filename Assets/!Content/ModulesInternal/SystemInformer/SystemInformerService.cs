namespace Content.SystemInformer
{
    using State;
    using UnityEngine;
    using Modules.Shared.SystemInformer;


    public class SystemInformerService : SystemInformerServiceBase<GameState>
    {
        public override void Initialize()
        {
            base.Initialize();

            Debug.developerConsoleVisible = false;
            Debug.developerConsoleEnabled = false;

            FPS.Style.normal.textColor = Color.black;
            Informer.Style.normal.textColor = Color.black;
            Informer.AdditionHeight = 40f;
            Touch.TouchColor = Color.blue;
            Touch.CircleRadius = 10f;

#if !PROD && !UNITY_EDITOR
            FPS.IsShow = true;
            GameState.IsShow = true;
#endif
#if UNITY_EDITOR
            FPS.IsShow = true;
            Touch.IsShow = false;
            GameState.IsShow = false;
#endif
        }
    }
}