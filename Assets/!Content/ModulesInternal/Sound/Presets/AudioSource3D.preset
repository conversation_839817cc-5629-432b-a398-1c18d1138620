%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: AudioSource3D
  m_TargetType:
    m_NativeTypeID: 82
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: OutputAudioMixerGroup
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_audioClip
    value: 
    objectReference: {fileID: 8300000, guid: eb63b2f7ffe6e425da0c9187b44aa307, type: 3}
  - target: {fileID: 0}
    propertyPath: m_PlayOnAwake
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Volume
    value: 0.4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Pitch
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: Loop
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: Mute
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: Spatialize
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: SpatializePostEffects
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: Priority
    value: 128
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: DopplerLevel
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: MinDistance
    value: 1.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: MaxDistance
    value: 23
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: Pan2D
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffMode
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: BypassEffects
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: BypassListenerEffects
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: BypassReverbZones
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.size
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].value
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
    value: -5.136618
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
    value: -5.136618
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].tangentMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].weightedMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
    value: 0.07119175
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
    value: 0.4567566
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
    value: -0.014600382
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
    value: -0.014600382
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].tangentMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].weightedMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
    value: 0.597633
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_PreInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_PostInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: rolloffCustomCurve.m_RotationOrder
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.size
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].time
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].inSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].outSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].tangentMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].weightedMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].inWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].outWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_PreInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_PostInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: panLevelCustomCurve.m_RotationOrder
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.size
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].time
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].value
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].inSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].outSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].tangentMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].weightedMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].inWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_Curve.Array.data[0].outWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_PreInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_PostInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: spreadCustomCurve.m_RotationOrder
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.size
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].time
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].value
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].inSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].outSlope
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].tangentMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].weightedMode
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].inWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_Curve.Array.data[0].outWeight
    value: 0.33333334
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_PreInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_PostInfinity
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: reverbZoneMixCustomCurve.m_RotationOrder
    value: 4
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
