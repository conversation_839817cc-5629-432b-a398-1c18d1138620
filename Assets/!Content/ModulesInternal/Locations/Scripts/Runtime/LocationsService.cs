namespace Content.Locations
{
    using System;
    using Zenject;
    using Settings;
    using LocationFlowPipeline.Data;
    using JetBrains.Annotations;
    using Cysharp.Threading.Tasks;
    using UnityEngine.AddressableAssets;
    using Modules.Shared.GlobalServices.Level;
    using Modules.Shared.GlobalServices.Saver;
    using UnityEngine.ResourceManagement.AsyncOperations;


    public sealed class LocationsService : IInitializable
    {
        public const string KEY_LOCATION = "location";
        public const string KEY_LOCATION_METADATA = "location_metadata";
        public const string KEY_SOON = "soon";

        // private LocationListData _data;

        // public LocationListData Data => _data ??= StorageService.Get<LocationListData>();
        // public LocationIndex CurrentLocationIndex { get; private set; }


        public void Initialize()
        {

            // CurrentLocationIndex = LocationIndex.FromLevel(LevelService.Current);
            // LevelService.OnLevelChanged += OnLevelChanged;
        }

        // private void OnLevelChanged(int level)
        // {
        //     CurrentLocationIndex = LocationIndex.FromLevel(level);
        // }

        // public void Dispose()
        // {
        //     LevelService.OnLevelChanged -= OnLevelChanged;
        //     Addressables.Release(_operationMetadata);
        // }
    }
}