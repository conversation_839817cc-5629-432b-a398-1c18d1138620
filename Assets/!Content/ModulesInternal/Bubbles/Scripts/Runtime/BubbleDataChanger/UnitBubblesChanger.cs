namespace ModulesInternal.Bubbles
{
    using UnityEngine;
    using Unity.Collections;
    using System.Collections.Generic;
    using Modules.TilePuzzle.Structure.Entities;


    public class UnitBubblesChanger : BubbleDataChangerBase
    {
        private readonly struct UnitData
        {
            public readonly Vector2 position;
            public readonly float radiusThresholdChunk;

            public UnitData(Vector2 position, float radiusThresholdChunk)
            {
                this.position = position;
                this.radiusThresholdChunk = radiusThresholdChunk;
            }
        }

        private UnitData[] _unitData;

        public override void Initialize(BubblesShaderChangerService service)
        {
            base.Initialize(service);

            _unitData = new UnitData[BubblesShaderChangerService.MAX_BUBBLES];
        }

        public override void Update(ref NativeArray<byte> pixelData)
        {
            var countShape = UpdateDataShader(ref pixelData);
            var rect = _service.AreaBubbles;

            foreach (var point in rect.allPositionsWithin)
            {
                var bubblesData01 = Matrix4x4.zero;
                int count = 0;

                for (var i = 0; i < countShape; i++)
                {
                    var unitData = _unitData[i];
                    var dist = (unitData.position - point).sqrMagnitude;

                    if (dist < unitData.radiusThresholdChunk)
                    {
                        bubblesData01[count] = i;
                        count++;

                        if (count == BubblesShaderChangerService.MAX_BUBBLES_CHUNK)
                            break;
                    }
                }

                bubblesData01[15] = count;

                if (count == 0)
                    _service.ChunkMaterialService.ClearChunk(point);
                else
                    _service.ChunkMaterialService.ActiveChunk(point, bubblesData01);
            }
        }

        private int UpdateDataShader(ref NativeArray<byte> pixelData)
        {
            var listUnits = _service.TilePuzzleService.SpawnerService.UnitsSpawned;
            var countShape = Mathf.Min(BubblesShaderChangerService.MAX_BUBBLES, listUnits.Count);
            var radius = _service.Settings.RadiusThresholdChunk * _service.Settings.RadiusThresholdChunk;
            var radiusMovement = _service.Settings.RadiusMovementThresholdChunk * _service.Settings.RadiusMovementThresholdChunk;

            for (var i = 0; i < countShape; i++)
            {
                var unit = listUnits[i];
                var velocity = Vector2.LerpUnclamped(unit.LastVelocity, unit.ElasticFollow.Velocity, _service.Settings.StretchSpeed);

                var index = i * BubblesShaderChangerService.LENGTH_DATA;
                SetData1Shader(ref pixelData, index, velocity, unit.PowerFill);

                index += LENGTH_ROW;
                SetData2Shader(ref pixelData, index, unit.transform.lossyScale.x, unit.IndexColorPattern, unit.TimeClimax, unit.PowerOutline);

                index += LENGTH_ROW;
                var pos = (Vector2)unit.transform.position;
                _unitData[i] = new UnitData(pos, unit.State == UnitState.Movement ? radiusMovement : radius);

                SetDataPositionShader(ref pixelData, index, pos);

                index += LENGTH_ROW;
                SetData3Shader(ref pixelData, index, unit.cornerSquare);
            }

            return countShape;
        }
    }
}