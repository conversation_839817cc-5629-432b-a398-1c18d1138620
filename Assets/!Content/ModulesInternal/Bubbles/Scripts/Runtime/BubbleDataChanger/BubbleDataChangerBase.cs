namespace ModulesInternal.Bubbles
{
    using UnityEngine;
    using Unity.Collections;
    using Redcode.Extensions;


    public abstract class BubbleDataChangerBase
    {
        protected const int LENGTH_ROW = BubblesShaderChangerService.MAX_BUBBLES * BubblesShaderChangerService.LENGTH_DATA;

        protected BubblesShaderChangerService _service;

        public virtual void Initialize(BubblesShaderChangerService service)
        {
            _service = service;
        }

        public abstract void Update(ref NativeArray<byte> pixelData);


        protected void SetData1Shader(ref NativeArray<byte> pixelData, int index, Vector2 velocity, float powerFill)
        {
            var angle = Mathf.Atan2(velocity.y, velocity.x);
            var stretch = Mathf.Clamp(velocity.sqrMagnitude * _service.Settings.StretchPower, _service.Settings.StretchMin, _service.Settings.StretchMax);
            var fill = powerFill * _service.Settings.FactorPowerFill + (stretch * (1f / _service.Settings.StretchMax) * (1f - _service.Settings.FactorPowerFill));

            pixelData[index] = (byte)((Mathf.Sin(angle) * 0.5f + 0.5f) * byte.MaxValue);
            pixelData[index + 1] = (byte)((Mathf.Cos(angle) * 0.5f + 0.5f) * byte.MaxValue);
            pixelData[index + 2] = (byte)(stretch * byte.MaxValue);
            pixelData[index + 3] = (byte)(fill * byte.MaxValue);
        }

        protected void SetData2Shader(ref NativeArray<byte> pixelData, int index, float scale, int indexColorPattern, float timeClimax, float powerOutline)
        {
            var radius = Mathf.InverseLerp(_service.Settings.RadiusMin, _service.Settings.RadiusMax, scale) * _service.Settings.Radius;
            var indexColor = (byte)(indexColorPattern * 10);
            var popAmount = _service.Settings.PopCurve.Evaluate(timeClimax);

            pixelData[index] = (byte)Mathf.Clamp(radius * byte.MaxValue, 1, byte.MaxValue);
            pixelData[index + 1] = indexColor;
            pixelData[index + 2] = (byte)(popAmount * byte.MaxValue);
            pixelData[index + 3] = (byte)(powerOutline * _service.Settings.FactorPowerOutline * byte.MaxValue);
        }

        protected void SetData3Shader(ref NativeArray<byte> pixelData, int index, Vector4 cornerSquare)
        {
            pixelData[index] = (byte)(cornerSquare.x * byte.MaxValue);
            pixelData[index + 1] = (byte)(cornerSquare.y * byte.MaxValue);
            pixelData[index + 2] = (byte)(cornerSquare.z * byte.MaxValue);
            pixelData[index + 3] = (byte)(cornerSquare.w * byte.MaxValue);
        }

        protected void SetDataPositionShader(ref NativeArray<byte> pixelData, int index, Vector2 position)
        {
            var rect = _service.AreaBubblesMovement;
            ushort xShort = (ushort)position.x.Remap(rect.xMin, rect.xMax, 0, ushort.MaxValue);
            ushort yShort = (ushort)position.y.Remap(rect.yMin, rect.yMax, 0, ushort.MaxValue);

            byte xLow = (byte)(xShort & 0xFF);
            byte xHigh = (byte)((xShort >> 8) & 0xFF);
            byte yLow = (byte)(yShort & 0xFF);
            byte yHigh = (byte)((yShort >> 8) & 0xFF);

            pixelData[index] = xLow;     // R - младший байт X
            pixelData[index + 1] = xHigh; // G - старший байт X
            pixelData[index + 2] = yLow;  // B - младший байт Y
            pixelData[index + 3] = yHigh; // A - старший байт Y
        }
    }
}