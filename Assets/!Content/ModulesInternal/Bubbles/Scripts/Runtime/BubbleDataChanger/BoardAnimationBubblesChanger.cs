namespace ModulesInternal.Bubbles
{
    using System;
    using UnityEngine;
    using Unity.Collections;
    using Modules.Shared.Helpers;
    using Modules.Shared.Extensions;
    using System.Collections.Generic;
    using Modules.TilePuzzle.Structure.Entities;


    public class BoardAnimationBubblesChanger : BubbleDataChangerBase
    {
        private struct BubbleData
        {
            public Vector2 position;
            public float scale;
            public float evaluated;
            public int indexColorPattern;
            public Vector4 cornerSquare;
            public UnitBase unit;
        }

        private BubbleData[] _bubbleData = null;
        private List<UnitBase> _unitsAdditional = new(25);
        private BoardAnimationBubblesSettings _animationSettings = null;
        private Action _onComplete = null;
        private float _endProgress = 0f;
        private float _progress = 0f;


        public void Start(BoardAnimationBubblesSettings animationSettings, Action onComplete)
        {
            _animationSettings = animationSettings;
            _onComplete = onComplete;
            _endProgress = 1f + BitMapUtils.DIMENSION * _animationSettings.Interval;
            _bubbleData = GetBubbleData();
        }

        private BubbleData[] GetBubbleData()
        {
            var data = new BubbleData[BitMapUtils.DIMENSION * BitMapUtils.DIMENSION];

            for (int x = 0; x < BitMapUtils.DIMENSION; x++)
            {
                for (int y = 0; y < BitMapUtils.DIMENSION; y++)
                {
                    int i = y * BitMapUtils.DIMENSION + x;
                    var unit = _service.TilePuzzleService.BoardService[i];

                    if (unit != null)
                    {
                        data[i] = new BubbleData()
                        {
                            unit = unit,
                            indexColorPattern = unit.IndexColorPattern,
                            position = unit.CellPosition,
                        };
                    }
                    else
                    {
                        var cellPosition = new Vector2Int(x, y);
                        var indexColorPattern = _service.TilePuzzleService.TilePuzzleSettings.RangeShapeIndexColorPattern.RangeRandom();

                        data[i] = new BubbleData()
                        {
                            indexColorPattern = indexColorPattern,
                            position = cellPosition,
                        };
                    }
                }
            }

            foreach (var shapeSlot in _service.TilePuzzleService.ShapeDispenser.ShapeSlots)
            {
                var shape = shapeSlot.Shape;

                if (shape != null)
                    _unitsAdditional.AddRange(shape.Units);
            }

            return data;
        }

        public override void Update(ref NativeArray<byte> pixelData)
        {
            if (_bubbleData == null)
                return;

            if (TryUpdateAnimation())
            {
                UpdateCornerSquare();
                UpdateDataPosition();
                UpdateDataShader(ref pixelData);
            }
        }

        private bool TryUpdateAnimation()
        {
            if (_progress >= _endProgress)
            {
                Release();
                return false;
            }
            else
            {
                _progress += Time.deltaTime / _animationSettings.EachDuration;

                for (var y = 0; y < BitMapUtils.DIMENSION; y++)
                {
                    var time = Mathf.Clamp01(_progress - (y * _animationSettings.Interval));
                    var scaleEmpty = _animationSettings.CurveEmpty.Evaluate(time);
                    var scaleUnit = _animationSettings.CurveUnit.Evaluate(time);
                    for (var x = 0; x < BitMapUtils.DIMENSION; x++)
                    {
                        var index = y * BitMapUtils.DIMENSION + x;
                        _bubbleData[index].evaluated = scaleEmpty;

                        if (_bubbleData[index].unit != null)
                            _bubbleData[index].scale = scaleUnit;
                        else
                            _bubbleData[index].scale = scaleEmpty;
                    }
                }
            }

            return true;
        }

        private void UpdateCornerSquare()
        {
            var radius = _service.Settings.UnitSquare.RadiusThresholdUnit * _service.Settings.UnitSquare.RadiusThresholdUnit;
            var radiusCorner = _service.Settings.UnitSquare.RadiusThresholdCorner * _service.Settings.UnitSquare.RadiusThresholdCorner;
            var directions = BubblesShaderChangerService.directionsCorner;

            for (var i = 0; i < _bubbleData.Length; i++)
            {
                var data = _bubbleData[i];

                Vector4 totalInfluenceOnCorners = Vector4.zero;

                for(int j = 0; j < _bubbleData.Length; j++)
                {
                    if (i == j)
                        continue;

                    var unitCheck = _bubbleData[j];
                    var dist = (data.position - unitCheck.position).sqrMagnitude;

                    if (dist < radius)
                    {
                        for(int k = 0; k < directions.Length; k++)
                        {
                            var posCorner = (Vector2)data.position + directions[k] * data.scale;

                            for(int l = 0; l < directions.Length; l++)
                            {
                                var posCornerCheck = (Vector2)unitCheck.position + directions[l] * unitCheck.scale;
                                var distCorner = (posCorner - posCornerCheck).sqrMagnitude;

                                if (distCorner < radiusCorner)
                                    totalInfluenceOnCorners[k] += (1.0f - Mathf.Clamp01(distCorner / radiusCorner));
                            }
                        }
                    }
                }

                for(int k = 0; k < directions.Length; k++)
                {
                    _bubbleData[i].cornerSquare[k] = 1.0f - Mathf.Clamp01(totalInfluenceOnCorners[k]);
                }
            }
        }

        private void UpdateDataPosition()
        {
            var radius = _service.Settings.RadiusThresholdChunk * _service.Settings.RadiusThresholdChunk;
            var rect = _service.AreaBubbles;

            foreach (var point in rect.allPositionsWithin)
            {
                var bubblesData01 = Matrix4x4.zero;
                int count = 0;

                for (var i = 0; i < _bubbleData.Length; i++)
                {
                    if (_bubbleData[i].scale > 0f)
                    {
                        var pos = _bubbleData[i].position;
                        var dist = (point - pos).sqrMagnitude;

                        if (dist < radius)
                        {
                            bubblesData01[count] = i;
                            count++;
                        }
                    }
                }

                for (var i = 0; i < _unitsAdditional.Count; i++)
                {
                    var unit = _unitsAdditional[i];

                    if (unit.isActiveAndEnabled)
                    {
                        var pos = (Vector2)unit.transform.position;
                        var dist = (point - pos).sqrMagnitude;

                        if (dist < radius)
                        {
                            bubblesData01[count] = _bubbleData.Length + i;
                            count++;
                        }
                    }
                }

                bubblesData01[15] = count;

                if (count == 0)
                    _service.ChunkMaterialService.ClearChunk(point);
                else
                    _service.ChunkMaterialService.ActiveChunk(point, bubblesData01);
            }
        }

        private void UpdateDataShader(ref NativeArray<byte> pixelData)
        {
            for (var i = 0; i < _bubbleData.Length; i++)
            {
                var data = _bubbleData[i];

                var index = i * BubblesShaderChangerService.LENGTH_DATA;
                SetData1Shader(ref pixelData, index, Vector2.zero, data.evaluated);

                index += LENGTH_ROW;
                SetData2Shader(ref pixelData, index, data.scale, data.indexColorPattern, 0f, 0f);

                index += LENGTH_ROW;
                SetDataPositionShader(ref pixelData, index, data.position);

                index += LENGTH_ROW;
                SetData3Shader(ref pixelData, index, data.cornerSquare);
            }

            for (var i = 0; i < _unitsAdditional.Count; i++)
            {
                var unit = _unitsAdditional[i];

                if (unit.isActiveAndEnabled)
                {
                    var index = (i + _bubbleData.Length) * BubblesShaderChangerService.LENGTH_DATA;
                    SetData1Shader(ref pixelData, index, Vector2.zero, unit.PowerFill);

                    index += LENGTH_ROW;
                    SetData2Shader(ref pixelData, index, unit.transform.localScale.x, unit.IndexColorPattern, unit.TimeClimax, unit.PowerOutline);

                    index += LENGTH_ROW;
                    SetDataPositionShader(ref pixelData, index, unit.transform.position);

                    index += LENGTH_ROW;
                    SetData3Shader(ref pixelData, index, unit.cornerSquare);
                }
            }
        }

        private void Release()
        {
            _bubbleData = null;
            _animationSettings = null;
            _endProgress = 0f;
            _progress = 0f;
            _unitsAdditional.Clear();
            DelegateHelper.SafeInvoke(ref _onComplete);
        }
    }
}