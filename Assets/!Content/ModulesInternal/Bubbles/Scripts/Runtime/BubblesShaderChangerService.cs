namespace ModulesInternal.Bubbles
{
    using System;
    using Zenject;
    using UnityEngine;
    using Content.State;
    using Unity.Collections;
    using Modules.TilePuzzle;
    using Redcode.Extensions;
    using Content.InteractionOnMap;
    using Modules.Shared.Extensions;
    using System.Collections.Generic;
    using Modules.TilePuzzle.Structure.Entities;


    public sealed class BubblesShaderChangerService : IInitializable, ITickable, IDisposable
    {
        public static readonly Vector2[] directionsCorner = new Vector2[]
        {
            new Vector2(0.5f, 0.5f),
            new Vector2(0.5f, -0.5f),
            new Vector2(-0.5f, 0.5f),
            new Vector2(-0.5f, -0.5f)
        };
        private static readonly int _minMaxPositionProperty = Shader.PropertyToID("_MinMaxPosition");
        private static readonly int _timeNoise01Property = Shader.PropertyToID("_TimeNoise01");

        public const float TAU = 6.28318530718f;
        public const int MAX_BUBBLES = 100;
        public const int LENGTH_DATA = 4;
        public const int MAX_BUBBLES_CHUNK = 15;
        public const int LENGTH_DATA_CHUNK = 4;

        private NativeArray<byte> _pixelData;
        private BubbleDataChangerBase _currentDataChanger;
        private BubbleDataChangerBase _defaultDataChanger = new UnitBubblesChanger();
        private BoardAnimationBubblesChanger _animationDataChanger = new BoardAnimationBubblesChanger();
        private CameraService _cameraService;
        private float _timeNoise01;

        public RectInt AreaBubbles { get; private set; }
        public RectInt AreaBubblesMovement { get; private set; }
        public ChunkMaterialService ChunkMaterialService { get; private set; }
        public TilePuzzleService TilePuzzleService { get; private set; }
        public BubblesShaderSettings Settings { get; private set; }


        [Inject]
        public void Construct(BubblesShaderSettings bubblesShaderSettings, ChunkMaterialService chunkMaterialService, TilePuzzleService tilePuzzleService, CameraService cameraService)
        {
            Settings = bubblesShaderSettings;
            ChunkMaterialService = chunkMaterialService;
            TilePuzzleService = tilePuzzleService;
            _cameraService = cameraService;
        }

        public void Initialize()
        {
            CalculateAreaBubbles();

            _pixelData = new NativeArray<byte>(MAX_BUBBLES * LENGTH_DATA * LENGTH_DATA_CHUNK, allocator: Allocator.Persistent);


            UpdateMinMaxPositionProperty();
            _currentDataChanger = _defaultDataChanger;
            _defaultDataChanger.Initialize(this);
            _animationDataChanger.Initialize(this);
            TilePuzzleService.OnChangeState += TilePuzzle_StateChanged;
            GameStateService.OnStateChanged += GameStateService_OnStateChanged;
        }

        private void GameStateService_OnStateChanged(GameState state)
        {
            if (state.IsReplay())
                StopAnimation();
        }

        private void UpdateMinMaxPositionProperty()
        {
            Settings.Material.SetVector(_minMaxPositionProperty, new Vector4(AreaBubblesMovement.xMin, AreaBubblesMovement.yMin, AreaBubblesMovement.xMax, AreaBubblesMovement.yMax));
        }

        private void CalculateAreaBubbles()
        {
            const string fadeBorders = "FADE_BORDERS";

            _cameraService.HorizontalCamera.RefreshCamera();

            var cameraWorldRect = _cameraService.Camera.GetOrthoWorldRect();

            bool wasChanged = TryClampArea(cameraWorldRect, out var areaBubbles);

            AreaBubbles = areaBubbles;

            AreaBubblesMovement = new RectInt(
                AreaBubbles.x - 1,
                AreaBubbles.y - 1,
                AreaBubbles.width + 2,
                AreaBubbles.height + 2);

            if (wasChanged)
                Settings.Material.EnableKeyword(fadeBorders);
            else
                Settings.Material.DisableKeyword(fadeBorders);
        }

        private bool TryClampArea(Rect worldRect, out RectInt clampedArea)
        {
            bool wasChanged = false;

            int xMin = Mathf.FloorToInt(worldRect.xMin);
            int yMin = Mathf.FloorToInt(worldRect.yMin);
            int width = Mathf.CeilToInt(worldRect.width + (worldRect.xMin - xMin)) + 1;
            int height = Mathf.CeilToInt(worldRect.height + (worldRect.yMin - yMin)) + 1;


            if (width > Settings.MaxSizeArea.x)
            {
                xMin = xMin + (width - Settings.MaxSizeArea.x) / 2;
                width = Settings.MaxSizeArea.x;
                wasChanged = true;
            }

            if (height > Settings.MaxSizeArea.y)
            {
                yMin = yMin + (height - Settings.MaxSizeArea.y) / 2;
                height = Settings.MaxSizeArea.y;
                wasChanged = true;
            }

            clampedArea = new RectInt(xMin, yMin, width, height);
            return wasChanged;
        }

        public void StartAnimation(BoardAnimationBubblesSettings animationSettings, Action onComplete = null)
        {
            _animationDataChanger.Start(animationSettings, onComplete);
            _currentDataChanger = _animationDataChanger;
        }

        public void StopAnimation()
        {
            _currentDataChanger = _defaultDataChanger;
        }

        public void Tick()
        {
            if (_currentDataChanger != null)
            {
                UpdateCornerSquare();

                _currentDataChanger.Update(ref _pixelData);
                ChunkMaterialService.ApplyData(ref _pixelData);
#if UNITY_EDITOR
                UpdateMinMaxPositionProperty();
#endif
            }

            _timeNoise01 = (_timeNoise01 + Time.deltaTime * Settings.NoiseSpeed) % 1f;
            Shader.SetGlobalFloat(_timeNoise01Property, _timeNoise01);
        }

        private void UpdateCornerSquare()
        {
            var listUnits = (List<UnitBase>)TilePuzzleService.SpawnerService.UnitsSpawned;
            var radius = Settings.UnitSquare.RadiusThresholdUnit * Settings.UnitSquare.RadiusThresholdUnit;
            var radiusCorner = Settings.UnitSquare.RadiusThresholdCorner * Settings.UnitSquare.RadiusThresholdCorner;

            for (int i = 0; i < listUnits.Count; i++)
            {
                var unit = listUnits[i];
                Vector4 totalInfluenceOnCorners = Vector4.zero;

                for(int j = 0; j < listUnits.Count; j++)
                {
                    if (i == j)
                        continue;

                    var unitCheck = listUnits[j];
                    var dist = (unit.transform.position - unitCheck.transform.position).sqrMagnitude;

                    if (dist < radius)
                    {
                        for(int k = 0; k < directionsCorner.Length; k++)
                        {
                            var posCorner = (Vector2)unit.transform.position + directionsCorner[k] * unit.transform.lossyScale.x;

                            for(int l = 0; l < directionsCorner.Length; l++)
                            {
                                var posCornerCheck = (Vector2)unitCheck.transform.position + directionsCorner[l] * unitCheck.transform.lossyScale.x;
                                var distCorner = (posCorner - posCornerCheck).sqrMagnitude;

                                if (distCorner < radiusCorner)
                                    totalInfluenceOnCorners[k] += (1.0f - Mathf.Clamp01(distCorner / radiusCorner));
                            }
                        }
                    }
                }

                for(int k = 0; k < directionsCorner.Length; k++)
                {
                    unit.cornerSquare[k] = 1.0f - Mathf.Clamp01(totalInfluenceOnCorners[k]);
                }
            }
        }

        private void TilePuzzle_StateChanged(TilePuzzleState state)
        {
            UpdatePriorityUnits(state);
        }

        private void UpdatePriorityUnits(TilePuzzleState state)
        {
            if (state == TilePuzzleState.ShapeMovement)
            {
                var listUnits = (List<UnitBase>)TilePuzzleService.SpawnerService.UnitsSpawned;

                for (int i = 0; i < listUnits.Count; i++)
                {
                    var unit = listUnits[i];

                    if (unit.State == UnitState.Movement)
                    {
                        listUnits.RemoveAt(i);
                        listUnits.Insert(0, unit);
                    }
                }
            }
        }

        public void Dispose()
        {
            TilePuzzleService.OnChangeState -= TilePuzzle_StateChanged;
            GameStateService.OnStateChanged -= GameStateService_OnStateChanged;

            if (_pixelData.IsCreated)
                _pixelData.Dispose();
        }
    }
}