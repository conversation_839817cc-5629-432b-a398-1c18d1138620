namespace ModulesInternal.Bubbles
{
    using Zenject;
    using UnityEngine;
    using Unity.Collections;
    using Sirenix.OdinInspector;


    public class ChunkMaterialService : SerializedMonoBehaviour
    {
        private static readonly int _textureDataProperty = Shader.PropertyToID("_TextureData");
        private static readonly int _bubbleData01Property = Shader.PropertyToID("_BubblesData01");
        [SerializeField] private MeshRenderer _prefabChunk;
        private MaterialPropertyBlock _propertyBlock;
        private MeshRenderer[] _matrixChunk;
        private BubblesShaderChangerService _bubblesShaderChangerService;
        [ShowInInspector]
        private Texture2D _textureData;


        [Inject]
        public void Construct(BubblesShaderChangerService bubblesShaderChangerService)
        {
            _bubblesShaderChangerService = bubblesShaderChangerService;
        }

        private void Awake()
        {
            _propertyBlock = new();
            _textureData = new(BubblesShaderChangerService.MAX_BUBBLES, BubblesShaderChangerService.LENGTH_DATA_CHUNK, TextureFormat.RGBA32, false, true)
            {
                filterMode = FilterMode.Point,
                wrapMode = TextureWrapMode.Clamp,
                anisoLevel = 0
            };
            _prefabChunk.sharedMaterial.SetTexture(_textureDataProperty, _textureData);

            GenerateChunks();
        }

        private void GenerateChunks()
        {
            var rect = _bubblesShaderChangerService.AreaBubbles;
            var index = 0;

            _matrixChunk = new MeshRenderer[rect.width * rect.height];

            foreach (var position in rect.allPositionsWithin)
            {
                var chunk = Instantiate(_prefabChunk, new(position.x, position.y, 0), Quaternion.identity, transform);
                chunk.enabled = false;
                chunk.gameObject.isStatic = true;
                _matrixChunk[index++] = chunk;
            }
        }

        public void ApplyData(ref NativeArray<byte> bubbleTransform)
        {
            _textureData.SetPixelData(bubbleTransform, 0);
            _textureData.Apply(false);
#if UNITY_EDITOR
            _prefabChunk.sharedMaterial.SetTexture(_textureDataProperty, _textureData);
#endif
        }

        public void ActiveChunk(Vector2Int point, Matrix4x4 bubblesData01)
        {
            if (TryGetChunkByPoint(point, out var chunk))
            {
                chunk.enabled = true;
                _propertyBlock.SetMatrix(_bubbleData01Property, bubblesData01);
                chunk.SetPropertyBlock(_propertyBlock);
            }
        }

        public void ClearChunk(Vector2Int point)
        {
            if (TryGetChunkByPoint(point, out var chunk))
                chunk.enabled = false;
        }

        private bool TryGetChunkByPoint(Vector2Int point, out MeshRenderer chunk)
        {
            var area = _bubblesShaderChangerService.AreaBubbles;

            if (area.Contains(point))
            {
                var index = point.x - area.xMin + (point.y - area.yMin) * area.width;
                chunk = _matrixChunk[index];
                return true;
            }

            chunk = null;
            return false;
        }
    }
}