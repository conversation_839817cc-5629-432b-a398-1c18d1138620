namespace ModulesInternal.Bubbles
{
    using System;
    using UnityEngine;
    using OdinContinuousGroup;
    using Sirenix.Serialization;
    using Sirenix.OdinInspector;


    [ManageableData, CreateAssetMenu(fileName = "BubblesShaderSettings", menuName = "Modules/Bubbles/BubblesShaderSettings", order = 0)]
    public class BubblesShaderSettings : SerializedScriptableObject
    {
        [Serializable]
        public struct UnitSquareSettings
        {
            [OdinSerialize] public float RadiusThresholdUnit { get; private set; }
            [OdinSerialize] public float RadiusThresholdCorner { get; private set; }
        }

        [ContinuousGroup("Unit", GroupType.FoldoutGroup)]
        [Range(0f, 1f)]
        [OdinSerialize] public float FactorPowerFill { get; private set; }
        [Range(0f, 1f)]
        [OdinSerialize] public float FactorPowerOutline { get; private set; }
        [OdinSerialize] public UnitSquareSettings UnitSquare { get; private set; }


        [ContinuousGroup("Chunk", GroupType.FoldoutGroup)]
        [OdinSerialize] public Material Material { get; private set; }
        [OdinSerialize] public float RadiusThresholdChunk { get; private set; }
        [OdinSerialize] public float RadiusMovementThresholdChunk { get; private set; }
        [OdinSerialize] public Vector2Int MaxSizeArea { get; private set; }


        [ContinuousGroup("Radius", GroupType.FoldoutGroup)]
        [Range(0f, 1f)]
        [OdinSerialize] public float Radius { get; private set; }
        [OdinSerialize] public float RadiusMin { get; private set; }
        [OdinSerialize] public float RadiusMax { get; private set; }


        [ContinuousGroup("Stretch", GroupType.FoldoutGroup)]
        [PropertyRange(0f, 1f)]
        [OdinSerialize] public float StretchPower { get; private set; }
        [PropertyRange(0f, 1f)]
        [OdinSerialize] public float StretchMin { get; private set; }
        [PropertyRange("StretchMin", 1f)]
        [OdinSerialize] public float StretchMax { get; private set; }
        [PropertyRange(0.1f, 1f)]
        [OdinSerialize] public float StretchSpeed { get; private set; }


        [ContinuousGroup("Pop", GroupType.FoldoutGroup)]
        [HideReferenceObjectPicker]
        [OdinSerialize] public AnimationCurve PopCurve { get; private set; }

        [ContinuousGroup("Noise", GroupType.FoldoutGroup)]
        [OdinSerialize] public float NoiseSpeed { get; private set; }
    }
}