namespace ModulesInternal.Bubbles
{
    using System;
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    [HideReferenceObjectPicker]
    [Serializable]
    public class BoardAnimationBubblesSettings
    {
        [HideReferenceObjectPicker]
        [OdinSerialize] public AnimationCurve CurveUnit { get; private set; } = new();
        [HideReferenceObjectPicker]
        [OdinSerialize] public AnimationCurve CurveEmpty { get; private set; } = new();
        [OdinSerialize] public float EachDuration { get; private set; }
        [OdinSerialize] public float Interval { get; private set; }
    }
}