%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Bubbles
  m_Shader: {fileID: 4800000, guid: 8e4cd5c09de514572a6d81abbe8fe2fa, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _USE_LINEAR_LIGHT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BubbleTexture:
        m_Texture: {fileID: 2800000, guid: 2ad3503974b024b0cb06a5f6c56dcc07, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture:
        m_Texture: {fileID: 2800000, guid: cf7a2bfe0ced84573826b9408ea2694a, type: 3}
        m_Scale: {x: 0.14, y: 0.12}
        m_Offset: {x: 0, y: 0}
    - _NoiseTextureState:
        m_Texture: {fileID: 2800000, guid: cf7a2bfe0ced84573826b9408ea2694a, type: 3}
        m_Scale: {x: 0.15, y: 0.19}
        m_Offset: {x: 0, y: 0}
    - _PopTexture:
        m_Texture: {fileID: 2800000, guid: f9231259b85b148dd89da77cb0114201, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0.5, y: 0.5}
    - _PositionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SlickTexture:
        m_Texture: {fileID: 2800000, guid: 209efd3e4fccd4b81800f136db56c8e3, type: 3}
        m_Scale: {x: 0.28, y: 0.55}
        m_Offset: {x: 0, y: 0}
    - _Texture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureData:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - PixelSnap: 0
    - _Blick: 5.76
    - _BubbleHeight: 1
    - _ColorMultiplier: 1
    - _ColorOffset: 0
    - _CornerRadius: 0.729
    - _CornerRadiusPower: 0.511
    - _CornerRadiusRange: 0.378
    - _DiffuseOpacity: 0.311
    - _EnableExternalAlpha: 0
    - _FillPower: 0
    - _InvFadeWidth: 1.28
    - _InvOutlineWidth: 46
    - _JunctionFillPower: 1
    - _JunctionOffset: 0.822
    - _JunctionOffset2: 0
    - _JunctionPower: 0.532
    - _JunctionPower2: 0.191
    - _NoiseCombinePower: 0.195
    - _NoiseMax: 0.369
    - _NoiseMin: -0.436
    - _NoisePower: 0.58
    - _NoiseScale: 0.4
    - _NoiseSpeed: 0.08
    - _NoiseStatePower: 0.13
    - _Opacity: 1
    - _OpacityCenter: 0.06
    - _OpacityHeight: 0.484
    - _OutlineSmooth: 46.3
    - _OutlineWidth: 0.02
    - _PopAmount: 0
    - _PopInvScale: -1
    - _PopScale: 0.61
    - _Radius: 0.58
    - _RadiusMax: 1.3
    - _RadiusMin: 0
    - _SDFBorder: 0.115
    - _SlickOpacity: 0.381
    - _SlickSpeed: 0.82
    - _SminSmoothness: 0.5
    - _SminSmoothness2: 0.173
    - _SmoothSpecular: 0.215
    - _SpecularOpacity: 0.158
    - _StretchMax: 0.5
    - _StretchMin: 0
    - _StretchPower: 0.01
    - _UVDisplacement: 1.33
    - _UseLinearLight: 1
    m_Colors:
    - _BubbleColor: {r: 1, g: 1, b: 1, a: 1}
    - _BubbleOutlineColor: {r: 8.996728, g: 9.250493, b: 18.164822, a: 1}
    - _BubblePositionA: {r: 6.44, g: 20.26, b: 0, a: 0}
    - _BubblePositionB: {r: -0.66, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _CornerRadii: {r: 0, g: 0, b: 0, a: 0}
    - _DiffuseColor: {r: 1.2311443, g: 1.2311443, b: 1.2311443, a: 0.45490196}
    - _DiffuseLight: {r: 0.08, g: -0.2, b: 0.01, a: -0.31}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _HighlightColor: {r: 0, g: 1.887191, b: 14.754423, a: 1}
    - _LightIntensityDiffuse: {r: -0.15, g: 0.38, b: -3.5714, a: -3.125}
    - _LightIntensitySpecular: {r: 0.32, g: 0.33, b: -100, a: -100}
    - _LightPos: {r: 0.21, g: -0.98, b: 0.96, a: -0.3}
    - _LightPos1: {r: 13.7, g: 4.1, b: 0, a: 0}
    - _NoiseTextureState_ST: {r: 0.04, g: 0.08, b: 0, a: 0}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _Specular: {r: 0.4, g: -0.5, b: -0.46, a: -0.49}
    - _SpecularColor: {r: 12.844471, g: 12.844471, b: 12.844471, a: 1}
    - _SpecularLight: {r: -0.26, g: -0.27, b: -0.27, a: -0.28}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
