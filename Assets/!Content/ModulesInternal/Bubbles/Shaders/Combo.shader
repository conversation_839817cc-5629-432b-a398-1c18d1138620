Shader "!Self/Modules/Bubbles/Combo"
{
    Properties
    {
        [HDR] _Color ("Color", Color) = (1, 1, 1, 1)
        [NoScaleOffset] _MainTex ("Main Texture", 2D) = "white" {}

        [Space(20)]
        [Header(Noise)]
        _Noise ("Noise", 2D) = "white" {}
        _NoiseStrength ("Noise Strength", Range(0, 1)) = 0.5
        _NoiseSpeed ("Noise Speed", Range(0, 1)) = 1

        [Space(20)]
        [Header(Pop)]
        _Pop ("Pop", Range(0, 1)) = 0

        [Space(20)]
        [Header(Outline)]
        [HDR] _ColorOutline ("Color Outline", Color) = (1, 1, 1, 1)
        _OutlineWidth ("Outline Width", Range(0, 1)) = 0.5
        _OutlineSmoothnessInv ("Outline Smoothness Inv", float) = 1

        [Space(20)]
        [Header(Outline Absolute)]
        _OutlineAbsolute ("Outline Absolute", Range(-1, 1)) = 0
        _OutlineAbsoluteWidth ("Outline Absolute Width", Range(0, 1)) = 0.5
        _OutlineAbsoluteSmoothnessInv ("Outline Absolute Smoothness Inv", float) = 1

        [Space(20)]
        [Header(Specular)]
        [HDR] _ColorSpecular ("Color Specular", Color) = (1, 1, 1, 1)
        _SpecularPos ("Specular Pos", Vector) = (0, 0, 0, 0)
        _Specular1ScaleInv ("Specular 1 Scale Inv", Float) = 1
        _Specular2ScaleInv ("Specular 2 Scale Inv", Float) = 1
        _SpecularSmoothnessInv ("Specular Smoothness Inv", Float) = 1

        [Space(20)]
        [Header(Heart)]
        _Heart ("Heart", Range(-1, 1)) = 0.5
        _HeartVolumeMin ("Heart Volume Min", Range(-5, 5)) = 0.5
        _HeartVolumeMax ("Heart Volume Max", Range(-5, 5)) = 0.5

        [Header(Heart Bloom)]
        [HDR] _ColorHeartBloom ("Color Heart Bloom", Color) = (1, 1, 1, 1)
        _HeartBloom ("Heart Bloom", Range(0, 1)) = 0.5

        [Space(20)]
        [Header(SDF)]
        [Header(Ring)]
        _Ring ("Ring", Range(-1, 1)) = 0.5
        _RingWidth ("Ring Width", Range(0, 1)) = 0.5
        _RingSmoothness ("Ring Smoothness", Range(0, 1)) = 0.5

        [Header(Smoothness)]
        _MinSmoothness ("Min Smoothness", Range(0, 1)) = 0.5
        _MaxSmoothness ("Max Smoothness", Range(0, 1)) = 0.5
    }

    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "PreviewType" = "Plane"
            "CanUseSpriteAtlas" = "True"
        }

        Cull Back
        Lighting Off
        ZWrite Off
        ZTest Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #include "UnityCG.cginc"

            sampler2D _MainTex;
            fixed4 _Color;
            sampler2D _Noise;
            half4 _Noise_ST;
            half _NoiseStrength;
            half _NoiseSpeed;

            half _Pop;

            fixed4 _ColorOutline;
            half _OutlineWidth;
            half _OutlineSmoothnessInv;

            half _OutlineAbsolute;
            half _OutlineAbsoluteWidth;
            half _OutlineAbsoluteSmoothnessInv;

            fixed4 _ColorSpecular;
            half4 _SpecularPos;
            half _Specular1ScaleInv;
            half _Specular2ScaleInv;
            half _SpecularSmoothnessInv;

            half _Heart;
            half _HeartVolumeMin;
            half _HeartVolumeMax;

            fixed4 _ColorHeartBloom;
            half _HeartBloom;

            half _Ring;
            half _RingWidth;
            half _RingSmoothness;

            half _MinSmoothness;
            half _MaxSmoothness;

            half dot2( half2 v ) { return dot(v,v); }

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                float2 uvNoise : TEXCOORD1;
            };

            v2f vert(appdata IN)
            {
                v2f OUT;

                OUT.vertex = UnityObjectToClipPos(IN.vertex);
                OUT.uv = IN.uv;
                OUT.uvNoise = TRANSFORM_TEX(IN.uv, _Noise) + frac(_Time.y * _NoiseSpeed);

                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                half noise = tex2D(_Noise, IN.uvNoise).r * _NoiseStrength;

                half2 uv = IN.uv;
                half2 uvCenter = (uv - half(0.5)) * half(2.0);
                half2 uvCenterNoise = uvCenter * noise;
                uv -= uvCenterNoise;
                uvCenter -= uvCenterNoise;

                fixed4 mainTex = tex2D(_MainTex, uv);
                half popNoise = mainTex.b - half(1.0);
                half pop = saturate(popNoise + _Pop * half(2.0));

                half heartHeight = mainTex.r - mainTex.g;

                half heartShape = step(_Heart, heartHeight);
                half heartVolume = smoothstep(_Heart - _HeartVolumeMin, _Heart + _HeartVolumeMax, heartHeight);
                half heart = saturate(heartShape - heartVolume);

                half heartBloom = smoothstep(_Heart - _HeartBloom, _Heart + _HeartBloom, heartHeight) * (half(1.0) - heartShape);

                half2 uvSpecular = uvCenter * (_Heart * half(2.5) + half(1.0));
                half specular1 = dot2((uvSpecular - _SpecularPos.xy) * (_Specular1ScaleInv));
                half specular2 = dot2((uvSpecular - _SpecularPos.zw) * (_Specular2ScaleInv));
                half specular = saturate((half(1.0) - min(specular1, specular2)) * _SpecularSmoothnessInv) * heart;

                half outline = (abs(heartHeight - _Heart) - _OutlineWidth) * _OutlineSmoothnessInv;
                half outlineAbsolute = (abs(heartHeight + _OutlineAbsolute) - _OutlineAbsoluteWidth) * _OutlineAbsoluteSmoothnessInv;

                half ring = (abs(dot2(uvCenter) - _Ring));
                ring = smoothstep(_RingWidth - _RingSmoothness, _RingWidth + _RingSmoothness, ring);

                half combinedOutline = saturate(ring + heartShape) * saturate(outline * (outlineAbsolute + heartShape));
                half smoothCombinedOutline = smoothstep(_MinSmoothness, _MaxSmoothness, combinedOutline);

                fixed4 specularColor = _ColorSpecular * specular;
                fixed4 heartColor = heart * _Color;
                fixed4 heartBloomColor = heartBloom * _ColorHeartBloom.a * _ColorHeartBloom;

                fixed4 color = lerp(heartColor + specularColor + heartBloomColor, _ColorOutline, smoothCombinedOutline * _ColorOutline.a);
                color = lerp(color, 0, pop);
                return color;
            }
            ENDCG
        }
    }
}