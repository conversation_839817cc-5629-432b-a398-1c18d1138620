Shader "!Self/Modules/Bubbles"
{
    Properties
    {
        [HDR] _BubbleColor ("Bubble Color", Color) = (1,1,1,1)
        _BubbleHeight ("Bubble Height", Range(0, 1)) = 1
        _OpacityHeight ("Opacity Height", Range(0, 1)) = 0.3
        _OpacityCenter ("Opacity Center", Range(0, 1)) = 0.7
        _Opacity ("Opacity", Range(0, 1)) = 1

        [Space(20)]
        [Header(Outline)]
        [HDR] _BubbleOutlineColor ("Bubble Outline Color", Color) = (1,1,1,1)
        // _InvOutlineWidth ("InvOutlineWidth", Float) = 46.3

        [Space(20)]
        [Header(Boundary Fade)]
        _InvFadeWidth ("InvFadeWidth", Range(0, 10)) = 0.5

        [Space(20)]
        [Header(Pop)]
        _PopTexture ("PopTexture", 2D) = "white" {}
        _PopScale ("Pop Scale", Float) = 1

        [Space(20)]
        [Header(Light)]
        _LightPos ("LightPos", Vector) = (0.1521, -0.9884, 0.9534, -0.3018)
        _LightIntensitySpecular ("Light Intensity Specular", Vector) = (0.26,0.27,-100,-100)
        _LightIntensityDiffuse ("Light Intensity Diffuse", Vector) = (0.08,0.01,-3.5714,-3.125)
        [HDR] _DiffuseColor ("Diffuse Color", Color) = (1,1,1,1)
        [HDR] _SpecularColor ("Specular Color", Color) = (1,1,1,1)

        [Space(20)]
        [Header(Slick)]
        _SlickTexture ("SlickTexture", 2D) = "white" {}
        _SlickOpacity ("SlickOpacity", Range(0, 1)) = 1
        _SlickSpeed ("SlickSpeed", Float) = 1

        [Space(20)]
        [Header(Noise)]
        // _NoiseTextureState ("NoiseTextureState", 2D) = "white" {}
        _NoiseTexture ("NoiseTexture", 2D) = "white" {}
        _NoiseTextureState_ST ("NoiseTextureState_ST", Vector) = (0.15, 0.19, 0, 0)
        _NoiseStatePower ("NoiseStatePower", Range(0, 1)) = 0
        _NoisePower ("NoisePower", Range(0, 1)) = 0
        _NoiseCombinePower ("NoiseCombinePower", Range(0, 1)) = 0
        _NoiseSpeed ("NoiseSpeed", Range(0, 1)) = 0
        _NoiseMin ("NoiseMin", Range(-1, 1)) = 0
        _NoiseMax ("NoiseMax", Range(-1, 1)) = 0

        _CornerRadiusRange ("Corner Radius Range", Range(0, 1)) = 0.0
        _CornerRadiusPower ("Corner Radius Power", Range(0, 1)) = 0.0
        // _CornerRadii ("Corner Radii (TL,TR,BR,BL)", Vector) = (0,0,0,0) // Each component 0-1. TL=TopLeft, TR=TopRight, BR=BottomRight, BL=BottomLeft.

        // [Space(20)]
        // [Header(Shape Settings)]
        // _SminSmoothness ("Bubble Blend Smoothness", Range(0.01, 0.5)) = 0.1
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Back
        Lighting Off
        ZWrite Off
        ZTest Off
        Blend One OneMinusSrcAlpha

        Pass
        {
        CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            #pragma multi_compile_instancing
            #pragma multi_compile _ FADE_BORDERS
            #include "UnitySprites.cginc"

            #define MAX_BUBBLE_COUNT 15

            fixed4 _BubbleColor;
            half _BubbleHeight;
            half _OpacityHeight;
            half _OpacityCenter;
            half _Opacity;
            half _InvFadeWidth;

            fixed4 _BubbleOutlineColor;
            // half _InvOutlineWidth;

            sampler2D _PopTexture;
            half4 _PopTexture_ST;
            half _PopAmount;
            half _PopScale;

            half4 _LightPos;
            half4 _LightIntensitySpecular;
            half4 _LightIntensityDiffuse;
            fixed4 _DiffuseColor;
            fixed4 _SpecularColor;

            sampler2D _SlickTexture;
            half4 _SlickTexture_ST;
            half _SlickOpacity;
            half _SlickSpeed;

            half4 _NoiseTextureState_ST;
            sampler2D _NoiseTexture;
            half4 _NoiseTexture_ST;
            half _NoiseStatePower;
            half _NoisePower;
            half _NoiseCombinePower;
            half _NoiseMin;
            half _NoiseMax;


            half _CornerRadiusRange;
            half _CornerRadiusPower;

            // half _SminSmoothness;

            uniform fixed4 _ColorPatternArray[14];
            uniform sampler2D _TextureData;
            uniform half4 _TextureData_TexelSize;
            uniform half4 _MinMaxPosition;
            uniform float _TimeNoise01;

            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(half4x4, _BubblesData01)
            UNITY_INSTANCING_BUFFER_END(Props)

            struct appdata
            {
                float4 vertex   : POSITION;

                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f_t
            {
                float4 vertex   : SV_POSITION;
                float2 worldPosXY  : TEXCOORD1;
                float2 uvNoise : TEXCOORD2;

                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f_t vert(appdata IN)
            {
                v2f_t OUT;

                UNITY_SETUP_INSTANCE_ID (IN);
                UNITY_TRANSFER_INSTANCE_ID(IN, OUT);

                OUT.vertex = UnityObjectToClipPos(IN.vertex);
                OUT.worldPosXY = mul(unity_ObjectToWorld, IN.vertex).xy;
                OUT.uvNoise = TRANSFORM_TEX(OUT.worldPosXY, _NoiseTexture) + float2(_TimeNoise01, _TimeNoise01 * half(2.0));

                return OUT;
            }

            struct BubbleData
            {
                half2 uv;
                half2 dist;
                half2 distCircle;
                fixed3 color;
                half powerFill;
                half powerOutline;
            };

            #define DECODE_VEC float2(float(255.0), float(65280.0))
            #define INV_65535 float(1.0 / 65535.0)

            float2 DecodePosition(half4 encodedPos)
            {
                float2 norms = float2(
                    dot(encodedPos.rg, DECODE_VEC),
                    dot(encodedPos.ba, DECODE_VEC)
                ) * INV_65535;

                return lerp(_MinMaxPosition.xy, _MinMaxPosition.zw, norms);
            }

            half sdRoundedBox(half2 p, half2 b, half4 r)
            {
                r.xy = (p.x > 0.0) ? r.xy : r.zw;
                r.x = (p.y > 0.0) ? r.x : r.y;

                r.x = ((r.x * _CornerRadiusRange) + _CornerRadiusPower) * b.x;
                half2 q = abs(p) - b + r.x;
                half2 qClamp = max(q, half2(0.0, 0.0));
                // Используем squared distance для оптимизации
                half length_squared = dot(qClamp, qClamp);
                return min(max(q.x, q.y), 0.0) + length_squared - r.x;
            }

            BubbleData GetBubbleData(float2 worldPosXY)
            {
                BubbleData data;
                data.dist = half2(0.0, 0.0);
                data.distCircle = half2(0.0, 0.0);
                data.uv = half2(0.0, 0.0);
                data.color = fixed3(0.0, 0.0, 0.0);
                data.powerFill = half(0.0);
                data.powerOutline = half(0.0);
                half4x4 bubblesData01 = UNITY_ACCESS_INSTANCED_PROP(Props, _BubblesData01);
                int count = int(bubblesData01[3].w);

                half bubbleIndexes[MAX_BUBBLE_COUNT] =
                {
                    bubblesData01[0].x, bubblesData01[1].x, bubblesData01[2].x, bubblesData01[3].x,
                    bubblesData01[0].y, bubblesData01[1].y, bubblesData01[2].y, bubblesData01[3].y,
                    bubblesData01[0].z, bubblesData01[1].z, bubblesData01[2].z, bubblesData01[3].z,
                    bubblesData01[0].w, bubblesData01[1].w, bubblesData01[2].w
                };

                half popNoise = half(1.0) - tex2D(_PopTexture, TRANSFORM_TEX(worldPosXY, _PopTexture) * _PopScale).r + half(1.0);

                half texelSizeY = _TextureData_TexelSize.y;
                half y0 = half(0.5) * texelSizeY;
                half y1 = half(1.5) * texelSizeY;
                half y2 = half(2.5) * texelSizeY;
                half y3 = half(3.5) * texelSizeY;
                half texelSizeX = _TextureData_TexelSize.x;


                [unroll(MAX_BUBBLE_COUNT)]
                for (int i = 0; i < count; i++)
                {
                    half index = bubbleIndexes[i];
                    half u = (index + half(0.5)) * texelSizeX;

                    half4 dataTex1 = tex2D(_TextureData, half2(u, y0));
                    half4 dataTex2 = tex2D(_TextureData, half2(u, y1));
                    half4 encodedPosition = tex2D(_TextureData, half2(u, y2));
                    half4 dataTex3 = tex2D(_TextureData, half2(u, y3));

                    float2 position = DecodePosition(encodedPosition);

                    half invRadius = half(1.0) / dataTex2.x;
                    half stretch = dataTex1.z;
                    half popAmount = dataTex2.z;

                    half packedSinNorm = dataTex1.x;
                    half packedCosNorm = dataTex1.y;

                    half s = (packedSinNorm - half(0.5)) * half(2.0);
                    half c = (packedCosNorm - half(0.5)) * half(2.0);
                    half2 uvBubble = (worldPosXY - position) * invRadius;

                    half2 delta = half2(
                        uvBubble.x * c + uvBubble.y * s,
                       -uvBubble.x * s + uvBubble.y * c
                    );
                    half2 delta1 = delta * (half(1.0) / max(half(1.0) - stretch, half(0.85))) + half2(stretch * half(2.0), half(0.0));
                    // half2 delta2 = delta * half2(half(1.0) / (half(1.0) + stretch * half(1.1)), half(1.25));
                    // half distCircle = min(dot(delta1, delta1), dot(delta2, delta2));
                    half distCircle = dot(delta, delta);

                    half box_extent = half(1.2);
                    half sdf2 = sdRoundedBox(uvBubble, half2(box_extent, box_extent), dataTex3);

                    // Работаем полностью с squared distance - без sqrt в цикле
                    half dist = saturate(-sdf2 / (box_extent * box_extent));
                    // dist = max(dist, 1.0 - saturate(distCircle + 0.06));
                    distCircle = half(1.0) - saturate(distCircle);
                    // dist = lerp(dist, distCircle, saturate((distCircle * 1.3) - 0.3) );

                    // dist *= saturate(popNoise - popAmount * half(2.0));
                    distCircle *= saturate(popNoise - popAmount * half(2.0));


                    if (distCircle > data.distCircle.x)
                    {
                        data.distCircle.y = data.distCircle.x;
                        data.distCircle.x = distCircle;
                        data.uv = uvBubble;
                    }
                    else if (distCircle > data.distCircle.y)
                    {
                        data.distCircle.y = distCircle;
                    }

                    if (dist > data.dist.x)
                    {
                        data.dist.y = data.dist.x;
                        data.dist.x = dist;

                        int colorPattern = round(dataTex2.y * half(25.5));
                        half colorMul = saturate(data.dist.y / data.dist.x);

                        data.color = lerp(_ColorPatternArray[colorPattern].rgb, data.color, colorMul);
                        data.powerFill = lerp(dataTex1.w, data.powerFill, colorMul);
                        data.powerOutline = lerp(dataTex2.w, data.powerOutline, colorMul);
                    }
                    else if (dist > data.dist.y)
                    {
                        data.dist.y = dist;
                    }
                }

                data.dist = sqrt(half(1.0) - data.dist);
                data.distCircle = sqrt(half(1.0) - data.distCircle);
                return data;
            }

            half smin( half a, half b, half k )
            {
                half h = max(k-abs(a-b), half(0.0))/k;
                return min(a,b) - h*h*k*half(0.25);
            }


            fixed4 frag(v2f_t IN) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(IN);

                float2 worldPosXY = IN.worldPosXY;
                BubbleData data = GetBubbleData(worldPosXY);

                //NOISE
                half noiseState = tex2D(_NoiseTexture, TRANSFORM_TEX(IN.worldPosXY, _NoiseTextureState)).r * _NoiseStatePower;
                half noiseTex = tex2D(_NoiseTexture, IN.uvNoise).r * _NoisePower;
                half combinedNoise = smoothstep(_NoiseMin, _NoiseMax, saturate(noiseState + noiseTex)) * _NoiseCombinePower;

                // HEIGHT + SDF
                half height = smin(data.dist.x, data.dist.y, half(0.15)) + combinedNoise;
                half heightEdge = step(height, half(1.0));
                half heightSphere = sqrt(saturate(half(1.0) - (height * height))) * _BubbleHeight;


                half heightCircle = data.distCircle.x + combinedNoise;
                half heightEdgeCircle = step(heightCircle, half(1.0));
                half heightSphereCircle = sqrt(saturate(half(1.0) - (heightCircle * heightCircle))) * _BubbleHeight;

                // return fixed4(heightCircle.xxx, 1);



                //UVs
                half2 uv = data.uv;
                half2 uvBubbles = uv * heightSphere;

                // BUBBLE COLOR
                fixed3 bubbleColor = data.color * _BubbleColor;

                half2 pointLight1 = half2(_LightPos.x, _LightPos.y);
                half2 pointLight2 = half2(_LightPos.z, _LightPos.w);

                //LIGHT
                // half2 pointLight1 = half2(half(0.1521), half(-0.9884));
                // half2 pointLight2 = half2(half(0.9534), half(-0.3018));

                // делаю плавный переход блика в местах пересечения пузырей
                half bubbleIntersectionBoundaryOffset = saturate(data.distCircle.y + half(0.1));
                // в каких местах нужно освещать
                half lightMask = saturate(heightSphereCircle) * bubbleIntersectionBoundaryOffset;

                half reflection1 = dot(pointLight1, uv) * lightMask;
                half reflection2 = dot(pointLight2, uv) * lightMask;
                half diffuseLight1 = saturate((reflection1 - _LightIntensityDiffuse.x) * _LightIntensityDiffuse.z) * _DiffuseColor.a;
                half diffuseLight2 = saturate((reflection2 - _LightIntensityDiffuse.y) * _LightIntensityDiffuse.w) * _DiffuseColor.a;
                half specularLight1 = saturate((reflection1 + _LightIntensitySpecular.x) * _LightIntensitySpecular.z) * _SpecularColor.a;
                half specularLight2 = saturate((reflection2 + _LightIntensitySpecular.y) * _LightIntensitySpecular.w) * _SpecularColor.a;
                half specular = saturate(specularLight1 + specularLight2);
                fixed3 colorLight = (saturate(diffuseLight1 + diffuseLight2) * _DiffuseColor.rgb + specular * _SpecularColor.rgb) * bubbleColor;

                //OUTLINE
                half lines = heightEdge * saturate(((data.distCircle.y - data.distCircle.x) - half(0.08)) * half(-64.0));
                half outlineWidth = half(100) - data.powerOutline * half(60.0);
                half outline = saturate(saturate(half(1.0) - (abs(height - half(1)) - half(0.015)) * outlineWidth) + lines);
                half3 outlineColor = data.color * _BubbleOutlineColor * (data.powerOutline * half(15.0) + half(1.0));

                // SLICK
                fixed3 slickTexture = tex2D(_SlickTexture, worldPosXY * _SlickTexture_ST.xy + (uvBubbles * (_SlickTexture_ST.xy * half(-0.4))) + _Time.x * _SlickSpeed) * _SlickOpacity;
                half lumaSlick = (Luminance(bubbleColor) * 1.0) * 2.0;
                lumaSlick = saturate(lumaSlick * lumaSlick);
                lumaSlick = (1.0 - (heightSphere - lumaSlick));
                slickTexture = (slickTexture * (1.0 - specular)) * lumaSlick;

                fixed3 color = lerp(saturate(slickTexture + colorLight + bubbleColor), outlineColor, outline);

                half opacityHeight = _OpacityHeight * (data.powerFill * half(3.0) + half(1.0));
                half alpha = max(saturate(heightEdge * (half(1.0) - heightSphereCircle) * opacityHeight + specularLight1 + specularLight2 + (heightEdge * _OpacityCenter)), outline) * _Opacity;

                // FADE BORDERS
#ifdef FADE_BORDERS
                half distL = worldPosXY.x - (_MinMaxPosition.x + half(1.0));
                half distR = _MinMaxPosition.z - half(2.0) - worldPosXY.x;
                half distB = worldPosXY.y - (_MinMaxPosition.y + half(1.0));
                half distT = _MinMaxPosition.w - half(2.0) - worldPosXY.y;
                half minDistToEdge = min(min(distL, distR), min(distB, distT));
                half fadeFactor = saturate(minDistToEdge * _InvFadeWidth);
                alpha *= fadeFactor;
#endif

                fixed4 c = fixed4(color, alpha);
                c.rgb *= c.a;
                return c;
            }
        ENDCG
        }
    }
}