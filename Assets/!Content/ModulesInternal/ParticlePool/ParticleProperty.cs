namespace Content.ModulesInternal.ParticlePool
{
    using UnityEngine;
    using UnityEngine.Pool;
    using Sirenix.OdinInspector;
    using System.Collections.Generic;
    using Modules.Shared.Helpers.Particle;


    [RequireComponent(typeof(ParticleSystem))]
    public class ParticleProperty : SerializedMonoBehaviour
    {
        public class ParticleController
        {
            private ParticleMultiplier<ParticleSystem.MainModule> _size = null;
            private ParticleMultiplier<ParticleSystem.MainModule> _speed = null;
            private ParticleColorApplyer<ParticleSystem.MainModule> _color = null;
            private bool _isColor = false;
            public ParticleSystem ParticleSystem { get; }

            private ParticleMultiplier<ParticleSystem.MainModule> Size
            {
                get
                {
                    if (_size == null)
                    {
                        var main = ParticleSystem.main;
                        _size = new(main, main.startSize,
                            (module, minMaxCurve) => module.startSize = minMaxCurve,
                            (module, multiplier) => module.startSizeMultiplier = multiplier);
                    }

                    return _size;
                }
            }
            private ParticleMultiplier<ParticleSystem.MainModule> Speed
            {
                get
                {
                    if (_speed == null)
                    {
                        var main = ParticleSystem.main;
                        _speed = new(main, main.startSpeed,
                            (module, minMaxCurve) => module.startSpeed = minMaxCurve,
                            (module, multiplier) => module.startSpeedMultiplier = multiplier);
                    }

                    return _speed;
                }
            }

            private ParticleColorApplyer<ParticleSystem.MainModule> Color
            {
                get
                {
                    if (_color == null)
                    {
                        var main = ParticleSystem.main;
                        _color = new(main, main.startColor,
                            (module, minMaxGradient) => module.startColor = minMaxGradient);
                    }

                    return _color;
                }
            }


            public ParticleController(ParticleSystem particleSystem, bool isColor)
            {
                ParticleSystem = particleSystem;
                _isColor = isColor;
            }


            public void Play()
            {
                ParticleSystem.Play(false);
            }

            public void SetSizeMultiplier(float size)
            {
                Size.Multiply(size);
            }

            public void SetSpeedMultiplier(float speed)
            {
                Speed.Multiply(speed);
            }

            public void SetColor(Color color)
            {
                if (_isColor)
                    Color.ApplyColor(color);
            }
        }

        [BoxGroup("Settings")]
        [SerializeField] private bool _isAwakeInitialize = false;
        [BoxGroup("Size")]
        [SerializeField] private float _sizeFactor = 1f;
        [BoxGroup("Size")]
        [SerializeField] private bool _isSizeTransform = false;

        [BoxGroup("Color")]
        [SerializeField] private string _tagColor = "ParticleColor";
        protected ParticleController[] _particles;

        public IReadOnlyList<ParticleController> Particles => _particles;


        protected virtual void Awake()
        {
            if (_isAwakeInitialize)
                Initialize();
        }

        public void Initialize(float? sizeFactor = null, bool? isSizeTransform = null, string tagColor = null)
        {
            if (_particles != null)
                return;

            _sizeFactor = sizeFactor ?? _sizeFactor;
            _isSizeTransform = isSizeTransform ?? _isSizeTransform;
            _tagColor = tagColor ?? _tagColor;

            var particles = ListPool<ParticleSystem>.Get();
            GetComponentsInChildren(particles);

            var count = particles.Count;
            _particles = new ParticleController[count];

            for (var i = 0; i < count; i++)
            {
                var particle = particles[i];
                var isColor = particle.CompareTag(_tagColor);
                _particles[i] = new(particle, isColor);
            }

            ListPool<ParticleSystem>.Release(particles);
        }

        public void SetSizeMultiplier(float size)
        {
            if (size >= 0f)
            {
                var scale = _sizeFactor * size;

                if (_isSizeTransform)
                {
                    transform.localScale = scale * Vector3.one;
                }
                else
                {
                    Initialize();

                    foreach (var particle in _particles)
                    {
                        particle.SetSizeMultiplier(scale);
                    }
                }
            }
        }

        public void SetSpeedMultiplier(float speed)
        {
            if (speed >= 0f)
            {
                Initialize();

                foreach (var particle in _particles)
                {
                    particle.SetSpeedMultiplier(speed);
                }
            }
        }

        public void SetColor(Color color)
        {
            Initialize();

            foreach (var particle in _particles)
            {
                particle.SetColor(color);
            }
        }

        public void Play()
        {
            Initialize();

            foreach (var particle in _particles)
            {
                particle.Play();
            }
        }

        public void Stop()
        {
            Initialize();

            foreach (var particle in _particles)
            {
                particle.ParticleSystem.Stop(false);
            }
        }
    }
}