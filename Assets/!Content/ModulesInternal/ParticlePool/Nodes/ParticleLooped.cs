namespace Content.ModulesInternal.ParticlePool
{
    using System.Linq;
    using Cysharp.Threading.Tasks;


    public class ParticleLooped : ParticleNodeBase
    {
        private float _durationParticle;


        protected override void Awake()
        {
            base.Awake();

            InitializeDurationParticle();
        }

        private void InitializeDurationParticle()
        {
            _durationParticle = ParticleProperty.Particles.Max(particle => particle.ParticleSystem.main.duration);
        }

        public async void Stop()
        {
            ParticleProperty.Stop();

            var isCancel = await UniTask.Delay((int)(_durationParticle * 1000), cancellationToken: destroyCancellationToken).SuppressCancellationThrow();

            if(!isCancel)
                Release();
        }
    }
}