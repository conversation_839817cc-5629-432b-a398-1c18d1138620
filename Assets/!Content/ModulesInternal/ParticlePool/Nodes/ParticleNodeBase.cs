namespace Content.ModulesInternal.ParticlePool
{
    using UnityEngine;
    using OdinContinuousGroup;
    using Modules.Shared.PoolEnumComponent;


    [RequireComponent(typeof(ParticleSystem))]
    public abstract class ParticleNodeBase : NodeBase<ParticleType, ParticleNodeBase>
    {
        [ContinuousGroup("Size", GroupType.FoldoutGroup)]
        [SerializeField] private float _sizeFactor = 1f;
        [SerializeField] private bool _isSizeTransform = false;
        private ParticleProperty _particleProperty;
        private bool _isInitialized = false;


        public ParticleProperty ParticleProperty
        {
            get
            {
                InitializeParticleProperty();
                return _particleProperty;
            }
        }

        protected virtual void Awake()
        {
            InitializeParticleProperty();
        }

        private void InitializeParticleProperty()
        {
            if(_isInitialized)
                return;

            _isInitialized = true;

            if(!TryGetComponent(out _particleProperty))
                _particleProperty = gameObject.AddComponent<ParticleProperty>();

            _particleProperty.Initialize(_sizeFactor, _isSizeTransform);
        }


        public virtual void Play()
        {
            InitializeParticleProperty();

            _particleProperty.Play();
        }
    }
}