namespace Content.ModulesInternal.ParticlePool
{
    using UnityEngine;
    using Cysharp.Threading.Tasks;


    public class ParticleLifespan : ParticleNodeBase
    {
        [SerializeField] private float _durationParticle;


        public override async void Play()
        {
            base.Play();

            var isCancel = await UniTask.Delay((int)(_durationParticle * 1000), cancellationToken: destroyCancellationToken).SuppressCancellationThrow();

            if(!isCancel)
                Release();
        }
    }
}