namespace Content.ModulesInternal.ParticlePool
{
    using Zenject;
    using Settings;
    using UnityEngine;
    using Modules.Shared.Pool;
    using Modules.Shared.PoolEnumComponent;


    public class ParticlePoolService : PoolEnumServiceBase<ParticleType, ParticleNodeBase>
    {
        private DiContainer _diContainer;


        public ParticlePoolService(ParticlePoolSettings settings, DiContainer diContainer) : base(settings)
        {
            _diContainer = diContainer;
        }

        protected override ComponentPool<ParticleNodeBase> CreatePool(ParticleNodeBase selectPrefab)
        {
            return new(selectPrefab,
                prefab =>
                {
#if UNITY_EDITOR
                    var created = (GameObject)UnityEditor.PrefabUtility.InstantiatePrefab(prefab.gameObject, Zenject.Internal.ZenUtilInternal.GetOrCreateInactivePrefabParent());
                    var particle = _diContainer.InjectGameObjectForComponent<ParticleNodeBase>(created);
                    created.transform.SetParent(_parentComponents);
                    return particle;
#else
                    return _diContainer.InstantiatePrefabForComponent<ParticleNodeBase>(prefab, _parentComponents);
#endif
                },
                instance => instance.gameObject.SetActive(true),
                instance => instance.gameObject.SetActive(false));
        }
    }
}