namespace Content.Effects
{
    using System;
    using Zenject;
    using PrimeTween;
    using UnityEngine;
    using Modules.TilePuzzle;
    using Modules.ColorSystem;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    public class BoardColorEffect : EffectBase<BoardColorEffect, BoardColorEffect.BoardColorSettings>
    {
        public class BoardColorSettings : EffectSettingsBase
        {
            [SerializeField] public ColorValue colorValue;


            [MinMaxSlider(0f, 1f, true)]
            [OdinSerialize] public Vector2 minMaxPower;

            [HideReferenceObjectPicker]
            [SerializeField] public AnimationCurve curveLerp;
            [Range(0f, 2f)]
            [SerializeField] public float duration;
        }

        private BoardView _boardView;
        private ColorChangerService _colorChangerService;
        private Tween _tween;
        private Color _startColor, _endColor;
        private float _power;
        private Action _onComplete;


        [Inject]
        public void Construct(TilePuzzleService tilePuzzleService, ColorChangerService colorChangerService)
        {
            _boardView = tilePuzzleService.BoardView;
            _colorChangerService = colorChangerService;
        }

        public override void Initialize()
        {

        }

        public override void Play(float power, Action onComplete = null)
        {
            if(power <= 0f)
                return;

            Stop(true);

            _power = Mathf.Lerp(Settings.minMaxPower.x, Settings.minMaxPower.y, power);
            _onComplete = onComplete;
            _startColor = _boardView.MeshColor.Color;
            _endColor = _colorChangerService.ApplyColorAndGet(ref Settings.colorValue);

            _tween = Tween.Custom(this, 0f, 1f, Settings.duration, (effect, t) =>
            {
                var lerp = effect.Settings.curveLerp.Evaluate(t) * effect._power;
                effect._boardView.MeshColor.Color = Color.Lerp(effect._startColor, effect._endColor, lerp);
            });

            if(_onComplete != null)
                _tween.OnComplete(_onComplete);
        }

        public override void Stop(bool isComplete = false)
        {
            if(isComplete)
                _tween.Complete();
            else
                _tween.Stop();
        }
    }
}