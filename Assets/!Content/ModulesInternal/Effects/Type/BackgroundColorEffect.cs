namespace Content.Effects
{
    using System;
    using Zenject;
    using PrimeTween;
    using UnityEngine;
    using Modules.ColorSystem;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Content.InteractionOnMap;


    public class BackgroundColorEffect : EffectBase<BackgroundColorEffect, BackgroundColorEffect.BackgroundColorSettings>
    {
        public class BackgroundColorSettings : EffectSettingsBase
        {
            [SerializeField] public ColorValue colorValue;

            [MinMaxSlider(0f, 1f, true)]
            [OdinSerialize] public Vector2 minMaxPower;

            [HideReferenceObjectPicker]
            [SerializeField] public AnimationCurve curveLerp;
            [Range(0.01f, 2f)]
            [SerializeField] public float duration;
        }

        private CameraService _cameraService;
        private ColorChangerService _colorChangerService;
        private Tween _tween;
        private Color _startColor, _endColor;
        private float _power;
        private Action _onComplete;


        [Inject]
        public void Construct(CameraService cameraService, ColorChangerService colorChangerService)
        {
            _cameraService = cameraService;
            _colorChangerService = colorChangerService;
        }

        public override void Initialize()
        {

        }

        public override void Play(float power, Action onComplete = null)
        {
            if(power <= 0f)
                return;

            Stop(true);

            _cameraService.CameraColorChanger.enabled = false;

            _power = Mathf.Lerp(Settings.minMaxPower.x, Settings.minMaxPower.y, power);
            _onComplete = onComplete;
            _startColor = _cameraService.Camera.backgroundColor;
            _endColor = _colorChangerService.ApplyColorAndGet(ref Settings.colorValue);

            _tween = Tween.Custom(this, 0f, 1f, Settings.duration, (effect, t) =>
            {
                var lerp = effect.Settings.curveLerp.Evaluate(t) * effect._power;
                effect._cameraService.Camera.backgroundColor = Color.Lerp(effect._startColor, effect._endColor, lerp);
            });

            _tween.OnComplete(this, effect =>
            {
                effect._cameraService.CameraColorChanger.enabled = true;
                effect._onComplete?.Invoke();
            });
        }

        public override void Stop(bool isComplete = false)
        {
            if(isComplete)
                _tween.Complete();
            else
                _tween.Stop();
        }
    }
}