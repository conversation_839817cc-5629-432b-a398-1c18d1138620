namespace Content.Effects
{
    using System;
    using Zenject;
    using PrimeTween;
    using Modules.UI;
    using UnityEngine;
    using OdinContinuousGroup;
    using Sirenix.Serialization;
    using Content.InteractionOnMap;
    using Sirenix.OdinInspector;

    public class CameraShakeEffect : EffectBase<CameraShakeEffect, CameraShakeEffect.CameraShakeSettings>
    {
        public class CameraShakeSettings : EffectSettingsBase
        {
            [ContinuousGroup("Camera", GroupType.FoldoutGroup)]
            [MinMaxSlider(0f, 1f, true)]
            [OdinSerialize] public Vector2 cameraStrength;
            [OdinSerialize] public float cameraDuration;
            [OdinSerialize] public float cameraFrequency;

            [ContinuousGroup("UI", GroupType.FoldoutGroup)]
            [MinMaxSlider(0f, 1f, true)]
            [OdinSerialize] public Vector2 uiStrength;
            [OdinSerialize] public float uiDuration;
            [OdinSerialize] public float uiFrequency;
        }

        private CameraService _cameraService;
        private UiService _uiService;
        private Sequence _tweenShake;


        [Inject]
        public void Construct(CameraService cameraService, UiService uiService)
        {
            _cameraService = cameraService;
            _uiService = uiService;
        }


        public override void Initialize()
        {

        }

        public override void Play(float power, Action onComplete = null)
        {
            if(power <= 0f)
                return;

            Stop(true);

            var cameraStrength = Mathf.Lerp(Settings.cameraStrength.x, Settings.cameraStrength.y, power);
            var uiStrength = Mathf.Lerp(Settings.uiStrength.x, Settings.uiStrength.y, power);
            _tweenShake = Tween.ShakeCamera(_cameraService.Camera, cameraStrength, Settings.cameraDuration, Settings.cameraFrequency);
            _tweenShake.Group(Tween.ShakeLocalPosition(_uiService.RootContainer.transform, new Vector3(uiStrength, uiStrength), Settings.uiDuration, Settings.uiFrequency));
            _tweenShake.Group(Tween.ShakeLocalRotation(_uiService.RootContainer.transform, new Vector3(0f, 0f, uiStrength), Settings.uiDuration, Settings.uiFrequency));

            if(onComplete != null)
                _tweenShake.OnComplete(onComplete);
        }

        public override void Stop(bool isComplete = false)
        {
            if(isComplete)
                _tweenShake.Complete();
            else
                _tweenShake.Stop();
        }
    }
}
