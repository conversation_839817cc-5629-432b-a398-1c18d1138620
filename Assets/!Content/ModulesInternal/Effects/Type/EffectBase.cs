namespace Content.Effects
{
    public interface IEffect
    {
        void Initialize();
        void Play(float power, System.Action onComplete = null);
        void Stop(bool isComplete = false);
    }

    public interface IEffectSettings
    {
        IEffect CreateEffect();
    }

    public abstract class EffectBase<TEffect, TSettings> : IEffect where TEffect : EffectBase<TEffect, TSettings>, new() where TSettings : EffectBase<TEffect, TSettings>.EffectSettingsBase
    {
        [System.Serializable]
        [Sirenix.OdinInspector.HideReferenceObjectPicker]
        public abstract class EffectSettingsBase : IEffectSettings
        {
            public TEffect CreateEffect()
            {
                return new TEffect
                {
                    Settings = (TSettings)this
                };
            }

            IEffect IEffectSettings.CreateEffect()
            {
                return CreateEffect();
            }
        }

        protected TSettings Settings { get; private set; }


        public abstract void Initialize();
        public abstract void Play(float power, System.Action onComplete = null);
        public abstract void Stop(bool isComplete = false);
    }
}