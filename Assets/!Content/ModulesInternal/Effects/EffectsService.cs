namespace Content.Effects
{
    using Zenject;
    using Settings;
    using System.Collections.Generic;


    public class EffectsService : IInitializable
    {
        private DiContainer _container;
        private EffectsSettings _settings;
        private Dictionary<EffectType, IEffect> _effects;


        [Inject]
        public void Construct(DiContainer container, EffectsSettings settings)
        {
            _container = container;
            _settings = settings;
        }

        public void Initialize()
        {
            _effects = new Dictionary<EffectType, IEffect>();
            foreach (var effectConfig in _settings.Configs.Collection)
            {
                var effect = effectConfig.Value.CreateEffect();
                _effects.Add(effectConfig.KeyEnum, effect);
                _container.Inject(effect);
                effect.Initialize();
            }
        }

        public void Play(EffectType effectType, float power, System.Action onComplete = null)
        {
            _effects[effectType].Play(power, onComplete);
        }

        public void Stop(EffectType effectType, bool isComplete = false)
        {
            _effects[effectType].Stop(isComplete);
        }
    }
}