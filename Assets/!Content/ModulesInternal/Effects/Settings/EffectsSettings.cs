namespace Content.Effects.Settings
{
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Modules.Shared.Config.Enum;


    [ManageableData, CreateAssetMenu(fileName = "EffectsSettings", menuName = "Content/EffectsSettings")]
    public class EffectsSettings : SerializedScriptableObject
    {
        [System.Serializable]
        public class EffectConfig : EnumConfig<EffectType, IEffectSettings>
        {
#if UNITY_EDITOR
            protected override EnumValue[] GetOrderEnumValues()
            {
                return new EnumValue[]
                {
                    new(EffectType.CameraShake, new CameraShakeEffect.CameraShakeSettings()),
                    new(EffectType.BackgroundColor, new BackgroundColorEffect.BackgroundColorSettings()),
                    new(EffectType.BoardColor, new BoardColorEffect.BoardColorSettings()),
                };
            }
#endif
        }

        [OdinSerialize] public EffectConfig Configs { get; private set; } = new();
    }
}