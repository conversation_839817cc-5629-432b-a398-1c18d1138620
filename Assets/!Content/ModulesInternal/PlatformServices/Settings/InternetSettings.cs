namespace Content.PlatformServices.Services.Settings
{
    using Bayat.Json;
    using UnityEngine;
    using ModulesInternal.Tags;
    using Sirenix.Serialization;
    using Modules.Shared.PlatformServices.Mediator.Internet.Settings;


    [ManageableData, CreateAssetMenu(fileName = "InternetSettings", menuName = "Modules/Shared/PlatformServices/InternetSettings", order = 0)]
    public class InternetSettings : InternetSettingsBase
    {
        [JsonProperty(Tags.Config.Internet.FORCE_ACCESS_TO_INTERNET)]
        [OdinSerialize] public bool ForceAccessToInternet { get; internal set; } = true;
    }
}