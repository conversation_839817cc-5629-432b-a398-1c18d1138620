namespace Content.PlatformServices.Services.Settings
{
    using System;
    using System.Text;
    using UnityEngine;
    using ModulesInternal.Tags;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Modules.Shared.PlatformServices.Settings.Mediator;
    using Modules.Shared.PlatformServices.ServiceProvider.Platform;


    [ManageableData, CreateAssetMenu(fileName = "SocialSettings", menuName = "Modules/Shared/PlatformServices/SocialSettings", order = 0)]
    public class SocialSettings : SerializedScriptableObject, IMediatorSettings
    {
        [Serializable]
        [HideReferenceObjectPicker]
        private class Hashtag
        {
            [Serializable]
            private struct AdditionHashtag
            {
                [HorizontalGroup("AdditionHashtag")]
                [HideLabel]
                [NonSerialized] [OdinSerialize] private PlatformAggregate _platform;
                [HorizontalGroup("AdditionHashtag")]
                [HideLabel]
                [OdinSerialize] private string _custom;

                public string Get()
                {
                    return _platform.HasCurrent() ? _custom : string.Empty;
                }
            }

            [OdinSerialize] private string _default;
            [ListDrawerSettings(DraggableItems = false)]
            [OdinSerialize] private AdditionHashtag[] _additionHashtags;


            public string Get()
            {
                var hashtag = new StringBuilder(Tags.GeneralBake.HASHTAG);
                hashtag.Append(" ");
                hashtag.Append(_default);

                foreach ( var hashtags in _additionHashtags )
                {
                    hashtag.Append(" ");
                    hashtag.Append(hashtags.Get());
                }

                return hashtag.ToString();
            }
        }


        [OdinSerialize] private Hashtag _hashtag;
        private string _hashtagCashed = null;

        [field: SerializeField] public string KeyMainText { get; private set; }
        [field: SerializeField] public string KeyCommunityLinkText { get; private set; }
        [field: NonSerialized, OdinSerialize] public PlatformAggregate PlatformJoinCommunity { get; private set; }
        [field: NonSerialized, OdinSerialize] public PlatformAggregate PlatformSupportsShare { get; private set; }


        public string GetHashtag()
        {
            return _hashtagCashed ??= _hashtag.Get();
        }
    }
}