namespace Content.PlatformServices.Services.Settings
{
    using UnityEngine;
    using OdinContinuousGroup;
    using Sirenix.Serialization;
    using Modules.Shared.RemoteConfig;
    using Modules.Shared.PlatformServices.Mediator.Ads.Settings;


    [ManageableData, CreateAssetMenu(fileName = "AdsServiceSettings", menuName = "Modules/Shared/PlatformServices/Ads/AdsServiceSettings", order = 0)]
    public class AdsServiceSettings : AdsServiceSettingsBase
    {
        [ContinuousGroup("AdInterval")]
        [OdinSerialize] public AdIntervalSettings AdIntervalSettings { get; private set; }
    }
}