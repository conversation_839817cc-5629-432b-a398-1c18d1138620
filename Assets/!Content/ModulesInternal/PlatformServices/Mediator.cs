namespace Content.PlatformServices
{
    using State;
    using System;
    using Zenject;
    using Services;
    using Services.Privacy;
    using Services.Payments;
    using Services.Internet;
    using ModulesInternal.Tags;
    using Modules.Shared.Sound;
    using Services.Notifications;
    using Modules.Shared.Helpers;
    using Modules.Shared.InputSystem;
    using Modules.Shared.GlobalServices;
    using Modules.Shared.GlobalServices.Level;
    using Modules.Shared.PlatformServices.Mediator;
    using Modules.Shared.PlatformServices.Mediator.Vibration;


    public class Mediator : MediatorGenericBase<
        Mediator,
        PrivacyService,
        InternetService,
        AdsService,
        PaymentsService,
        SocialService,
        VibrationService,
        NotificationsService>, IInitializable, IDisposable
    {
        private ISoundService _soundService;
        private InputService _inputService;


        [Inject]
        public void Construct(ISoundService soundService, InputService inputService)
        {
            _inputService = inputService;
            _soundService = soundService;
        }

        void IInitializable.Initialize()
        {
            Game.OnPause += GameOnPause;
            Game.OnResume += GameOnResume;

            LevelService.OnLevelOpen += OnLevelOpen;
            GameStateService.OnStateChanging += OnStateChanging;
            GameStateService.OnStateChanged += OnStateChanged;
        }

        private void GameOnPause()
        {
            _inputService.InputAccessor.SetActive(this, false);
            TimeScaleService.SetActive(this, false);
            _soundService.IsMute = true;
        }

        private void GameOnResume()
        {
            _inputService.InputAccessor.SetActive(this, true);
            TimeScaleService.SetActive(this, true);
            _soundService.IsMute = false;
        }

        private void OnLevelOpen(int level)
        {
            Analytics.LogEvent(
                Tags.AnalyticsBake.Event.LEVEL_UP,
                Tags.AnalyticsBake.Parameter.LEVEL,
                level);
        }

        private void OnStateChanging(GameState from, GameState to)
        {
            if(from.IsPlaying())
                Game.GameplayStop();
        }

        private void OnStateChanged(GameState state)
        {
            Analytics.SetUserProperty(Tags.AnalyticsBake.UserCustom.CURRENT_GAME_STATE, Enum<GameState>.GetName(state));

            if(state.IsPlaying() || state.IsTutorial())
                Game.GameplayStart();
        }

        void IDisposable.Dispose()
        {
            Game.OnPause -= GameOnPause;
            Game.OnResume -= GameOnResume;

            LevelService.OnLevelOpen -= OnLevelOpen;
            GameStateService.OnStateChanging -= OnStateChanging;
            GameStateService.OnStateChanged -= OnStateChanged;
        }
    }
}