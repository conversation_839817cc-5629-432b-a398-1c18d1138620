namespace Content.PlatformServices.Services
{
    using State;
    using Zenject;
    using Payments;
    using Modules.UI;
    using UI.Screens.Informer;
    using UI.Screens.Informer.Settings;
    using Modules.Shared.PlatformServices.Mediator.Ads;


    public class AdInterval : AdIntervalBase
    {
        protected override bool NoAds => Mediator.Payments.GetProduct(ProductId.NoAds).IsActive;
        private UiService _uiService;


        [Inject]
        public void Construct(UiService uiService)
        {
            _uiService = uiService;
        }

        protected override void InitializeInternal()
        {
            GameStateService.OnStateChanged += OnStateChanged;
            GameStateService.OnStateChanging += OnStateChanging;
            Mediator.Ads.OnAdsClose += OnAdsClose;
            Mediator.Payments.GetProduct(ProductId.NoAds).OnChangeActive += OnChangeActiveNoAds;

            if ( GameStateService.Current.IsPlaying() )
            {
                ResetTime();
                StartInterval();
            }
        }

        private void OnStateChanged(GameState state)
        {
            if ( state.IsPlaying() )
                StartInterval();
        }

        private void OnStateChanging(GameState from, GameState to)
        {
            if ( from.IsPlay(to) )
                ResetTime();

            if ( from.IsPlaying() )
                StopInterval();
        }

        private void OnAdsClose(bool success)
        {
            ResetTime();
        }

        private void OnChangeActiveNoAds(bool noAds)
        {
            if ( noAds )
                DisposeManagedResources();
        }

        protected override async void OnTimeExpendedInternal()
        {
            if(GameStateService.Current.IsPause())
                return;

            GameStateService.SetChanging(GameState.Pause);

            var informer = await _uiService.ShowAsync<InformerScreen>();

            GameStateService.SetChanged(GameState.Pause);

            var content = await informer.ApplyPresetAsync<InformerContentAdInterval>(InformerPreset.AdInterval);
            content.NoAdsButton.OnPurchased.AddListener(HideInformerAdInterval);
            informer.AddEventOptionalControl(OptionalControl.All, () =>
            {
                ShowAd(success =>
                {
                    if ( !success && Mediator.Ads.IsAdblockEnabled )
                    {
                        _uiService.Hide<InformerScreen>(immediately: true);

                        Mediator.Ads.ShowInformerAdBlock(onHide: () =>
                            GameStateService.SetChanging(GameState.Playing, true));
                    }
                    else
                    {
                        HideInformerAdInterval();
                    }
                });
            });
        }

        private void HideInformerAdInterval()
        {
            GameStateService.SetChanging(GameState.Playing);

            _uiService.Hide<InformerScreen>(() =>
                GameStateService.SetChanged(GameState.Playing));
        }

        protected override void DisposeManagedResources()
        {
            base.DisposeManagedResources();

            GameStateService.OnStateChanged -= OnStateChanged;
            GameStateService.OnStateChanging -= OnStateChanging;
            Mediator.Ads.OnAdsClose -= OnAdsClose;
            Mediator.Payments.GetProduct(ProductId.NoAds).OnChangeActive -= OnChangeActiveNoAds;
        }
    }
}