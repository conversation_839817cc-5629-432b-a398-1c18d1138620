namespace Content.PlatformServices.Services
{
    using Bayat.Json;
    using Modules.Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public class AdData : DataStorage
    {
        [JsonProperty] private int _hintCount;

        public int HintCount
        {
            get => _hintCount;
            set
            {
                if(_hintCount == value)
                    return;

                _hintCount = value;

                SetDirty();
            }
        }
    }
}