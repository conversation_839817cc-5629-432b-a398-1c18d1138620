namespace Content.PlatformServices.Services
{
    using TMPro;
    using State;
    using UnityEngine;
    using Sirenix.OdinInspector;


    public class HintCounter : SerializedMonoBehaviour
    {
        [SerializeField] private GameObject _content, _stateAd, _stateCounter;
        [SerializeField] private TextMeshProUGUI _textCounter;

        private void OnEnable()
        {
            UpdateHintCounter(Mediator.Ads.HintCount);
            Mediator.Ads.OnChangeHintCount += OnChangeHintCount;

            UpdateActive();
            GameStateService.OnStateChanged += OnStateChanged;
        }

        private void OnStateChanged(GameState state)
        {
            UpdateActive();
        }

        private void UpdateActive()
        {
            var isActive = GameStateService.IsChanged && GameStateService.Current is GameState.Playing or GameState.Pause;
            _content.SetActive(isActive);
        }

        private void OnChangeHintCount(int hintCount)
        {
            UpdateHintCounter(hintCount);
        }

        private void UpdateHintCounter(int hintCount)
        {
            ChangeState(hintCount > 0);
            _textCounter.text = hintCount.ToString();
        }

        private void ChangeState(bool isCounter)
        {
            _stateAd.SetActive(!isCounter);
            _stateCounter.SetActive(isCounter);
        }

        private void OnDisable()
        {
            Mediator.Ads.OnChangeHintCount -= OnChangeHintCount;
            GameStateService.OnStateChanged -= OnStateChanged;
        }
    }
}