namespace Content.PlatformServices.Services
{
    using State;
    using System;
    using Zenject;
    using Payments;
    using Settings;
    using Modules.UI;
    using UnityEngine;
    using Redcode.Extensions;
    using UI.Screens.Informer;
    using Modules.Shared.Helpers;
    using Cysharp.Threading.Tasks;
    using UI.Screens.Informer.Settings;
    using Modules.Shared.GlobalServices;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.Shared.PlatformServices.Settings;
    using Modules.Shared.PlatformServices.Mediator.Ads;


    public class AdsService : AdsServiceBase<AdsServiceSettings>//, ITickable, IDisposable
    {
        private AdData _data;
        private UiService _uiService;
        private Action<bool> _onRewardedDecisioned;

        public event Action<int> OnChangeHintCount;

        protected override bool NoAds => Mediator.Payments.GetProduct(ProductId.NoAds).IsActive;
        public AdData Data => _data ??= StorageService.Get<AdData>();
        // public AdInterval Interval { get; } = new();
        public int HintCount
        {
            get => Data.HintCount;
            private set
            {
                if ( value >= 0 && value != HintCount)
                {
                    Data.HintCount = value;
                    OnChangeHintCount?.Invoke(value);
                    StorageService.Save();
                }
            }
        }


        [Inject]
        public void Construct(DiContainer diContainer, UiService uiService)
        {
            _uiService = uiService;

            // diContainer.QueueForInject(Interval);
        }

        public override void Initialize(UnifiedServicesSettings unifiedSettings)
        {
            // OnRewardedReward += OnRewardedRewardEvent;
            OnUpdateBannerHeight += OnUpdateBannerHeightEvent;

            base.Initialize(unifiedSettings);

            Mediator.Payments.GetProduct(ProductId.NoAds).OnChangeActive += OnChangeNoAds;

            InitializeBanner();
        }

        protected override void OnReadyInternal()
        {
            base.OnReadyInternal();

            // InitializeBanner();

            // Interval.Initialize(_adsServiceSettings.AdIntervalSettings);
        }

        private void InitializeBanner()
        {
            DevDebug.LogModule("[Banner] Initialize", NAME);

            if ( IsAlwaysShowBanner && !NoAds )
            {
                OnBannerLoaded += OnBannerLoadedEvent;
            }
        }

        private void OnBannerLoadedEvent(float? height)
        {
            DevDebug.LogModule($"[Banner] OnBannerLoadedEvent: IsReadyBanner={IsReadyBanner}, IsBannerAvailable={IsBannerAvailable}", NAME);
            OnBannerLoaded -= OnBannerLoadedEvent;

            if(!NoAds)
            {
                if ( !GameStateService.Current.IsInStart() )
                {
                    HardShowBanner();
                }
                else
                {
                    DevDebug.LogModule("[Banner] Subscribe to OnStateChanged", NAME);
                    GameStateService.OnStateChanged += OnGameStateChanged_ShowBanner;
                }
            }
        }

        private void OnGameStateChanged_ShowBanner(GameState state)
        {
            if ( !state.IsInStart() )
            {
                DevDebug.LogModule("[Banner] Unsubscribe from OnStateChanged", NAME);
                GameStateService.OnStateChanged -= OnGameStateChanged_ShowBanner;

                if( !NoAds )
                    HardShowBanner();
            }
        }

/*         private void OnRewardedRewardEvent(string placementName)
        {
            UpdateHintCount();
        } */

        private void OnUpdateBannerHeightEvent(int height)
        {
            var cf = (float)height / Mediator.Screen.Resolution.y;
            _uiService.RootContainer.SetAnchorMinY(cf);
        }

/*         public void Tick()
        {
            ((ITickable)Interval).Tick();
        } */

        public override void ShowRewarded(string placementName = null, Action onRewarded = null, Action<bool> onDecisioned = null)
        {
/*             if ( TryHintReduction() )
            {
                onDecisioned?.Invoke(true);
                onRewarded?.Invoke();

                return;
            } */

            // GameStateService.SetChanging(GameState.Pause, true);

            _onRewardedDecisioned = onDecisioned;
            base.ShowRewarded(placementName, onRewarded, OnRewardedDecisionedInternal);
        }

/*         private bool TryHintReduction()
        {
            if ( HintCount > 0 )
            {
                HintCount--;

                return true;
            }

            return false;
        }

        private void UpdateHintCount()
        {
            HintCount = _adsServiceSettings.HintCount;
        }
 */
        private void OnRewardedDecisionedInternal(bool success)
        {
            if ( IsAdblockEnabled )
            {
                ShowInformerAdBlock(
                    informer => informer.AddContent(InformerPreset.AdErrorRewarded, true),
                    () => ExitRewarded(success));
            }
            /*else if ( !success )
            {
                ShowInformerErrorReward().Forget();
            }*/
            else
            {
                ExitRewarded(success);
            }
        }

        private void ExitRewarded(bool success)
        {
            DelegateHelper.SafeInvoke(ref _onRewardedDecisioned, success);
            // GameStateService.SetChanging(GameState.Playing, true);
        }

        public void ShowInformerAdBlock(Action<InformerScreen> onShow = null, Action onHide = null)
        {
            ShowInformerAdBlockInternal(onShow, onHide).Forget();
        }

        private async UniTaskVoid ShowInformerAdBlockInternal(Action<InformerScreen> onShow = null, Action onHide = null)
        {
            var informer = await ShowInformerAdBlockAsync(onHide);
            onShow?.Invoke(informer);
        }

        public async UniTask<InformerScreen> ShowInformerAdBlockAsync(Action onHide = null)
        {
            var informer = await _uiService.ShowAsync<InformerScreen>(immediately: true);
            await informer.ApplyPresetAsync(InformerPreset.Adblock);

            informer.AddEventOptionalControl(OptionalControl.All, () => _uiService.Hide<InformerScreen>(onHide));
            return informer;
        }

 /*        private async UniTaskVoid ShowInformerErrorReward()
        {
            var informer = await _uiService.ShowAsync<InformerScreen>(immediately: true);
            await informer.ApplyPresetAsync(InformerPreset.AdErrorRewarded);
            informer.ShowButtons(new(ButtonName.Ok, ButtonColor.Default, () =>
            {
                _uiService.Hide<InformerScreen>(() => ExitRewarded(false));
            }, OptionalControl.FadeBackground | OptionalControl.Escape));
        } */

/*         public void Dispose()
        {
            ((IDisposable)Interval)?.Dispose();
        } */
    }
}