namespace Content.PlatformServices.Services.Notifications.Settings
{
    using System;
    using Bayat.Json;
    using UnityEngine;
    using Sirenix.Serialization;
    using System.Collections.Generic;
    using Modules.Shared.RemoteConfig;
    using Content.ModulesInternal.Tags;
    using Modules.Shared.PlatformServices.Settings.Mediator;


    [ManageableData, CreateAssetMenu(fileName = "NotificationsSettings", menuName = "Modules/Shared/PlatformServices/NotificationsSettings", order = 0)]
    public class NotificationsSettings : RemoteConfigBase, IMediatorSettings
    {
        [Serializable]
        public struct NotificationInterval
        {
            [SerializeField] public int id;
            [SerializeField] public string title;
            [SerializeField] public string text;
            [SerializeField] public bool isRepeat;
            [SerializeField] public float hour;
        }

        protected override string ParameterKey => Tags.Config.Notifications.NOTIFICATIONS_SETTINGS;

        [JsonProperty(Tags.Config.Notifications.ENABLED)]
        [OdinSerialize] public bool IsEnabled { get; private set; }

        [JsonProperty(Tags.Config.Notifications.INTERVALS)]
        [OdinSerialize] private NotificationInterval[] _notificationIntervals;


        public IReadOnlyList<NotificationInterval> NotificationIntervals => _notificationIntervals;
    }
}