namespace Content.PlatformServices.Services.Notifications
{
    using State;
    using System;
    using Zenject;
    using Settings;
    using Locations;
    using System.Globalization;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.Shared.PlatformServices.Settings;
    using Modules.Shared.PlatformServices.Mediator.Notifications;


    public class NotificationsService : NotificationsServiceBase
    {
        private const string KEY_REQUEST = "request_permission";
        private const int INTERVAL_REQUEST_MINUTES = 60;

        private DateTime? _nextRequest;
        private bool _isPending;

        private NotificationsSettings _settings;


        public override void Initialize(UnifiedServicesSettings unifiedSettings)
        {
            _settings = unifiedSettings.GetMediator<NotificationsSettings>();
            InitializeScheduleNotifications().Forget();
        }

        private async UniTaskVoid InitializeScheduleNotifications()
        {
            if (NeedPermission)
            {
                await RequestPermissionAsync(false);
                CancelAllNotifications();

                if (_settings.IsEnabled)
                {
                    foreach (var interval in _settings.NotificationIntervals)
                    {
                        ScheduleNotification(interval.id, interval.title, interval.text, interval.hour, interval.isRepeat);
                    }
                }
            }
        }

        public override async void RequestPermission(bool isForce, Action<bool> onPermissionStatus)
        {
            if (NeedPermission && (isForce || await CanRequest()))
            {
                _isPending = true;
                base.RequestPermission(isForce, granted =>
                {
                    UpdateNextRequest();
                    _isPending = false;

                    onPermissionStatus?.Invoke(granted);
                });
            }
        }

        public override async UniTask<bool> RequestPermissionAsync(bool isForce)
        {
            if (NeedPermission && (isForce || await CanRequest()))
            {
                _isPending = true;
                var granted = await base.RequestPermissionAsync(isForce);
                UpdateNextRequest();
                _isPending = false;

                return granted;
            }

            return false;
        }

        private async UniTask<bool> CanRequest()
        {
            if (!_nextRequest.HasValue)
            {
                if (await StorageService.DirectLocal.HasKey(KEY_REQUEST) && DateTime.TryParse(await StorageService.DirectLocal.GetString(KEY_REQUEST), CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateTime))
                    _nextRequest = dateTime;
                else
                    return true;
            }

            return Mediator.Internet.Time.LocalUtcNow > _nextRequest.Value;
        }

        private void UpdateNextRequest()
        {
            _nextRequest = Mediator.Internet.Time.LocalUtcNow.AddMinutes(INTERVAL_REQUEST_MINUTES);
            StorageService.DirectLocal.Set(KEY_REQUEST, _nextRequest.Value.ToString(CultureInfo.InvariantCulture));
            StorageService.DirectLocal.Save();
        }
    }
}