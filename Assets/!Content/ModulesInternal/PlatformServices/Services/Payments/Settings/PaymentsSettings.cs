namespace Content.PlatformServices.Services.Payments.Settings
{
    using System;
    using UnityEngine;
    using ModulesInternal.Tags;
    using Modules.Shared.PlatformServices.Mediator.Payments.Settings;


    [ManageableData, CreateAssetMenu(fileName = "PaymentsSettings", menuName = "Modules/Shared/PlatformServices/PaymentsSettings", order = 0)]
    public class PaymentsSettings : PaymentsSettingsBase
    {
#if UNITY_EDITOR
        protected override Type TagsStatic => typeof(Tags.Payments);
#endif
    }
}