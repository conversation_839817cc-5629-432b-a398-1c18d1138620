namespace Content.PlatformServices.Services.Payments.Product
{
    using System.Linq;
    using Modules.Shared.PlatformServices.Mediator.Payments.Product;


    public class ProductCollection : ProductCollection<ProductId, ProductCustomBase>
    {
        public ProductCollection(params ProductCustomBase[] products) :
            base(products.ToDictionary(product => product.ProductId, product => product)) { }
    }
}