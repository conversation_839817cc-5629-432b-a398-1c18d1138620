namespace Content.PlatformServices.Services.Payments.Product
{
    using Zenject;
    using Modules.UI;
    using UI.Screens.Informer;
    using UI.Screens.Informer.Settings;
    using Modules.Shared.PlatformServices.Mediator.Payments;
    using Modules.Shared.PlatformServices.Mediator.Payments.Product;


    public abstract class ProductCustomBase : ProductBase
    {
        private UiService _uiService;

        public abstract ProductId ProductId { get;}


        [Inject]
        public void Construct(UiService uiService)
        {
            _uiService = uiService;
        }

        protected override void OnPurchaseFailedInternal(PaymentsFailureReason paymentsFailureReason)
        {
            if ( !_uiService.IsShowed<InformerScreen>() )
            {
                _uiService.Show<InformerScreen>(screen => screen
                    .ApplyPreset(InformerPreset.PurchaseError)
                    .AddEventOptionalControl(OptionalControl.All, () => _uiService.Hide<InformerScreen>())
                );
            }

            base.OnPurchaseFailedInternal(paymentsFailureReason);
        }
    }
}