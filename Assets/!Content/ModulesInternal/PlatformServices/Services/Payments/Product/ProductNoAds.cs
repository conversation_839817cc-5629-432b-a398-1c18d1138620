namespace Content.PlatformServices.Services.Payments.Product
{
    using ModulesInternal.Tags;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.Shared.PlatformServices.Mediator.Payments;
    using Modules.Shared.PlatformServices.Mediator.Payments.Data;


    public class ProductNoAds : ProductCustomBase
    {
        private readonly ProductDefinition _definition = new(Tags.Payments.NO_ADS, ProductCategory.NonConsumable);

        public override ProductDefinition Definition => _definition;
        public override ProductId ProductId => ProductId.NoAds;


        protected override void InitializeStoreInternal(bool availableToPurchase, bool isActive, ProductMetadata metadata)
        {
#if UNITY_WEBGL && !UNITY_EDITOR
            #region Migration
            if ( GamePush.GP_Player.Has(_definition.id) && GamePush.GP_Player.GetBool(_definition.id))
            {
                isActive = true;

                StorageService.Get<PaymentsData>().NonConsumableAdd(_definition.id);
            }
            #endregion
#endif

            base.InitializeStoreInternal(availableToPurchase, isActive, metadata);
        }
    }
}