namespace Content.PlatformServices.Services.Payments
{
    using Product;
    using Settings;
    using Modules.Shared.PlatformServices.Mediator.Payments;


    public class PaymentsService : PaymentsServiceBase<ProductCustomBase, PaymentsSettings>
    {
        private readonly ProductCollection _products = new(new ProductNoAds());

        protected override IProductCollection ProductsInternal => _products;
        public ProductCollection Products => _products;


        public bool AvailableToPurchase(ProductId productId)
        {
            var product = _products.Get(productId);
            return IsPaymentsAvailable && product.AvailableToPurchase;
        }

        public bool AvailableToPurchaseAndGet(ProductId productId, out ProductCustomBase product)
        {
            product = _products.Get(productId);
            return IsPaymentsAvailable && product.AvailableToPurchase;
        }

        public ProductCustomBase GetProduct(ProductId productId)
        {
            return _products.Get(productId);
        }

        public virtual void Purchase(ProductId productId)
        {
            Purchase(_products.Get(productId).Definition.id);
        }

        public virtual void Confirm(ProductId productId)
        {
            Confirm(_products.Get(productId).Definition.id);
        }

        public virtual void Cancel(ProductId productId)
        {
            Cancel(_products.Get(productId).Definition.id);
        }
    }
}