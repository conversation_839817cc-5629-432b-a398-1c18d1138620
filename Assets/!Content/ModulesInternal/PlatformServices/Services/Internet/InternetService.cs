namespace Content.PlatformServices.Services.Internet
{
    using State;
    using Zenject;
    using Settings;
    using Modules.UI;
    using UI.Screens.Informer;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.InputSystem;
    using UI.Screens.Informer.Settings;
    using Modules.Shared.PlatformServices.Mediator.Internet;
    using Modules.Shared.PlatformServices.Mediator.Internet.Checker;


    public class InternetService : InternetServiceBase<InternetSettings>
    {
        private const float DELAY_SHOW_INFORMER = 2;

        private UiService _uiService;
        private InputService _inputService;
        private GameState? _lockStateInformer;

        private bool CheckCanShowInformer => Settings.ForceAccessToInternet && !Checker.IsConnected && GameStateService.IsChanged && (GameStateService.Current is GameState.MainMenu or GameState.Playing);

        [Inject]
        public void Construct(UiService uiService, InputService inputService)
        {
            _inputService = inputService;
            _uiService = uiService;
        }

        protected override async UniTask InitializeInternal()
        {
            InitializeInformer();

            await InitializeStartForceAccess();
        }

        private async UniTask InitializeStartForceAccess()
        {
            await WaitingStatusConnectedAsync();
            await UniTask.SwitchToMainThread();
        }

        private void InitializeInformer()
        {
            Checker.OnStatusChanged += OnStatusChangedEvent;
            GameStateService.OnStateChanged += OnStateChangedGameEvent;
        }

        private void OnStatusChangedEvent(NetStatus status)
        {
            TryShowInformer().Forget();
        }

        private void OnStateChangedGameEvent(GameState state)
        {
            TryShowInformer().Forget();
        }

        private async UniTaskVoid TryShowInformer()
        {
            if ( !_lockStateInformer.HasValue && CheckCanShowInformer)
            {
                _lockStateInformer = GameStateService.Current;

                while (await _uiService.IsShowedAsync<InformerScreen>())
                {
                    await UniTask.Delay(100);

                    if(!CheckCanShowInformer)
                    {
                        _lockStateInformer = null;
                        return;
                    }
                }

                await UniTask.Delay((int)(DELAY_SHOW_INFORMER * 1000));

                if(!CheckCanShowInformer)
                {
                    _lockStateInformer = null;
                    return;
                }

                _inputService.InputAccessor.SetActive(this, false);
                GameStateService.SetChanging(GameState.Pause, true);

                var informer = await _uiService.ShowAsync<InformerScreen>();
                await informer.ApplyPresetAsync(InformerPreset.NoInternet);

                await WaitingStatusConnectedAsync();

                GameStateService.SetChanging(_lockStateInformer.Value);
                _uiService.Hide<InformerScreen>(() =>
                {
                    GameStateService.SetChanged(_lockStateInformer.Value);
                    _inputService.InputAccessor.SetActive(this, true);

                    _lockStateInformer = null;
                });
            }
        }

        private UniTask WaitingStatusConnectedAsync()
        {
#if !PROD
            return UniTask.WaitUntil(() => !Settings.ForceAccessToInternet || Checker.IsConnected);
#else
            return Checker.WaitingStatusConnectedAsync();
#endif
        }
    }
}