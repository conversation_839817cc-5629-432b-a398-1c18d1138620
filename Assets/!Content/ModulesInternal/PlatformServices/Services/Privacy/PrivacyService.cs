namespace Content.PlatformServices.Services.Privacy
{
    using Modules.Shared.GlobalServices;
    using Modules.Shared.PlatformServices.Mediator.Privacy;
    using Modules.Shared.PlatformServices.ServiceProvider.Privacy;


    public class PrivacyService : PrivacyServiceBase
    {
        protected override void OnGather(string error)
        {
            CustomDebug.LogModule($"OnGather: {error}", PrivacyProviderBase.NAME);
        }
    }
}