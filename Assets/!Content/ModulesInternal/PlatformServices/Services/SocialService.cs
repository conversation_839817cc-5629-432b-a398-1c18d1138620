namespace Content.PlatformServices.Services
{
    using Zenject;
    using Settings;
    using System.Text;
    using ModulesInternal.Tags;
    using ModulesInternal.Score;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.Extensions;
    using Modules.Shared.CustomPlugin.Localization;
    using Modules.Shared.PlatformServices.Settings;
    using Modules.Shared.PlatformServices.Mediator.Social;
    using Modules.Shared.PlatformServices.ServiceProvider.Platform;


    public class SocialService : SocialServiceBase
    {
        private SocialSettings _socialSettings;
        private ScoreService _scoreService;

        public override bool IsSupported => _socialSettings.PlatformSupportsShare.HasCurrent() && base.IsSupported;
        public override bool CanJoinCommunity => _socialSettings.PlatformJoinCommunity.HasCurrent() && base.CanJoinCommunity;


        [Inject]
        public void Construct(ScoreService scoreService)
        {
            _scoreService = scoreService;
        }

        public override void Initialize(UnifiedServicesSettings unifiedSettings)
        {
            _socialSettings = unifiedSettings.GetMediator<SocialSettings>();

            base.Initialize(unifiedSettings);
        }


        protected override UniTask<string> BuildShareMessage() =>
            BuildMessage();

        protected override UniTask<string> BuildInviteMessage() =>
            BuildMessage();

        private async UniTask<string> BuildMessage()
        {
            StringBuilder post = new StringBuilder();

            _scoreService.SendScoreToLeaderboard();

            var mainText = await LocalizationStringChanger.GetLocalizedString(_socialSettings.KeyMainText);
            var playerPosition = await Mediator.Leaderboard.GetPlayerRatingAsync();
            var score = _scoreService.GetBestScore();

            post.AppendLine(string.Format(mainText, playerPosition.ToString(), score.ToString()));

            if ( CanJoinCommunity )
            {
                var communityLink = CommunityLink;
                var communityText = await LocalizationStringChanger.GetLocalizedString(_socialSettings.KeyCommunityLinkText);

                post.AppendLine();
                post.AppendLine($"[{communityLink}|{Tags.GeneralBake.STUDIOS}] - {communityText}");
            }

            var hashtag = _socialSettings.GetHashtag();
            if ( !hashtag.IsNullOrEmpty() )
            {
                post.AppendLine();
                post.AppendLine(hashtag);
            }

            return post.ToString();
        }
    }
}