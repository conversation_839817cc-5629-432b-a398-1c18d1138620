namespace Content.ColorSystem.Settings
{
    using UnityEngine;
    using Modules.ColorSystem;


    [ManageableData, CreateAssetMenu(fileName = "ColorSettings", menuName = "Modules/ColorSystem/ColorSettings", order = 0)]
    public sealed class ColorsSettings : ColorSettingsBase
    {
#if UNITY_EDITOR
        protected override PatternColorBase GetPattern() =>
            new PatternColorInfo();
#endif
    }
}