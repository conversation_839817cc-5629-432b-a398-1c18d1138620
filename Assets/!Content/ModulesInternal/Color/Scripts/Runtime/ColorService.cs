namespace Content.ColorSystem
{
    using Zenject;
    using Settings;
    using Modules.ColorSystem;


    public class ColorService : IInitializable
    {
        private ColorChangerService _colorChangerService;
        private ColorsSettings _colorsSettings;

        public ColorChangerService ColorChangerService => _colorChangerService;
        public ColorsSettings ColorsSettings => _colorsSettings;


        [Inject]
        public void Construct(ColorChangerService colorChangerService, ColorsSettings colorsSettings)
        {
            _colorsSettings = colorsSettings;
            _colorChangerService = colorChangerService;
        }

        public void Initialize()
        {
            _colorChangerService.ChangePattern(_colorsSettings.GetPatternById(_colorsSettings.IdStarter));
        }
    }
}