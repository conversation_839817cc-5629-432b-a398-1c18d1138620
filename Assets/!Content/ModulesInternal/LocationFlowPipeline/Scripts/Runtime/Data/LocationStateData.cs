namespace Content.LocationFlowPipeline.Data
{
    using Bayat.Json;
    using Modules.TilePuzzle.Data;
    using Modules.Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public class LocationStateData : DataStorage
    {
        [JsonProperty] private int _countPlay = 0;
        [JsonProperty("p")] private int _passagePhase = 0;
        [JsonProperty("t")] public TilePuzzleData TilePuzzleData { get; private set; } = new();


        public int PassagePhase
        {
            get =>  _passagePhase;
            set
            {
                if(_passagePhase == value)
                    return;

                _passagePhase = value;

                SetDirty();
            }
        }

        public int CountPlay
        {
            get => _countPlay;
            set
            {
                if(_countPlay == value)
                    return;

                _countPlay = value;

                SetDirty();
            }
        }

        internal void ResetLevelContent()
        {
            _passagePhase = 0;
            TilePuzzleData.ResetData();

            SetDirty();
        }
    }
}