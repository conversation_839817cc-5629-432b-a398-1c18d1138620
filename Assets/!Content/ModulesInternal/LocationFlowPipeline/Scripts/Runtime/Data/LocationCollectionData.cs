namespace Content.LocationFlowPipeline.Data
{
    using Bayat.Json;
    using System.Collections.Generic;
    using Modules.Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public class LocationCollectionData : DataStorage
    {
        [JsonProperty] private readonly Dictionary<int, LocationStateData> _locationDatas = new();

        public IReadOnlyDictionary<int, LocationStateData> LocationDatas => _locationDatas;


        public LocationStateData GetDataLocation(ModeType mode)
        {
            var flowTypeInt = (int)mode;

            if ( _locationDatas.TryGetValue(flowTypeInt, out var levelData) )
                return levelData;

            levelData = new();
            _locationDatas.Add(flowTypeInt, levelData);

            return levelData;
        }

        public bool TryResetDataLocation(ModeType mode)
        {
            var flowTypeInt = (int)mode;

            if ( _locationDatas.TryGetValue(flowTypeInt, out var levelData) )
            {
                levelData.ResetLevelContent();

                return true;
            }

            return false;
        }
    }
}