namespace Content.LocationFlowPipeline.PhaseState
{
    using State;
    using Zenject;
    using Modules.UI;
    using Cysharp.Threading.Tasks;


    public class PhaseStartClassicState : PhaseStateEnumBase<ClassicPassagePhase>
    {
        private LocationFlowServiceBase _locationFlowService;
        // private TutorialService _tutorialService;
        private UiService _uiService;
        internal override ClassicPassagePhase PassagePhase => ClassicPassagePhase.StartClassic;


        [Inject]
        public void Construct(LocationFlowServiceBase locationFlowService, /*TutorialService tutorialService,*/ UiService uiService)
        {
            _uiService = uiService;
            // _tutorialService = tutorialService;
            _locationFlowService = locationFlowService;
        }

        internal override async UniTask Enter()
        {
            await base.Enter();

            if(GameStateService.Current != GameState.Pause)
                GameStateService.SetChanging(GameState.Pause, true);

            await _locationFlowService.NextPhaseState();

            // TODO: Add tutorial
            // if(!_tutorialService.TryStart(TutorialFunnelType.StartPhaseCat, OnCompleteTutorialFunnel))
            //     await _locationFlowService.NextPhaseState();
        }

        // private void OnCompleteTutorialFunnel(TutorialFunnelType funnelType)
        // {
        //     GameStateService.SetChanging(GameState.Playing, true);
        //     _locationFlowService.NextPhaseState().Forget();
        // }

        internal override async UniTask Pass()
        {
            if(GameStateService.Current != GameState.Pause)
                GameStateService.SetChanging(GameState.Pause, true);

            await base.Pass();
        }

        internal override async UniTask Exit()
        {
            await base.Exit();
            _locationFlowService.NextCountPlay();
        }
    }
}