namespace Content.LocationFlowPipeline
{
    using Zenject;
    using PhaseState;
    using Modules.Shared.Helpers;
    using Cysharp.Threading.Tasks;
    using Content.PlatformServices;
    using Content.ModulesInternal.Tags;
    using Content.ModulesInternal.Score;
    using Modules.Shared.GlobalServices.Level;
    using Modules.TilePuzzle.Structure.Services;
    using Modules.Shared.PlatformServices.ServiceProvider;


    public class LocationFlowClassicService : LocationFlowServiceBase
    {
        private ScoreService _scoreService;

        public override ModeType Mode => ModeType.Classic;

        protected override PhaseStateBase[] PhaseStates { get; } = new PhaseStateBase[] {
            new PhaseStartClassicState(),
            new PhaseClassicState(),
            new PhaseFinishState()
        };



        [Inject]
        public void Construct(ScoreService scoreService)
        {
            _scoreService = scoreService;
        }


        public override void NextCountPlay()
        {
            base.NextCountPlay();

            Mediator.Analytics.LogEvent(Tags.AnalyticsBake.Event.LEVEL_START,
                new Parameter(Tags.Analytics.Parameter.COUNT_PLAY, Data.CountPlay),
                new Parameter(Tags.AnalyticsBake.ParameterCustom.MODE, Enum<ModeType>.GetName(Mode))
            );
        }

        public override UniTask Replay(bool isResetData = false, bool isAnalytics = true)
        {
            if (isAnalytics)
            {
                var score = ((ValueScoreService)_tilePuzzleService.ScoreService).Score;
                var maxCombo = ((ValueComboService)_tilePuzzleService.ComboService).MaxCombo;
                var maxClimaxLines = _tilePuzzleService.BoardService.MaxClimaxLines;
                var scoreTier = _scoreService.GetScoreTierByScore(score);

                LogEnd(Tags.Analytics.Event.LEVEL_RESET, score, scoreTier, maxCombo, maxClimaxLines);
            }

            return base.Replay(isResetData, isAnalytics);
        }

        public void LogEnd(string eventName, int score, ScoreTier scoreTier, int maxCombo, int maxClimaxLines)
        {
            Mediator.Analytics.LogEvent(eventName,
                new Parameter(Tags.AnalyticsBake.ParameterCustom.MODE, nameof(ModeType.Classic)),
                new Parameter(Tags.AnalyticsBake.Parameter.SCORE, score),
                new Parameter(Tags.Analytics.Parameter.SCORE_TIER, Enum<ScoreTier>.GetName(scoreTier)),
                new Parameter(Tags.Analytics.Parameter.MAX_COMBO, maxCombo),
                new Parameter(Tags.Analytics.Parameter.MAX_LINES, maxClimaxLines)
            );
        }
    }
}