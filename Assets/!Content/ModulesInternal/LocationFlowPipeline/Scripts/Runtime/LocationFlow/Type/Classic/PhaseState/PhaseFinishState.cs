namespace Content.LocationFlowPipeline.PhaseState
{
    using State;
    using Zenject;
    using Modules.UI;
    using Modules.TilePuzzle;
    using Cysharp.Threading.Tasks;
    using Content.PlatformServices;
    using Content.ModulesInternal.Tags;
    using Content.ModulesInternal.Score;
    using Content.UI.Screens.EndScreen.Win;
    using Content.UI.Screens.EndScreen.Lose;
    using Modules.TilePuzzle.Structure.Services;


    public class PhaseFinishState : PhaseStateEnumBase<ClassicPassagePhase>
    {
        private UiService _uiService;
        private LocationFlowClassicService _locationFlowService;
        private ScoreService _scoreService;
        private TilePuzzleService _tilePuzzleService;

        internal override ClassicPassagePhase PassagePhase => ClassicPassagePhase.Finish;



        [Inject]
        public void Construct(UiService uiService,
            LocationFlowServiceBase locationFlowService,
            ScoreService scoreService,
            TilePuzzleService tilePuzzleService)
        {
            _locationFlowService = (LocationFlowClassicService)locationFlowService;
            _uiService = uiService;
            _scoreService = scoreService;
            _tilePuzzleService = tilePuzzleService;
        }


        internal override async UniTask Enter()
        {
            await base.Enter();

            // TryUnlockAchievements();

            if(GameStateService.Current != GameState.Pause)
                GameStateService.SetChanging(GameState.Pause, true);

            GameStateService.SetChanging(GameState.GameOver, true);

            Mediator.Ads.ShowBanner();

            var score = ((ValueScoreService)_tilePuzzleService.ScoreService).Score;
            var maxCombo = ((ValueComboService)_tilePuzzleService.ComboService).MaxCombo;
            var maxClimaxLines = _tilePuzzleService.BoardService.MaxClimaxLines;

            var scoreTier = _scoreService.GetScoreTierByScore(score);

            _scoreService.SetScore(score);
            _locationFlowService.ResetData(true);

            _locationFlowService.LogEnd(Tags.AnalyticsBake.Event.LEVEL_END, score, scoreTier, maxCombo, maxClimaxLines);

            if(scoreTier == ScoreTier.None)
            {
                await Mediator.Ads.ShowInterstitialAsync(Tags.Ads.Interstitial.BEFORE_LOSE);
                var screen = await _uiService.ShowAsync<LoseScreen>();
                screen.PutScore(score);
            }
            else
            {
                await Mediator.Ads.ShowInterstitialAsync(Tags.Ads.Interstitial.BEFORE_WIN);
                var screen = await _uiService.ShowAsync<WinScreen>();
                screen.PutScore(score, scoreTier);
            }
        }

        // private void TryUnlockAchievements()
        // {
        //     Mediator.Achievements.Unlock($"{_locationsService.CurrentLocationIndex.GetLocationKey()}_{Tags.Achievements.COMPLETE}");
        //
        //     var allCount = _searchSubjectService.GetCount(SubjectGroup.Points).ToString();
        //     var foundCount = _searchSubjectService.GetCountFound(SubjectGroup.Points).ToString();
        //
        //     if(foundCount == allCount)
        //         Mediator.Achievements.Unlock($"{_locationsService.CurrentLocationIndex.GetLocationKey()}_{Tags.Achievements.POINT}");
        // }
    }
}