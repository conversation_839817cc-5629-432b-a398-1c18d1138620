namespace Content.LocationFlowPipeline.PhaseState
{
    using State;
    using Sound;
    using Zenject;
    using Modules.UI;
    using PlatformServices;
    using Modules.TilePuzzle;
    using ModulesInternal.Score;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.PlatformServices.Mediator.Vibration;


    public class PhaseClassicState : PhaseStateEnumBase<ClassicPassagePhase>
    {
        private UiService _uiService;
        private ScoreService _scoreService;
        private SoundService _soundService;
        private TilePuzzleService _tilePuzzleService;
        private LocationFlowServiceBase _locationFlowService;
        internal override ClassicPassagePhase PassagePhase => ClassicPassagePhase.Classic;


        [Inject]
        public void Construct(UiService uiService, ScoreService scoreService, SoundService soundService, TilePuzzleService tilePuzzleService, LocationFlowServiceBase locationFlowService)
        {
            _soundService = soundService;
            _scoreService = scoreService;
            _uiService = uiService;
            _tilePuzzleService = tilePuzzleService;
            _locationFlowService = locationFlowService;
        }

        internal override async UniTask Enter()
        {
            await base.Enter();

            _tilePuzzleService.StartWork();

            if(GameStateService.Current != GameState.Playing)
                GameStateService.SetChanging(GameState.Playing, true);
        }
    }
}