namespace Content.LocationFlowPipeline
{
    using State;
    using System;
    using Zenject;
    using Modules.Shared.Focus;
    using Modules.Shared.GlobalServices.Saver;


    public class AutoSaverLocationService : IInitializable, IDisposable
    {
        private FocusService _focusService;


        [Inject]
        public void Construct(FocusService focusService)
        {
            _focusService = focusService;
        }

        public void Initialize()
        {
            GameStateService.OnStateChanging += OnStateChanging;
            _focusService.OnUnionChange += OnUnionChange;
        }

        private void OnStateChanging(GameState from, GameState to)
        {
            if ( from.IsPlaying() || to.IsPlaying() )
                StorageService.Save();
        }

        private void OnUnionChange(bool hasFocus)
        {
            StorageService.Save();
        }

        public void Dispose()
        {
            GameStateService.OnStateChanging -= OnStateChanging;
            _focusService.OnUnionChange -= OnUnionChange;
        }
    }
}