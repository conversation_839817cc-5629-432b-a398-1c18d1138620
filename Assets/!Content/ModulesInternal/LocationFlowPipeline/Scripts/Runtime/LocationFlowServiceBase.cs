namespace Content.LocationFlowPipeline
{
    using Data;
    using System;
    using Zenject;
    using PhaseState;
    using Modules.UI;
    using Content.State;
    using PlatformServices;
    using Modules.TilePuzzle;
    using ModulesInternal.Tags;
    using Sirenix.OdinInspector;
    using Modules.Shared.Helpers;
    using Cysharp.Threading.Tasks;
    using Content.UI.Screens.Gameplay;
    using Modules.Shared.GlobalServices;
    using Modules.Shared.GlobalServices.Level;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.Shared.PlatformServices.ServiceProvider;


    public abstract class LocationFlowServiceBase : SerializedMonoBehaviour
    {
        protected TilePuzzleService _tilePuzzleService;
        private UiService _uiService;
        private int _currentIndexPhaseState = -1;

        public event Action<int> OnChangePhaseState;
        protected abstract PhaseStateBase[] PhaseStates { get; }
        public abstract ModeType Mode { get; }
        public LocationStateData Data { get; private set; }
        public PhaseStateBase CurrentPassagePhase => _currentIndexPhaseState == -1 ? null : PhaseStates[_currentIndexPhaseState];


        [Inject]
        public void Construct(DiContainer diContainer, TilePuzzleService tilePuzzleService, UiService uiService)
        {
            _tilePuzzleService = tilePuzzleService;
            _uiService = uiService;

            foreach (var phaseState in PhaseStates)
            {
                diContainer.QueueForInject(phaseState);
            }
        }

        public virtual async UniTask InitializeAsync()
        {
            Data = StorageService.Get<LocationCollectionData>().GetDataLocation(Mode);

            await _uiService.ShowAsync<GameplayScreen>();

            _tilePuzzleService.Initialize(Data.TilePuzzleData);

            await LoadPhaseState();
        }

        private async UniTask LoadPhaseState()
        {
            _currentIndexPhaseState = Array.FindIndex(PhaseStates, phaseState => phaseState.PassagePhaseNumber == Data.PassagePhase);

            for (var i = 0; i < _currentIndexPhaseState; i++)
            {
                await PhaseStates[i].Pass();
            }

            if (CurrentPassagePhase != null)
                OnChangePhaseState?.Invoke(CurrentPassagePhase.PassagePhaseNumber);

            await PhaseStates[_currentIndexPhaseState].Enter();
        }

        public async UniTask NextPhaseState()
        {
            if (_currentIndexPhaseState == PhaseStates.Length - 1)
            {
                CustomDebug.LogErrorModule("Current phase state last", "LocationLevel");

                return;
            }

            await PhaseStates[_currentIndexPhaseState].Exit();
            NextPhaseStateIndex();
            await PhaseStates[_currentIndexPhaseState].Enter();

            StorageService.Save();
        }

        private void NextPhaseStateIndex()
        {
            _currentIndexPhaseState++;

            if (CurrentPassagePhase != null)
            {
                Data.PassagePhase = CurrentPassagePhase.PassagePhaseNumber;
                OnChangePhaseState?.Invoke(CurrentPassagePhase.PassagePhaseNumber);
            }

        }

        public void ResetData(bool andSave)
        {
            if (StorageService.Get<LocationCollectionData>().TryResetDataLocation(Mode) && andSave)
                StorageService.Save(true);
        }

        public virtual void NextCountPlay()
        {
            Data.CountPlay++;
            StorageService.Save();
        }

        public virtual async UniTask Replay(bool isResetData = false, bool isAnalytics = true)
        {
            if (isResetData)
                ResetData(true);

            GameStateService.SetChanging(GameState.Replay, true);
            await LoadPhaseState();
        }
    }
}