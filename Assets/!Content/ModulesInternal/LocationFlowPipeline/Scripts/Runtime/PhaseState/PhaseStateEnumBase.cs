namespace Content.LocationFlowPipeline.PhaseState
{
    using System;
    using Modules.Shared.Helpers;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.GlobalServices;


    public abstract class PhaseStateEnumBase<TEnum> : PhaseStateBase where TEnum : struct, Enum
    {
        internal abstract TEnum PassagePhase { get; }
        public sealed override int PassagePhaseNumber => (int)Enum<TEnum>.GetNumber(PassagePhase);


        internal override UniTask Enter()
        {
            CustomDebug.LogModule($"'Enter' PhaseState({Enum<TEnum>.GetName(PassagePhase)})", "LocationLevel");

            return base.Enter();
        }
        internal override UniTask Exit()
        {
            CustomDebug.LogModule($"'Exit' PhaseState({Enum<TEnum>.GetName(PassagePhase)})", "LocationLevel");

            return base.Exit();
        }
        internal override UniTask Pass()
        {
            CustomDebug.LogModule($"'Pass' PhaseState({Enum<TEnum>.GetName(PassagePhase)})", "LocationLevel");

            return base.Pass();
        }
    }
}