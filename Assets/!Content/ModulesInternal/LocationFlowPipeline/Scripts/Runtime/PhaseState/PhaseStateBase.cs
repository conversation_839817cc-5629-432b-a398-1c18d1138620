namespace Content.LocationFlowPipeline.PhaseState
{
    using Cysharp.Threading.Tasks;


    public abstract class PhaseStateBase
    {
        public abstract int PassagePhaseNumber { get; }


        internal virtual UniTask Enter()
        {
            return UniTask.CompletedTask;
        }
        internal virtual UniTask Exit()
        {
            return UniTask.CompletedTask;
        }
        internal virtual UniTask Pass()
        {
            return UniTask.CompletedTask;
        }
    }
}