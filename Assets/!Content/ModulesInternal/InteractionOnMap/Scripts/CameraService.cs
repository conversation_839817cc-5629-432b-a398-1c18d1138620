namespace Content.InteractionOnMap
{
    using Zenject;
    using UnityEngine;
    using Modules.ColorSystem;
    using Sirenix.OdinInspector;
    using Modules.Shared.Utils.Component.Camera;


    [RequireComponent(typeof(Camera), typeof(CameraColorChanger), typeof(HorizontalCamera))]
    public sealed class CameraService : SerializedMonoBehaviour, IInitializable
    {
        public Camera Camera { get; private set; }
        public CameraColorChanger CameraColorChanger { get; private set; }
        public HorizontalCamera HorizontalCamera { get; private set; }


        public void Initialize()
        {
            Camera = GetComponent<Camera>();
            CameraColorChanger = GetComponent<CameraColorChanger>();
            HorizontalCamera = GetComponent<HorizontalCamera>();
        }
    }
}