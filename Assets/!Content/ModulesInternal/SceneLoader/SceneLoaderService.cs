namespace Content.SceneLoader
{
    using System;
    using Zenject;
    using UnityEngine;
    using PlatformServices;
    using UnityEngine.Pool;
    using ModulesInternal.Tags;
    using Modules.Shared.Helpers;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.EntryPoint;
    using Modules.Shared.InputSystem;
    using UnityEngine.SceneManagement;
    using Content.LocationFlowPipeline;


    public class SceneLoaderService
    {
        public const int SCENE_INDEX_MAIN_MENU = 1;
        public const int SCENE_INDEX_CLASSIC = 2;
        private const int MAIN_MENU_ID = -1;

        private InputService _inputService;


        [Inject]
        public void Construct(InputService inputService)
        {
            _inputService = inputService;
        }
        public void OpenMainMenu(Action onShowFade = null, Color? fadeColor = null)
        {
            LoadSceneMainMenu(onShowFade, fadeColor).Forget();
        }

        public UniTask OpenMainMenuAsync(Action onShowFade = null,  Color? fadeColor = null)
        {
            return LoadSceneMainMenu(onShowFade, fadeColor);
        }

        private async UniTask LoadSceneMainMenu( Action onShowFade = null,  Color? fadeColor = null)
        {
            await UniTask.SwitchToMainThread();
            SetUserAnalytics(MAIN_MENU_ID, ModeType.None);

            _inputService.InputAccessor.SetActive(this, false);

            // _uiService.Overlap.Show(color: fadeColor);
            // await UniTask.Delay((int)(_uiService.Overlap.DefaultOverlapSettings.Duration * 1000));

            onShowFade?.Invoke();

            await SceneManager.LoadSceneAsync(SCENE_INDEX_MAIN_MENU);
            await ExecuteEntryPointScene(SceneManager.GetSceneByBuildIndex(SCENE_INDEX_MAIN_MENU));

            // _uiService.Overlap.Hide(color: fadeColor);
            _inputService.InputAccessor.SetActive(this, true);
        }

        public async UniTask OpenClassicAsync()
        {
            SetUserAnalytics(-1, ModeType.Classic);

            _inputService.InputAccessor.SetActive(this, false);

            // var key = locationIndex.GetLocationKey();

/*             var completionShowLoadScreen = AutoResetUniTaskCompletionSource.Create();
            var screenLoad = await _uiService.ShowAsync<LoadScreen>(_ => completionShowLoadScreen.TrySetResult());
            screenLoad.Loader.BindPieceAggregate(_loadLaunchLocation);

            screenLoad.ShowProgressRing();

            await completionShowLoadScreen.Task; */

            // onShowFade?.Invoke();

            await SceneManager.LoadSceneAsync(SCENE_INDEX_CLASSIC);
            await ExecuteEntryPointScene(SceneManager.GetSceneByBuildIndex(SCENE_INDEX_CLASSIC));

            // _uiService.Hide<LoadScreen>();

            _inputService.InputAccessor.SetActive(this, true);
        }

        private static void SetUserAnalytics(int currentLevel, ModeType mode)
        {
            Mediator.Analytics.SetUserProperty(Tags.Analytics.User.CURRENT_LEVEL, currentLevel);
            Mediator.Analytics.SetUserProperty(Tags.Analytics.User.MODE, Enum<ModeType>.GetName(mode));
        }

        private async UniTask ExecuteEntryPointScene(Scene scene)
        {
            var roots = ListPool<GameObject>.Get();

            scene.GetRootGameObjects(roots);

            if (roots.Count == 0)
                throw new($"Scene '{scene.name}' does not contain any root objects");

            EntryPointBase entryPoint = null;

            foreach ( var root in roots )
            {
                if ( root.TryGetComponent(out entryPoint) )
                    break;
            }

            if (entryPoint == null)
                throw new($"Scene '{scene.name}' does not contain EntryPoint in root objects");

            ListPool<GameObject>.Release(roots);

            await entryPoint.Execute();
        }
    }
}