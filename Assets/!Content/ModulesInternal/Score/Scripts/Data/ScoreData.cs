namespace Content.ModulesInternal.Score.Data
{
    using Bayat.Json;
    using Modules.Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public class ScoreData : DataStorage
    {
        [JsonProperty] private int _goldScore;
        [JsonProperty] private int _silverScore;
        [JsonProperty] private int _bronzeScore;


        [JsonIgnore]
        public int GoldScore => _goldScore;

        [JsonIgnore]
        public int SilverScore => _silverScore;

        [JsonIgnore]
        public int BronzeScore => _bronzeScore;

        public bool IsClear => _goldScore == 0 && _silverScore == 0 && _bronzeScore == 0;


        public bool TryChangeScore(int score, ScoreTier scoreTier)
        {
            switch ( scoreTier )
            {
                case ScoreTier.Gold:
                    if ( score > _goldScore )
                    {
                        _goldScore = score;
                        SetDirty();
                        return true;
                    }
                    break;
                case ScoreTier.Silver:
                    if ( score > _silverScore )
                    {
                        _silverScore = score;
                        SetDirty();
                        return true;
                    }
                    break;
                case ScoreTier.Bronze:
                    if ( score > _bronzeScore )
                    {
                        _bronzeScore = score;
                        SetDirty();
                        return true;
                    }
                    break;
            }

            return false;
        }
    }
}