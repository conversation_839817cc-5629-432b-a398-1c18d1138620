namespace Content.ModulesInternal.Score
{
    using Tags;
    using Data;
    using System;
    using Zenject;
    using PlatformServices;
    using Modules.Shared.Focus;
    using Modules.Shared.GlobalServices;
    using Modules.Shared.GlobalServices.Saver;


    public enum ScoreTier
    {
        None,
        Bronze,
        Silver,
        Gold
    }

    public sealed class ScoreService : IInitializable
    {
        private ScoreData _scoreData;

        public ScoreData ScoreData => _scoreData;


        public void Initialize()
        {
            _scoreData = StorageService.Get<ScoreData>();
        }

        public void SendScoreToLeaderboard()
        {
            var score = GetBestScore();
            SendForceToLeaderboard(score);
        }

        public int GetBestScore(ScoreTier scoreTier = ScoreTier.Gold)
        {
            return scoreTier switch
            {
                ScoreTier.Gold => _scoreData.GoldScore,
                ScoreTier.Silver => _scoreData.SilverScore,
                ScoreTier.Bronze => _scoreData.BronzeScore,
                _ => 0
            };
        }

        public ScoreTier GetScoreTierByScore(int score)
        {
            if ( score > _scoreData.GoldScore )
                return ScoreTier.Gold;

            if ( score > _scoreData.SilverScore )
                return ScoreTier.Silver;

            if ( score > _scoreData.BronzeScore )
                return ScoreTier.Bronze;

            return ScoreTier.None;
        }

        public void SetScore(int score)
        {
            var scoreTier = GetScoreTierByScore(score);

            if ( scoreTier == ScoreTier.None )
                return;

            if ( _scoreData.TryChangeScore(score, scoreTier) && scoreTier == ScoreTier.Gold )
                SendForceToLeaderboard(score);
        }

        private void SendForceToLeaderboard(int score)
        {
            CustomDebug.LogModule("SEND SCORE", nameof(ScoreService));

            Mediator.Analytics.LogEvent(Tags.AnalyticsBake.Event.POST_SCORE, Tags.AnalyticsBake.Parameter.SCORE, score);

            Mediator.Leaderboard.SubmitScore(score);

#if UNITY_WEBGL //Need GP_Player.Sync();
            StorageService.Save(true);
#else
            StorageService.Save();
#endif
        }
    }
}