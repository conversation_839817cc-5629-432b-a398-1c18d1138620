namespace Modules.TilePuzzle.Editor
{
    using UnityEngine;


    public static class ShapeMatrixUtils
    {
        public const int DIMENSION = 5;

        public static bool[,] BitToMatrix(int bits, Vector2Int size)
        {
            bool[,] matrix = new bool[DIMENSION, DIMENSION];

            // Вычисляем смещение, чтобы разместить меньшую матрицу в центре 5x5 матрицы
            int offsetX = (DIMENSION - size.x) / 2;
            int offsetY = (DIMENSION - size.y) / 2;

            // Заполняем матрицу значениями из битов
            for (int y = 0; y < size.y; y++)
            {
                for (int x = 0; x < size.x; x++)
                {
                    int index = y * size.x + x;
                    bool bitValue = (bits & (1 << index)) != 0;
                    matrix[offsetY + y, offsetX + x] = bitValue;
                }
            }

            return matrix;
        }

        public static int MatrixToBit(bool[,] matrix, out Vector2Int size)
        {
            if (matrix == null)
            {
                Debug.LogError("Pattern.MatrixToBit: Matrix is null!");
                size = new Vector2Int(0, 0);
                return 0;
            }

            int minX = matrix.GetLength(1);
            int minY = matrix.GetLength(0);
            int maxX = 0;
            int maxY = 0;

            // Определяем границы заполненных ячеек
            for (int y = 0; y < matrix.GetLength(0); y++)
            {
                for (int x = 0; x < matrix.GetLength(1); x++)
                {
                    if (matrix[y, x])
                    {
                        minX = Mathf.Min(minX, x);
                        minY = Mathf.Min(minY, y);
                        maxX = Mathf.Max(maxX, x);
                        maxY = Mathf.Max(maxY, y);
                    }
                }
            }

            // Вычисляем размер
            size = new Vector2Int((maxX - minX + 1), (maxY - minY + 1));

            int bits = 0;

            // Преобразуем заполненные ячейки в биты
            for (int y = minY; y <= maxY; y++)
            {
                for (int x = minX; x <= maxX; x++)
                {
                    if (matrix[y, x])
                    {
                        int index = (y - minY) * size.x + (x - minX);
                        bits |= (1 << index);
                    }
                }
            }

            return bits;
        }
    }
}