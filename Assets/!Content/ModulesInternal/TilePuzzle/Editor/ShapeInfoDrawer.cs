namespace Modules.TilePuzzle.Editor
{
    using System;
    using UnityEditor;
    using UnityEngine;
    using Sirenix.Utilities;
    using Sirenix.Utilities.Editor;
    using Structure.Entities.Settings;
    using Sirenix.OdinInspector.Editor;


    public class ShapeInfoDrawer : OdinValueDrawer<ShapeInfo>
    {
        private const float CELL_SIZE = 15f;
        private const float PADDING = 5f;


        protected override void DrawPropertyLayout(GUIContent label)
        {
            // Get the EditorWindow
            EditorWindow window = EditorWindow.focusedWindow;

            if (window != null)
            {
                window.wantsMouseMove = true;
                window.Repaint();
            }

            // Get ShapeEditor object
            var shapeEditor = ValueEntry.SmartValue;

            // Get bit representation and size
            int shapeBit = shapeEditor.Bitmap;

            // Calculate preview size
            float previewWidth = shapeEditor.Size.x * CELL_SIZE + PADDING * 2;
            float previewHeight = shapeEditor.Size.y * CELL_SIZE + PADDING * 2;

            // Create a horizontal layout group
            EditorGUILayout.BeginHorizontal();

            // Display bitmap and binary representation
            EditorGUILayout.LabelField($"{shapeEditor.Bitmap} : {Convert.ToString(shapeEditor.Bitmap, 2).PadLeft(shapeEditor.Size.x * shapeEditor.Size.y, '0')}");

            // Display Count property with plus and minus buttons
            EditorGUILayout.LabelField("Count:", GUILayout.Width(50));
            EditorGUILayout.LabelField(shapeEditor.Count.ToString(), GUILayout.Width(30));

            EditorGUI.BeginChangeCheck();

            // Сначала кнопка минус (красного цвета)
            Color originalColor = GUI.backgroundColor;

            // Затем кнопка плюс (зеленого цвета)
            GUI.backgroundColor = new Color(0.3f, 0.9f, 0.3f); // Зеленый цвет для плюса
            if (GUILayout.Button("+", GUILayout.Width(25)))
            {
                // Теперь мы можем напрямую изменить свойство объекта
                ShapeInfo modifiedShape = ValueEntry.SmartValue;
                modifiedShape.Count += 1;
                ValueEntry.SmartValue = modifiedShape;
            }

            GUI.backgroundColor = new Color(0.9f, 0.3f, 0.3f); // Красный цвет для минуса
            if (GUILayout.Button("-", GUILayout.Width(25)))
            {
                // Теперь мы можем напрямую изменить свойство объекта
                ShapeInfo modifiedShape = ValueEntry.SmartValue;
                modifiedShape.Count = Mathf.Max(0, modifiedShape.Count - 1);
                ValueEntry.SmartValue = modifiedShape;
            }

            // Восстанавливаем оригинальный цвет
            GUI.backgroundColor = originalColor;

            if (EditorGUI.EndChangeCheck())
            {
                // Это уведомляет Odin, что значение было изменено
                GUI.changed = true;

                // Пометим родительский объект как "грязный" для сохранения
                UnityEngine.Object targetObject = null;

                // В Odin Inspector мы можем найти родителя через свойство
                if (Property.Parent != null && Property.Parent.ValueEntry != null)
                {
                    targetObject = Property.Parent.ValueEntry.WeakSmartValue as UnityEngine.Object;
                }

                // Если не нашли родителя, попробуем найти через путь свойства
                if (targetObject == null && Property.Tree != null && Property.Tree.UnitySerializedObject != null)
                {
                    targetObject = Property.Tree.UnitySerializedObject.targetObject;
                }

                if (targetObject != null)
                {
                    EditorUtility.SetDirty(targetObject);
                }
            }

            EditorGUILayout.EndHorizontal();

            // Create rectangle for drawing preview
            Rect previewRect = EditorGUILayout.GetControlRect(false, previewHeight);
            previewRect.width = previewWidth;

            // Reduce the drawing area
            Rect paddedPreviewRect = previewRect.Padding(PADDING);

            // Draw background for preview
            SirenixEditorGUI.DrawSolidRect(paddedPreviewRect, new Color(0.2f, 0.2f, 0.2f, 0.3f));

            // Draw each cell
            for (int y = 0; y < shapeEditor.Size.y; y++)
            {
                for (int x = 0; x < shapeEditor.Size.x; x++)
                {
                    // Calculate cell position
                    Rect cellRect = new Rect(
                        paddedPreviewRect.x + x * CELL_SIZE,
                        paddedPreviewRect.y + (shapeEditor.Size.y - y - 1) * CELL_SIZE,
                        CELL_SIZE,
                        CELL_SIZE
                    );

                    // Padding for visual cell separation
                    Rect paddedCellRect = cellRect.Padding(1);


                    int index = y * shapeEditor.Size.x + x;
                    bool bitValue = (shapeBit & (1 << index)) != 0;

                    // Cell color depends on value
                    Color cellColor = bitValue
                        ? new Color(0.1f, 0.8f, 0.2f)
                        : new Color(0.3f, 0.3f, 0.3f, 0.1f);

                    // Draw cell
                    SirenixEditorGUI.DrawSolidRect(paddedCellRect, cellColor);
                }
            }

            // Highlight on hover
            if (previewRect.Contains(Event.current.mousePosition))
            {
                Color highlightColor = new Color(0.5f, 0.5f, 0.5f, 0.2f);
                EditorGUI.DrawRect(previewRect, highlightColor);
            }

            // Check if preview was clicked
            if (GUI.Button(previewRect, GUIContent.none, GUIStyle.none))
            {
                ShapeMatrixEditorWindow.ShowWindow(shapeEditor);
            }
        }
    }
}