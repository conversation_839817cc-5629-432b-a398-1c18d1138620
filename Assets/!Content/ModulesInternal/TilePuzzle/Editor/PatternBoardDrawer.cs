namespace Modules.TilePuzzle.Editor
{
    using UnityEngine;
    using UnityEditor;
    using System.Linq;
    using Modules.ColorSystem;
    using Sirenix.Utilities.Editor;
    using System.Collections.Generic;
    using Sirenix.OdinInspector.Editor;
    using Modules.TilePuzzle.Structure.Entities;
    using Modules.TilePuzzle.Structure.Services.Settings;


    public class PatternBoardDrawer : OdinValueDrawer<PatternBoardInitializerSettings>
    {
        private const float CELL_SIZE = 25f;
        private const float PADDING = 5f; // Padding around the entire grid
        private const float CELL_SPACING = 1f; // Spacing between cells
        private const int BOARD_DIMENSION = 8;

        private int _selectedCellIndex = -1;
        private UnitType _currentEditUnitType = UnitType.Default; // Default value
        private int _currentEditTypeColorIndex = -1; // Index for _availableTypeColors, -1 means none selected or no colors available

        private ColorSettingsBase _colorSettings;
        private List<string> _availableTypeColors = new List<string>();
        private string[] _availableTypeColorsArray = System.Array.Empty<string>(); // For EditorGUILayout.Popup

        // Helper to manually pad a Rect
        private Rect PadRect(Rect rect, float padding)
        {
            return new Rect(rect.x + padding, rect.y + padding, rect.width - padding * 2, rect.height - padding * 2);
        }

        protected override void DrawPropertyLayout(GUIContent label)
        {
            // Draw other properties of PatternBoardInitializerSettings first using the default drawer behavior.
            // Pass null to avoid drawing the main label for the whole group again if it's already handled by the box later.
            // If you want a specific label for the default fields, you can provide one.
            this.CallNextDrawer(null);

            PatternBoardInitializerSettings settings = this.ValueEntry.SmartValue;
            var targetObject = this.Property.Tree.UnitySerializedObject?.targetObject;

            // Load ColorSettingsBase once
            if (_colorSettings == null)
            {
                _colorSettings = ColorSettingsBase.Get();
                if (_colorSettings != null && _colorSettings.TypeColors != null)
                {
                    _availableTypeColors = _colorSettings.TypeColors.ToList();
                    _availableTypeColorsArray = _availableTypeColors.ToArray();
                }
            }

            if (_colorSettings == null)
            {
                SirenixEditorGUI.ErrorMessageBox("ColorSettingsBase not found. Please ensure it exists and is configured.");
                return;
            }

            // It's okay if TypeColors is empty, the grid will just show default cells and no color selection.
            // A more specific warning is shown near the color dropdown if needed.

            // Use a different label for the box to avoid conflict if CallNextDrawer used the original label.
            SirenixEditorGUI.BeginBox(new GUIContent("Board Pattern Editor"));

            float gridTotalDisplaySize = BOARD_DIMENSION * CELL_SIZE + (BOARD_DIMENSION -1) * CELL_SPACING;
            float controlHeight = gridTotalDisplaySize + PADDING * 2;
            Rect controlRect = EditorGUILayout.GetControlRect(false, controlHeight);

            Rect gridArea = PadRect(new Rect(controlRect.x, controlRect.y, controlRect.width, gridTotalDisplaySize + PADDING*2), PADDING);
            if (controlRect.width > gridTotalDisplaySize)
            {
                gridArea.x = controlRect.x + (controlRect.width - (gridTotalDisplaySize + PADDING *2)) / 2;
            }
            gridArea.width = gridTotalDisplaySize + PADDING *2;

            SirenixEditorGUI.DrawSolidRect(PadRect(gridArea, -PADDING), new Color(0.2f, 0.2f, 0.2f, 0.3f)); // Grid background

            Event currentEvent = Event.current;

            if (GUILayout.Button("DEBUG: Clear UnitMap"))
            {
                if (EditorUtility.DisplayDialog("Clear UnitMap",
                    "Are you sure you want to clear all rules from the UnitMap? This cannot be undone.",
                    "Yes, Clear It", "Cancel"))
                {
                    settings.UnitMap.Clear();
                    EditorUtility.SetDirty(targetObject); // targetObject - это ваш ScriptableObject
                    GUI.changed = true; // Чтобы инспектор перерисовался
                }
            }

            for (int y = 0; y < BOARD_DIMENSION; y++)
            {
                for (int x = 0; x < BOARD_DIMENSION; x++)
                {
                    int cellBitOffset = y * BOARD_DIMENSION + x;
                    Rect cellRect = new Rect(
                        gridArea.x + PADDING + x * (CELL_SIZE + CELL_SPACING),
                        gridArea.y + PADDING + (BOARD_DIMENSION - 1 - y) * (CELL_SIZE + CELL_SPACING), // Invert Y
                        CELL_SIZE,
                        CELL_SIZE
                    );
                    Rect cellContentRect = cellRect;

                    int existingRuleIndex = settings.UnitMap.FindIndex(r => r.bitOffset == cellBitOffset);
                    Color cellDisplayColor = new Color(0.3f, 0.3f, 0.3f, 0.1f); // Default color for an empty/unassigned cell
                    bool hasRule = existingRuleIndex != -1;

                    if (hasRule)
                    {
                        PatternBoardInitializerSettings.UnitRule rule = settings.UnitMap[existingRuleIndex];
                        if (_availableTypeColors.Count > 0 && rule.indexColorPattern >= 0 && rule.indexColorPattern < _availableTypeColors.Count)
                        {
                            string typeColorString = _availableTypeColors[rule.indexColorPattern];
                            cellDisplayColor = _colorSettings.GetStarterColorByType(typeColorString);
                        }
                        else
                        {
                            cellDisplayColor = Color.magenta; // Error - rule exists but invalid indexColorPattern
                        }
                    }

                    SirenixEditorGUI.DrawSolidRect(cellContentRect, cellDisplayColor);

                    Color borderColor = Color.gray;
                    float borderWidth = 1f;
                    if (_selectedCellIndex == cellBitOffset)
                    {
                        borderColor = Color.yellow;
                        borderWidth = 2f;
                        EditorGUI.DrawRect(PadRect(cellContentRect, CELL_SPACING / 2), new Color(1f, 0.92f, 0.016f, 0.20f));
                    }

                    EditorGUI.DrawRect(new Rect(cellRect.x, cellRect.y, cellRect.width, borderWidth), borderColor);
                    EditorGUI.DrawRect(new Rect(cellRect.x, cellRect.yMax - borderWidth, cellRect.width, borderWidth), borderColor);
                    EditorGUI.DrawRect(new Rect(cellRect.x, cellRect.y + borderWidth, borderWidth, cellRect.height - (2 * borderWidth)), borderColor);
                    EditorGUI.DrawRect(new Rect(cellRect.xMax - borderWidth, cellRect.y + borderWidth, borderWidth, cellRect.height - (2 * borderWidth)), borderColor);

                    if (currentEvent.type == EventType.MouseDown && cellRect.Contains(currentEvent.mousePosition))
                    {
                        HandleCellClick(clickedCellIndex: cellBitOffset, settings: settings);
                        currentEvent.Use();
                        GUI.changed = true;
                    }
                }
            }

            if (_selectedCellIndex != -1)
            {
                EditorGUILayout.Space();
                SirenixEditorGUI.BeginBox($"Editing Cell (Bit Offset: {_selectedCellIndex})");

                EditorGUI.BeginChangeCheck();
                _currentEditUnitType = (UnitType)EditorGUILayout.EnumPopup("Unit Type", _currentEditUnitType);

                if (_availableTypeColors.Count > 0)
                {
                    _currentEditTypeColorIndex = EditorGUILayout.Popup("Color Type", _currentEditTypeColorIndex, _availableTypeColorsArray);
                }
                else
                {
                    EditorGUILayout.HelpBox("No available color types in ColorSettingsBase. Add colors to enable selection.", MessageType.Warning);
                }

                if (EditorGUI.EndChangeCheck())
                {
                     if (targetObject != null) EditorUtility.SetDirty(targetObject);
                }

                EditorGUILayout.Space();
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();

                bool canApply = _availableTypeColors.Count > 0 && _currentEditTypeColorIndex >= 0 && _currentEditTypeColorIndex < _availableTypeColors.Count;

                EditorGUI.BeginDisabledGroup(!canApply);
                if (GUILayout.Button("Apply", GUILayout.Width(100), GUILayout.Height(25)))
                {
                    ApplyChangesToCell(cellBitOffset: _selectedCellIndex, settings: settings, targetToDirty: targetObject);
                    GUI.changed = true;
                }
                EditorGUI.EndDisabledGroup();

                if (GUILayout.Button("Delete", GUILayout.Width(100), GUILayout.Height(25)))
                {
                    DeleteRuleForCell(cellBitOffset: _selectedCellIndex, settings: settings, targetToDirty: targetObject);
                    GUI.changed = true;
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                SirenixEditorGUI.EndBox();
            }
            SirenixEditorGUI.EndBox();
        }

        private void HandleCellClick(int clickedCellIndex, PatternBoardInitializerSettings settings)
        {
            // If a different cell is clicked, we don't auto-apply changes.
            // We just load the data for the newly clicked cell.
            // Any unapplied changes in the editor fields for the previous cell are effectively discarded.

            _selectedCellIndex = clickedCellIndex;

            int existingRuleIndex = settings.UnitMap.FindIndex(r => r.bitOffset == _selectedCellIndex);
            if (existingRuleIndex != -1)
            {
                PatternBoardInitializerSettings.UnitRule rule = settings.UnitMap[existingRuleIndex];
                _currentEditUnitType = rule.unitType;
                if (_availableTypeColors.Count > 0 && rule.indexColorPattern >= 0 && rule.indexColorPattern < _availableTypeColors.Count)
                {
                    _currentEditTypeColorIndex = rule.indexColorPattern;
                }
                else
                {
                    // Rule exists but has an invalid color index or no colors are available to map it to.
                    // Default to the first available color in dropdown, or -1 if none.
                    _currentEditTypeColorIndex = _availableTypeColors.Count > 0 ? 0 : -1;
                }
            }
            else // No existing rule for this cell, set editor fields to defaults.
            {
                _currentEditUnitType = UnitType.Default;
                _currentEditTypeColorIndex = _availableTypeColors.Count > 0 ? 0 : -1; // Default to first color in dropdown, or -1 if none.
            }
        }

        private void ApplyChangesToCell(int cellBitOffset, PatternBoardInitializerSettings settings, Object targetToDirty)
        {
            if (cellBitOffset == -1 || _colorSettings == null)
            {
                return;
            }

            if (_availableTypeColors.Count == 0 || _currentEditTypeColorIndex < 0 || _currentEditTypeColorIndex >= _availableTypeColors.Count)
            {
                 if (_availableTypeColors.Count > 0)
                    Debug.LogWarning($"PatternBoardDrawer: Valid Color Type not selected for cell {cellBitOffset}. Changes not applied.");
                return;
            }

            int colorIndex = _currentEditTypeColorIndex;

            int existingRuleIndex = settings.UnitMap.FindIndex(r => r.bitOffset == cellBitOffset);

            var newRule = new PatternBoardInitializerSettings.UnitRule
            {
                bitOffset = cellBitOffset,
                unitType = _currentEditUnitType,
                indexColorPattern = colorIndex
            };

            if (existingRuleIndex != -1)
            {
                settings.UnitMap[existingRuleIndex] = newRule;
            }
            else
            {
                settings.UnitMap.Add(newRule);
            }

            if (targetToDirty != null) EditorUtility.SetDirty(targetToDirty);
        }

        private void DeleteRuleForCell(int cellBitOffset, PatternBoardInitializerSettings settings, Object targetToDirty)
        {
            if (cellBitOffset == -1) return;

            int removedCount = settings.UnitMap.RemoveAll(r => r.bitOffset == cellBitOffset);

            if (removedCount > 0 && targetToDirty != null)
            {
                EditorUtility.SetDirty(targetToDirty);
                // After deleting, reset the editor fields to reflect the now-empty state of the selected cell.
                _currentEditUnitType = UnitType.Default;
                _currentEditTypeColorIndex = _availableTypeColors.Count > 0 ? 0 : -1;
            }
        }
    }
}