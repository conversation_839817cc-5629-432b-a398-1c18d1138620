namespace Modules.TilePuzzle.Editor
{
    using UnityEditor;
    using UnityEngine;
    using Sirenix.Utilities;
    using Debug = UnityEngine.Debug;
    using Structure.Entities.Settings;


    // Separate editor window for editing shape matrix
    public class ShapeMatrixEditorWindow : EditorWindow
    {
        private ShapeInfo _targetShape;
        private bool[,] _editMatrix;
        private float _cellSize = 30f;
        private Vector2 _scrollPosition;

        // Show window and initialize with target shape
        public static void ShowWindow(ShapeInfo target)
        {
            var window = GetWindow<ShapeMatrixEditorWindow>("Shape Editor");
            window.InitializeWithTarget(target);
            window.minSize = new Vector2(300, 350);
            window.Show();
        }

        // Initialize editor with target shape
        private void InitializeWithTarget(ShapeInfo target)
        {
            _targetShape = target;
            _editMatrix = ShapeMatrixUtils.BitToMatrix(target.Bitmap, target.Size);

            if (_editMatrix == null)
            {
                Debug.LogError("ShapeMatrixEditorWindow: Failed to create edit matrix!");
                _editMatrix = new bool[ShapeMatrixUtils.DIMENSION, ShapeMatrixUtils.DIMENSION]; // Создаем пустую матрицу
            }
        }

        // Draw editor GUI
        private void OnGUI()
        {
            if (_editMatrix == null)
            {
                EditorGUILayout.HelpBox("No shape to edit.", MessageType.Warning);
                return;
            }

            EditorGUILayout.LabelField("Shape Editor", EditorStyles.boldLabel);

            // Инструкции по использованию
            EditorGUILayout.HelpBox("Click cells to toggle their state. Green cells are active, gray are inactive.", MessageType.Info);

            EditorGUILayout.Space(10);

            // Calculate total matrix size
            float matrixSize = ShapeMatrixUtils.DIMENSION * _cellSize;
            float padding = 20f; // Добавляем отступ вокруг матрицы

            // Begin scroll view if needed
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

            // Создаем фиксированную область для матрицы с отступами
            GUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Центрирование по горизонтали

            // Создаем область с учетом отступов
            Rect totalArea = GUILayoutUtility.GetRect(matrixSize + padding * 2, matrixSize + padding * 2);

            // Рисуем фон для всей области
            EditorGUI.DrawRect(totalArea, new Color(0.2f, 0.2f, 0.2f, 0.1f));

            // Создаем область для самой матрицы
            Rect matrixArea = new Rect(
                totalArea.x + padding,
                totalArea.y + padding,
                matrixSize,
                matrixSize
            );

            // Рисуем фон для матрицы
            EditorGUI.DrawRect(matrixArea, new Color(0.25f, 0.25f, 0.25f, 0.2f));

            // Рисуем рамку вокруг матрицы
            GUI.Box(matrixArea, GUIContent.none);

            // Draw each cell
            for (int y = 0; y < ShapeMatrixUtils.DIMENSION; y++)
            {
                for (int x = 0; x < ShapeMatrixUtils.DIMENSION; x++)
                {
                    Rect cellRect = new Rect(
                        matrixArea.x + x * _cellSize,
                        matrixArea.y + (ShapeMatrixUtils.DIMENSION - y - 1) * _cellSize,
                        _cellSize,
                        _cellSize
                    );

                    Rect paddedCellRect = cellRect.Padding(2f);

                    // Handle mouse click
                    if (Event.current.type == EventType.MouseDown && cellRect.Contains(Event.current.mousePosition))
                    {
                        _editMatrix[y, x] = !_editMatrix[y, x];
                        GUI.changed = true;
                        Event.current.Use();
                        Repaint();
                    }

                    // Draw cell background
                    Color cellColor = _editMatrix[y, x]
                        ? new Color(0.1f, 0.8f, 0.2f)
                        : new Color(0.3f, 0.3f, 0.3f, 0.3f);

                    EditorGUI.DrawRect(paddedCellRect, cellColor);

                    // Рисуем рамку вокруг ячейки
                    GUI.Box(cellRect, GUIContent.none);
                }
            }

            GUILayout.FlexibleSpace(); // Центрирование по горизонтали
            GUILayout.EndHorizontal();

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space(15);

            // Add buttons for actions
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Apply", GUILayout.Width(100)))
            {
                ApplyChanges();
                Close();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Cancel", GUILayout.Width(100)))
                Close();

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
        }

        // Apply changes to target shape
        private void ApplyChanges()
        {
            if (_editMatrix != null)
            {
                int newShapeBit = ShapeMatrixUtils.MatrixToBit(_editMatrix, out var size);
                var bitmapProperty = typeof(ShapeInfo).GetProperty("Bitmap", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Public);
                var sizeProperty = typeof(ShapeInfo).GetProperty("Size", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Public);
                bitmapProperty.SetValue(_targetShape, newShapeBit);
                sizeProperty.SetValue(_targetShape, size);

                // Mark Unity objects as dirty to ensure changes are saved
                if (Selection.activeObject != null)
                    EditorUtility.SetDirty(Selection.activeObject);

                // Force inspector to repaint
                // InspectorWindow.RepaintAllInspectors();
                AssetDatabase.SaveAssets();
                // Обновляем все окна Unity
                UnityEditorInternal.InternalEditorUtility.RepaintAllViews();
            }
        }
    }
}