namespace Modules.TilePuzzle.Editor
{
    using UnityEditor;
    using UnityEngine;
    using Sirenix.Utilities;
    using Sirenix.Utilities.Editor;
    using Structure.Entities.Settings;
    using Sirenix.OdinInspector.Editor;


    [CustomEditor(typeof(PatternPreset))]
    public class PatternEditor : OdinEditor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            // Get Pattern object
            var pattern = target as PatternPreset;

            if (pattern == null)
                return;

            // Add small spacing
            EditorGUILayout.Space(5);

            // Header for preview
            SirenixEditorGUI.Title("Shape Preview", "", TextAlignment.Left, true);

            // Get all shapes for this pattern
            var shapes = pattern.Shapes;

            if (shapes == null || shapes.Count == 0)
            {
                EditorGUILayout.HelpBox("This pattern has no shapes. Add shapes to see the preview.", MessageType.Info);
                return;
            }

            // Constants for display
            const float cellSize = 12f;
            const float padding = 5f;
            const float outlineThickness = 1f;
            Color outlineColor = new Color(0.000f, 0.000f, 0.000f, 0.236f);

            // Maximum number of previews in one row
            float totalHeight = 0;
            int maxPreviewsPerRow = Mathf.FloorToInt((EditorGUIUtility.currentViewWidth - 50) / (5 * cellSize + padding * 2));
            maxPreviewsPerRow = Mathf.Max(1, maxPreviewsPerRow);
            int rows = Mathf.CeilToInt((float)shapes.Count / maxPreviewsPerRow);

            // Calculate max height
            for (int i = 0; i < shapes.Count; i++)
            {
                totalHeight = Mathf.Max(totalHeight, shapes[i].Size.y * cellSize + padding * 2);
            }

            // Create rectangle for all previews
            Rect totalRect = EditorGUILayout.GetControlRect(false, rows * totalHeight + 10);

            // Draw each preview
            float currentXOffset = 0;
            for (int i = 0; i < shapes.Count; i++)
            {
                int row = i / maxPreviewsPerRow;
                int col = i % maxPreviewsPerRow;
                var shape = shapes[i];
                var size = shape.Size;

                // Calculate preview position
                float previewWidth = size.x * cellSize + padding * 2;
                float previewHeight = size.y * cellSize + padding * 2;

                // Calculate Y offset for vertical centering
                float yOffset = (totalHeight - previewHeight) / 2;

                Rect previewRect = new Rect(
                    totalRect.x + currentXOffset,
                    totalRect.y + row * totalHeight + yOffset,
                    previewWidth,
                    previewHeight
                );

                // Update currentXOffset for the next preview
                currentXOffset += previewWidth;

                // If we're at the end of the row, reset the X offset
                if (col == maxPreviewsPerRow - 1)
                {
                    currentXOffset = 0;
                }

                // Padding for visual preview separation
                Rect paddedPreviewRect = previewRect.Padding(padding);

                // Draw outline for preview
                Rect outlineRect = paddedPreviewRect.Padding(-outlineThickness);
                SirenixEditorGUI.DrawSolidRect(outlineRect, outlineColor);

                // Draw background for preview
                SirenixEditorGUI.DrawSolidRect(paddedPreviewRect, new Color(0.2f, 0.2f, 0.2f, 0.1f));

                // Draw each cell
                for (int y = 0; y < size.y; y++)
                {
                    for (int x = 0; x < size.x; x++)
                    {
                        // Calculate cell position
                        Rect cellRect = new Rect(
                            paddedPreviewRect.x + x * cellSize,
                            paddedPreviewRect.y + (size.y - y - 1) * cellSize,
                            cellSize,
                            cellSize
                        );

                        // Padding for visual cell separation
                        Rect paddedCellRect = cellRect.Padding(1f);

                        int index = y * size.x + x;
                        bool bitValue = (shape.Bitmap & (1 << index)) != 0;

                        // Cell color depends on value
                        Color cellColor = bitValue
                            ? new Color(0.1f, 0.8f, 0.2f)
                            : new Color(0.3f, 0.3f, 0.3f, 0.1f);

                        // Draw cell
                        SirenixEditorGUI.DrawSolidRect(paddedCellRect, cellColor);
                    }
                }
            }
        }
    }
}
