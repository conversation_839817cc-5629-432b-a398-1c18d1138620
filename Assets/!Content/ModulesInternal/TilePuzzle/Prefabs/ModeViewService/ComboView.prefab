%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1731135432751889212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 760769384853720212}
  - component: {fileID: 2722775213828665215}
  m_Layer: 5
  m_Name: ComboAlert
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &760769384853720212
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1731135432751889212}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3384740554415044636}
  - {fileID: 4916825975865852699}
  - {fileID: 384220006804100301}
  m_Father: {fileID: 2093683279620398238}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 756.3}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2722775213828665215
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1731135432751889212}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bc4d90b8d7bdd4d30a8efc14293ade1f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 2722775213828665215}
    - {fileID: 0}
    - {fileID: 7754457388958740579}
    - {fileID: 0}
    - {fileID: 3166976764807265113}
    - {fileID: 0}
    - {fileID: 2100000, guid: 8f59593ba7b2a4f6c87aea0fd0bead93, type: 2}
    - {fileID: 760769384853720212}
    - {fileID: 8414402792246054634}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    - {fileID: 0}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: _enumConfig
      Entry: 7
      Data: 0|Modules.Shared.Config.Enum.EnumConfig`2[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _collection
      Entry: 7
      Data: 1|Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]][],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 5
    - Name: 
      Entry: 7
      Data: Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _keyEnum
      Entry: 3
      Data: 10
    - Name: _value
      Entry: 7
      Data: 2|Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer, Modules.Shared.Runtime
    - Name: _cycles
      Entry: 3
      Data: 1
    - Name: _cycleMode
      Entry: 3
      Data: 0
    - Name: _tweens
      Entry: 7
      Data: 3|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TweenBase[],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 7
    - Name: 
      Entry: 7
      Data: 4|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.CallbackTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Event Start
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _onComplete
      Entry: 7
      Data: 5|UnityEngine.Events.UnityEvent, UnityEngine.CoreModule
    - Name: m_PersistentCalls
      Entry: 7
      Data: 6|UnityEngine.Events.PersistentCallGroup, UnityEngine.CoreModule
    - Name: m_Calls
      Entry: 7
      Data: 7|System.Collections.Generic.List`1[[UnityEngine.Events.PersistentCall,
        UnityEngine.CoreModule]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 8|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 0
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: Modules.TilePuzzle.Structure.Services.ComboAlertPlayer, Assembly-CSharp
    - Name: m_MethodName
      Entry: 1
      Data: ResetPropertyMaterial
    - Name: m_Mode
      Entry: 3
      Data: 5
    - Name: m_Arguments
      Entry: 7
      Data: 9|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 1
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: _NoiseStrength
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 10|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 2
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: UnityEngine.ParticleSystem, UnityEngine
    - Name: m_MethodName
      Entry: 1
      Data: Play
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_Arguments
      Entry: 7
      Data: 11|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 3
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: 
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 12|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.CallbackTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Enable Combo
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _onComplete
      Entry: 7
      Data: 13|UnityEngine.Events.UnityEvent, UnityEngine.CoreModule
    - Name: m_PersistentCalls
      Entry: 7
      Data: 14|UnityEngine.Events.PersistentCallGroup, UnityEngine.CoreModule
    - Name: m_Calls
      Entry: 7
      Data: 15|System.Collections.Generic.List`1[[UnityEngine.Events.PersistentCall,
        UnityEngine.CoreModule]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 16|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 4
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Renderer, UnityEngine
    - Name: m_MethodName
      Entry: 1
      Data: set_enabled
    - Name: m_Mode
      Entry: 3
      Data: 6
    - Name: m_Arguments
      Entry: 7
      Data: 17|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 5
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: 
    - Name: m_BoolArgument
      Entry: 5
      Data: true
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 18|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart SCALE Climax
    - Name: _sequenceType
      Entry: 3
      Data: 1
    - Name: _time
      Entry: 4
      Data: 0.4
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.5
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 19|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 20|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.25638354
    - Name: m_OutTangent
      Entry: 4
      Data: 1.25638354
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.2918354
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.376114935
    - Name: m_Value
      Entry: 4
      Data: 0.6547584
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.848892331
    - Name: m_Value
      Entry: 4
      Data: 0.842825055
    - Name: m_InTangent
      Entry: 4
      Data: 0.5584
    - Name: m_OutTangent
      Entry: 4
      Data: 0.5584
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 21|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 22|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.207246646
    - Name: m_OutTangent
      Entry: 4
      Data: 0.207246646
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.3555554
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.457925528
    - Name: m_Value
      Entry: 4
      Data: 1.109028
    - Name: m_InTangent
      Entry: 4
      Data: -0.0350272655
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0350272655
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.750124931
    - Name: m_Value
      Entry: 4
      Data: 0.8620983
    - Name: m_InTangent
      Entry: 4
      Data: -0.2085082
    - Name: m_OutTangent
      Entry: 4
      Data: -0.2085082
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.22938
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_OutTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.7345324
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 1
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 1
    - Name: _endValue
      Entry: 4
      Data: 0
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 23|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart COLOR Climax
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.8
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 24|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 25|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 26|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 27|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.0499922037
    - Name: m_OutTangent
      Entry: 4
      Data: 0.0499922037
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.173996255
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5660298
    - Name: m_Value
      Entry: 4
      Data: 0.849814653
    - Name: m_InTangent
      Entry: 4
      Data: 1.34078753
    - Name: m_OutTangent
      Entry: 4
      Data: 1.34078753
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.110210843
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_OutTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.7345324
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 1
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 118.035355
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 6.92767239
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 5.21647453
    - Name: 
      Entry: 4
      Data: 0.0538416952
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: true
    - Name: _material
      Entry: 10
      Data: 6
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 28|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TransformScaleTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Scale
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.2
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 29|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 30|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.1870591
    - Name: m_Value
      Entry: 4
      Data: 0.109702758
    - Name: m_InTangent
      Entry: 4
      Data: -0.00096866634
    - Name: m_OutTangent
      Entry: 4
      Data: -0.00096866634
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.4275264
    - Name: m_Value
      Entry: 4
      Data: -0.112739086
    - Name: m_InTangent
      Entry: 4
      Data: 0.189647347
    - Name: m_OutTangent
      Entry: 4
      Data: 0.189647347
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.184996337
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.00388029241
    - Name: m_OutTangent
      Entry: 4
      Data: 0.00388029241
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.127735138
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 31|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 32|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 5
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.18772155
    - Name: m_Value
      Entry: 4
      Data: 0.107408464
    - Name: m_InTangent
      Entry: 4
      Data: -0.006210368
    - Name: m_OutTangent
      Entry: 4
      Data: -0.006210368
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.147337854
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.329818666
    - Name: m_Value
      Entry: 4
      Data: -0.123595633
    - Name: m_InTangent
      Entry: 4
      Data: -0.8370741
    - Name: m_OutTangent
      Entry: 4
      Data: -0.8370741
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.18431
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.574895263
    - Name: m_Value
      Entry: 4
      Data: -0.0351414457
    - Name: m_InTangent
      Entry: 4
      Data: 1.015513
    - Name: m_OutTangent
      Entry: 4
      Data: 1.015513
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.6282655
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.35677126
    - Name: m_OutTangent
      Entry: 4
      Data: 0.35677126
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.401488781
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _transform
      Entry: 10
      Data: 7
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 33|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Rind
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0.2
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.9
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 34|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 35|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.233058646
    - Name: m_Value
      Entry: 4
      Data: 0.742207646
    - Name: m_InTangent
      Entry: 4
      Data: -0.31817165
    - Name: m_OutTangent
      Entry: 4
      Data: -0.31817165
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.05769572
    - Name: m_OutTangent
      Entry: 4
      Data: -0.05769572
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.223327249
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 36|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 37|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.0105796605
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0105796605
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333423823
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.224121779
    - Name: m_Value
      Entry: 4
      Data: 0.6892223
    - Name: m_InTangent
      Entry: 4
      Data: 0.0166706033
    - Name: m_OutTangent
      Entry: 4
      Data: 0.0166706033
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.158530533
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5549913
    - Name: m_Value
      Entry: 4
      Data: 0.4601664
    - Name: m_InTangent
      Entry: 4
      Data: -1.28561234
    - Name: m_OutTangent
      Entry: 4
      Data: -1.28561234
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.374162585
    - Name: m_OutWeight
      Entry: 4
      Data: 0.07983886
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.628498852
    - Name: m_OutTangent
      Entry: 4
      Data: -0.628498852
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.277006954
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: -1
    - Name: _endValue
      Entry: 4
      Data: 0.9
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Ring
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 38|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Outline COLOR
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0.1
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 1
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 39|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 40|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 2.76878142
    - Name: m_OutTangent
      Entry: 4
      Data: 2.76878142
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.07341155
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.1918954
    - Name: m_Value
      Entry: 4
      Data: 0.613008738
    - Name: m_InTangent
      Entry: 4
      Data: -0.130021125
    - Name: m_OutTangent
      Entry: 4
      Data: -0.130021125
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.111937121
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -2.2815187
    - Name: m_OutTangent
      Entry: 4
      Data: -2.2815187
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.08725381
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 41|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 42|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 8.395409
    - Name: m_OutTangent
      Entry: 4
      Data: 8.395409
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.14863196
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.226852566
    - Name: m_Value
      Entry: 4
      Data: 0.991058648
    - Name: m_InTangent
      Entry: 4
      Data: 0.05524049
    - Name: m_OutTangent
      Entry: 4
      Data: 0.05524049
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.171507791
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -4.05036974
    - Name: m_OutTangent
      Entry: 4
      Data: -4.05036974
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.0398693681
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.99632025
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.6845208
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.441591
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.5824467
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: true
    - Name: _material
      Entry: 10
      Data: 6
    - Name: _propertyName
      Entry: 1
      Data: _ColorOutline
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _ColorOutline
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _keyEnum
      Entry: 3
      Data: 20
    - Name: _value
      Entry: 7
      Data: 43|Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer, Modules.Shared.Runtime
    - Name: _cycles
      Entry: 3
      Data: 1
    - Name: _cycleMode
      Entry: 3
      Data: 0
    - Name: _tweens
      Entry: 7
      Data: 44|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TweenBase[],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 7
    - Name: 
      Entry: 7
      Data: 45|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.CallbackTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Event Start
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _onComplete
      Entry: 7
      Data: 46|UnityEngine.Events.UnityEvent, UnityEngine.CoreModule
    - Name: m_PersistentCalls
      Entry: 7
      Data: 47|UnityEngine.Events.PersistentCallGroup, UnityEngine.CoreModule
    - Name: m_Calls
      Entry: 7
      Data: 48|System.Collections.Generic.List`1[[UnityEngine.Events.PersistentCall,
        UnityEngine.CoreModule]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 49|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 8
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: UnityEngine.ParticleSystem, UnityEngine
    - Name: m_MethodName
      Entry: 1
      Data: Play
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_Arguments
      Entry: 7
      Data: 50|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 9
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: 
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 51|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart Scale
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.3
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 52|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 53|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 54|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 55|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.06332731
    - Name: m_OutTangent
      Entry: 4
      Data: 1.06332731
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.145021588
    - Name: m_Value
      Entry: 4
      Data: 0.138601631
    - Name: m_InTangent
      Entry: 4
      Data: 0.79731077
    - Name: m_OutTangent
      Entry: 4
      Data: 0.79731077
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.6012232
    - Name: m_Value
      Entry: 4
      Data: -0.3128162
    - Name: m_InTangent
      Entry: 4
      Data: -0.12072774
    - Name: m_OutTangent
      Entry: 4
      Data: -0.12072774
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.193607286
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: -0.2
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 56|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Pop
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0.3
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.3
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 57|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 9
      Data: 53
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 58|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 59|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 34
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 34
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: 1
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Pop
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 60|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Color Outline
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.2
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 61|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 9
      Data: 53
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 62|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 63|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 2
    - Name: m_OutTangent
      Entry: 4
      Data: 2
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.99632025
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.6845208
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 25.6889381
    - Name: 
      Entry: 4
      Data: 0.5083377
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _ColorOutline
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 64|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Outline Width
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.2
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 65|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 9
      Data: 53
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 66|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 67|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 2
    - Name: m_OutTangent
      Entry: 4
      Data: 2
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0.005
    - Name: _endValue
      Entry: 4
      Data: 0.025
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: true
    - Name: _material
      Entry: 10
      Data: 6
    - Name: _propertyName
      Entry: 1
      Data: _OutlineWidth
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _OutlineWidth
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 68|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TransformScaleTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Scale
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.3
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 69|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 9
      Data: 53
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 70|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 71|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.8519774
    - Name: m_Value
      Entry: 4
      Data: 1.24771738
    - Name: m_InTangent
      Entry: 4
      Data: -0.07299357
    - Name: m_OutTangent
      Entry: 4
      Data: -0.07299357
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.23438099
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 1
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _transform
      Entry: 10
      Data: 7
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 72|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.CallbackTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Disable Combo
    - Name: _sequenceType
      Entry: 3
      Data: 1
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _onComplete
      Entry: 7
      Data: 73|UnityEngine.Events.UnityEvent, UnityEngine.CoreModule
    - Name: m_PersistentCalls
      Entry: 7
      Data: 74|UnityEngine.Events.PersistentCallGroup, UnityEngine.CoreModule
    - Name: m_Calls
      Entry: 7
      Data: 75|System.Collections.Generic.List`1[[UnityEngine.Events.PersistentCall,
        UnityEngine.CoreModule]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 76|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 4
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Renderer, UnityEngine
    - Name: m_MethodName
      Entry: 1
      Data: set_enabled
    - Name: m_Mode
      Entry: 3
      Data: 6
    - Name: m_Arguments
      Entry: 7
      Data: 77|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 10
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: 
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _keyEnum
      Entry: 3
      Data: 30
    - Name: _value
      Entry: 7
      Data: 78|Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer, Modules.Shared.Runtime
    - Name: _cycles
      Entry: 3
      Data: 1
    - Name: _cycleMode
      Entry: 3
      Data: 0
    - Name: _tweens
      Entry: 7
      Data: 79|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TweenBase[],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 8
    - Name: 
      Entry: 7
      Data: 80|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.CallbackTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Event Start
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _onComplete
      Entry: 7
      Data: 81|UnityEngine.Events.UnityEvent, UnityEngine.CoreModule
    - Name: m_PersistentCalls
      Entry: 7
      Data: 82|UnityEngine.Events.PersistentCallGroup, UnityEngine.CoreModule
    - Name: m_Calls
      Entry: 7
      Data: 83|System.Collections.Generic.List`1[[UnityEngine.Events.PersistentCall,
        UnityEngine.CoreModule]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 84|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 0
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: Modules.TilePuzzle.Structure.Services.ComboAlertPlayer, Assembly-CSharp
    - Name: m_MethodName
      Entry: 1
      Data: ResetPropertyMaterial
    - Name: m_Mode
      Entry: 3
      Data: 5
    - Name: m_Arguments
      Entry: 7
      Data: 85|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 11
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: _NoiseStrength
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 86|UnityEngine.Events.PersistentCall, UnityEngine.CoreModule
    - Name: m_Target
      Entry: 10
      Data: 2
    - Name: m_TargetAssemblyTypeName
      Entry: 1
      Data: UnityEngine.ParticleSystem, UnityEngine
    - Name: m_MethodName
      Entry: 1
      Data: Play
    - Name: m_Mode
      Entry: 3
      Data: 1
    - Name: m_Arguments
      Entry: 7
      Data: 87|UnityEngine.Events.ArgumentCache, UnityEngine.CoreModule
    - Name: m_ObjectArgument
      Entry: 10
      Data: 12
    - Name: m_ObjectArgumentAssemblyTypeName
      Entry: 1
      Data: UnityEngine.Object, UnityEngine
    - Name: m_IntArgument
      Entry: 3
      Data: 0
    - Name: m_FloatArgument
      Entry: 4
      Data: 0
    - Name: m_StringArgument
      Entry: 1
      Data: 
    - Name: m_BoolArgument
      Entry: 5
      Data: false
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CallState
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 88|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart SCALE Start
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.05
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 89|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 90|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.8422701
    - Name: m_OutTangent
      Entry: 4
      Data: 0.8422701
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.08994584
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0.229816437
    - Name: m_InTangent
      Entry: 4
      Data: -0.048160553
    - Name: m_OutTangent
      Entry: 4
      Data: -0.048160553
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.09641677
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 91|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 92|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.04161143
    - Name: m_OutTangent
      Entry: 4
      Data: 1.04161143
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.09461636
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.08161322
    - Name: m_OutTangent
      Entry: 4
      Data: 0.08161322
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.1120919
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: 0.3
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 93|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart COLOR Start
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.1
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 94|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 95|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0.4947281
    - Name: m_InTangent
      Entry: 4
      Data: 0.4947281
    - Name: m_OutTangent
      Entry: 4
      Data: 0.4947281
    - Name: m_TangentMode
      Entry: 3
      Data: 34
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 96|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 97|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.04161143
    - Name: m_OutTangent
      Entry: 4
      Data: 1.04161143
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.09461636
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.08161322
    - Name: m_OutTangent
      Entry: 4
      Data: 0.08161322
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.1120919
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.95334983
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.3090647
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 118.035355
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 6.92767239
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 98|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart SCALE Climax
    - Name: _sequenceType
      Entry: 3
      Data: 1
    - Name: _time
      Entry: 4
      Data: 0.4
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.4
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 99|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 100|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.25638354
    - Name: m_OutTangent
      Entry: 4
      Data: 1.25638354
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.2918354
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.239187881
    - Name: m_Value
      Entry: 4
      Data: 0.641422749
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.848892331
    - Name: m_Value
      Entry: 4
      Data: 0.842825055
    - Name: m_InTangent
      Entry: 4
      Data: 0.5584
    - Name: m_OutTangent
      Entry: 4
      Data: 0.5584
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 101|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 102|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 4.196124
    - Name: m_OutTangent
      Entry: 4
      Data: 4.196124
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.13371
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.190279022
    - Name: m_Value
      Entry: 4
      Data: 1.48124385
    - Name: m_InTangent
      Entry: 4
      Data: -0.0350272655
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0350272655
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.8499174
    - Name: m_Value
      Entry: 4
      Data: 0.6725994
    - Name: m_InTangent
      Entry: 4
      Data: -0.2085082
    - Name: m_OutTangent
      Entry: 4
      Data: -0.2085082
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.22938
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_OutTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.7345324
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: 0
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 103|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart COLOR Climax
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.4
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 104|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 105|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 1
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 1
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 106|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 107|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.0499922037
    - Name: m_OutTangent
      Entry: 4
      Data: 0.0499922037
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.173996255
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5660298
    - Name: m_Value
      Entry: 4
      Data: 0.849814653
    - Name: m_InTangent
      Entry: 4
      Data: 1.34078753
    - Name: m_OutTangent
      Entry: 4
      Data: 1.34078753
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.110210843
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_OutTangent
      Entry: 4
      Data: 0.010279594
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.7345324
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 1
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 74.80353
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 4.419525
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 5.21647453
    - Name: 
      Entry: 4
      Data: 0.0538416952
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: true
    - Name: _material
      Entry: 10
      Data: 6
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 108|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Rind
    - Name: _sequenceType
      Entry: 3
      Data: 2
    - Name: _time
      Entry: 4
      Data: 0.1
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.6
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 109|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 110|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.233058646
    - Name: m_Value
      Entry: 4
      Data: 0.742207646
    - Name: m_InTangent
      Entry: 4
      Data: -0.31817165
    - Name: m_OutTangent
      Entry: 4
      Data: -0.31817165
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.05769572
    - Name: m_OutTangent
      Entry: 4
      Data: -0.05769572
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.223327249
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 111|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 112|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.0105796605
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0105796605
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333423823
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.2316536
    - Name: m_Value
      Entry: 4
      Data: 1.00414383
    - Name: m_InTangent
      Entry: 4
      Data: 0.0166706033
    - Name: m_OutTangent
      Entry: 4
      Data: 0.0166706033
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.158530533
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5625231
    - Name: m_Value
      Entry: 4
      Data: 0.775087953
    - Name: m_InTangent
      Entry: 4
      Data: -1.25532591
    - Name: m_OutTangent
      Entry: 4
      Data: -1.25532591
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.07983886
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.628498852
    - Name: m_OutTangent
      Entry: 4
      Data: -0.628498852
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.277006954
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: -1
    - Name: _endValue
      Entry: 4
      Data: 0.9
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Ring
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 113|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TransformScaleTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Scale
    - Name: _sequenceType
      Entry: 3
      Data: 2
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.4
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 114|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 115|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.14595978
    - Name: m_Value
      Entry: 4
      Data: 0.1078761
    - Name: m_InTangent
      Entry: 4
      Data: -0.00096866634
    - Name: m_OutTangent
      Entry: 4
      Data: -0.00096866634
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.4275264
    - Name: m_Value
      Entry: 4
      Data: -0.112739086
    - Name: m_InTangent
      Entry: 4
      Data: 0.189647347
    - Name: m_OutTangent
      Entry: 4
      Data: 0.189647347
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.184996337
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.00388029241
    - Name: m_OutTangent
      Entry: 4
      Data: 0.00388029241
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.127735138
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 116|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 117|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 5
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0
    - Name: m_OutTangent
      Entry: 4
      Data: 0
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.162674472
    - Name: m_Value
      Entry: 4
      Data: 0.217505023
    - Name: m_InTangent
      Entry: 4
      Data: -0.006210368
    - Name: m_OutTangent
      Entry: 4
      Data: -0.006210368
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.147337854
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.3312785
    - Name: m_Value
      Entry: 4
      Data: -0.22740829
    - Name: m_InTangent
      Entry: 4
      Data: -0.8370741
    - Name: m_OutTangent
      Entry: 4
      Data: -0.8370741
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.18431
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5763551
    - Name: m_Value
      Entry: 4
      Data: -0.1389541
    - Name: m_InTangent
      Entry: 4
      Data: 1.015513
    - Name: m_OutTangent
      Entry: 4
      Data: 1.015513
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.6282655
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 0.35677126
    - Name: m_OutTangent
      Entry: 4
      Data: 0.35677126
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.401488781
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _transform
      Entry: 10
      Data: 7
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 118|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Outline COLOR
    - Name: _sequenceType
      Entry: 3
      Data: 2
    - Name: _time
      Entry: 4
      Data: 0.1
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: false
    - Name: _duration
      Entry: 4
      Data: 0.5
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 119|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 120|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 2.76878142
    - Name: m_OutTangent
      Entry: 4
      Data: 2.76878142
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.07341155
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.1918954
    - Name: m_Value
      Entry: 4
      Data: 0.613008738
    - Name: m_InTangent
      Entry: 4
      Data: -0.130021125
    - Name: m_OutTangent
      Entry: 4
      Data: -0.130021125
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.111937121
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -2.2815187
    - Name: m_OutTangent
      Entry: 4
      Data: -2.2815187
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.08725381
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 121|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 122|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 8.395409
    - Name: m_OutTangent
      Entry: 4
      Data: 8.395409
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.14863196
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.226852566
    - Name: m_Value
      Entry: 4
      Data: 0.991058648
    - Name: m_InTangent
      Entry: 4
      Data: 0.05524049
    - Name: m_OutTangent
      Entry: 4
      Data: 0.05524049
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.171507791
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -4.05036974
    - Name: m_OutTangent
      Entry: 4
      Data: -4.05036974
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.0398693681
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.99632025
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.6845208
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.441591
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.5824467
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _ColorOutline
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _keyEnum
      Entry: 3
      Data: 40
    - Name: _value
      Entry: 7
      Data: 123|Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer, Modules.Shared.Runtime
    - Name: _cycles
      Entry: 3
      Data: 1
    - Name: _cycleMode
      Entry: 3
      Data: 0
    - Name: _tweens
      Entry: 7
      Data: 124|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TweenBase[],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 125|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart Scale
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 1
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.3
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 126|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 127|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.11532664
    - Name: m_OutTangent
      Entry: 4
      Data: 1.11532664
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.102397248
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.7483159
    - Name: m_Value
      Entry: 4
      Data: 0.355512857
    - Name: m_InTangent
      Entry: 4
      Data: -0.5738078
    - Name: m_OutTangent
      Entry: 4
      Data: -0.5738078
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.321205854
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.08105491
    - Name: m_OutTangent
      Entry: 4
      Data: -0.08105491
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.41639024
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 128|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 129|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.98926091
    - Name: m_OutTangent
      Entry: 4
      Data: 1.98926091
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.09621747
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.727028
    - Name: m_Value
      Entry: 4
      Data: 1.18022513
    - Name: m_InTangent
      Entry: 4
      Data: -0.57180953
    - Name: m_OutTangent
      Entry: 4
      Data: -0.57180953
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.289581358
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: -0.00661357539
    - Name: m_OutTangent
      Entry: 4
      Data: -0.00661357539
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.3478213
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: -0.1
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 130|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Noise Strength
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 1
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.3
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 131|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 132|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.11532664
    - Name: m_OutTangent
      Entry: 4
      Data: 1.11532664
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.102397248
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.7483159
    - Name: m_Value
      Entry: 4
      Data: 0.355512857
    - Name: m_InTangent
      Entry: 4
      Data: -0.5738078
    - Name: m_OutTangent
      Entry: 4
      Data: -0.5738078
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.321205854
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.08105491
    - Name: m_OutTangent
      Entry: 4
      Data: -0.08105491
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.41639024
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 133|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 134|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.98926091
    - Name: m_OutTangent
      Entry: 4
      Data: 1.98926091
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.09621747
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.727028
    - Name: m_Value
      Entry: 4
      Data: 1.18022513
    - Name: m_InTangent
      Entry: 4
      Data: -0.57180953
    - Name: m_OutTangent
      Entry: 4
      Data: -0.57180953
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.289581358
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 1
    - Name: m_InTangent
      Entry: 4
      Data: -0.00661357539
    - Name: m_OutTangent
      Entry: 4
      Data: -0.00661357539
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.3478213
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 3
      Data: 8
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: 0.04
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _NoiseStrength
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: Modules.Shared.Config.Enum.EnumConfig`2+EnumValue[[Modules.TilePuzzle.Structure.Services.ComboAlertType,
        Assembly-CSharp],[Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer,
        Modules.Shared.Runtime]], Modules.Shared.Runtime
    - Name: _keyEnum
      Entry: 3
      Data: 50
    - Name: _value
      Entry: 7
      Data: 135|Modules.Shared.CustomPlugin.PrimeTweenPlayer.SequencePlayer, Modules.Shared.Runtime
    - Name: _cycles
      Entry: 3
      Data: -1
    - Name: _cycleMode
      Entry: 3
      Data: 0
    - Name: _tweens
      Entry: 7
      Data: 136|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.TweenBase[],
        Modules.Shared.Runtime
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 137|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialFloatTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart Impulse
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 1
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.4
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 138|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 139|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.04747677
    - Name: m_OutTangent
      Entry: 4
      Data: 1.04747677
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.617107332
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.197081909
    - Name: m_Value
      Entry: 4
      Data: 0.1335736
    - Name: m_InTangent
      Entry: 4
      Data: -0.03808984
    - Name: m_OutTangent
      Entry: 4
      Data: -0.03808984
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.6976619
    - Name: m_Value
      Entry: 4
      Data: -0.279281437
    - Name: m_InTangent
      Entry: 4
      Data: -0.173583224
    - Name: m_OutTangent
      Entry: 4
      Data: -0.173583224
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.32400316
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 1.31870353
    - Name: m_OutTangent
      Entry: 4
      Data: 1.31870353
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.477659047
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 140|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 141|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 3.17460442
    - Name: m_OutTangent
      Entry: 4
      Data: 3.17460442
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.345958263
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.190891415
    - Name: m_Value
      Entry: 4
      Data: 0.485725164
    - Name: m_InTangent
      Entry: 4
      Data: -0.07211781
    - Name: m_OutTangent
      Entry: 4
      Data: -0.07211781
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.171457544
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.713032365
    - Name: m_Value
      Entry: 4
      Data: -1.00673938
    - Name: m_InTangent
      Entry: 4
      Data: -0.0314362533
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0314362533
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.29541716
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: 2.02280045
    - Name: m_OutTangent
      Entry: 4
      Data: 2.02280045
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.3432948
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 0
    - Name: _customCompleteValue
      Entry: 4
      Data: 0
    - Name: _startValue
      Entry: 4
      Data: 0
    - Name: _endValue
      Entry: 4
      Data: 0.015
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialFloatBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Heart
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 142|Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.RendererMaterialColorTween,
        Modules.Shared.Runtime
    - Name: _isActive
      Entry: 5
      Data: true
    - Name: _name
      Entry: 1
      Data: Heart Color
    - Name: _sequenceType
      Entry: 3
      Data: 0
    - Name: _time
      Entry: 4
      Data: 0
    - Name: _blendingMode
      Entry: 3
      Data: 0
    - Name: _isStartValue
      Entry: 5
      Data: true
    - Name: _duration
      Entry: 4
      Data: 0.4
    - Name: _minMaxCurve
      Entry: 7
      Data: UnityEngine.ParticleSystem+MinMaxCurve, UnityEngine.ParticleSystemModule
    - Name: m_Mode
      Entry: 3
      Data: 2
    - Name: m_CurveMultiplier
      Entry: 4
      Data: 1
    - Name: m_CurveMin
      Entry: 7
      Data: 143|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 144|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -0.7697568
    - Name: m_OutTangent
      Entry: 4
      Data: -0.7697568
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.793834746
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.127039909
    - Name: m_Value
      Entry: 4
      Data: -0.0424086042
    - Name: m_InTangent
      Entry: 4
      Data: 0.246726424
    - Name: m_OutTangent
      Entry: 4
      Data: 0.246726424
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.2051678
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.5908863
    - Name: m_Value
      Entry: 4
      Data: 0.45225516
    - Name: m_InTangent
      Entry: 4
      Data: 0.0006257647
    - Name: m_OutTangent
      Entry: 4
      Data: 0.0006257647
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.333333343
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -1.28771436
    - Name: m_OutTangent
      Entry: 4
      Data: -1.28771436
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.192690223
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: m_CurveMax
      Entry: 7
      Data: 145|UnityEngine.AnimationCurve, UnityEngine.CoreModule
    - Name: 
      Entry: 7
      Data: 146|UnityEngine.Keyframe[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -1.83916509
    - Name: m_OutTangent
      Entry: 4
      Data: -1.83916509
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0
    - Name: m_OutWeight
      Entry: 4
      Data: 0.730449438
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.120597094
    - Name: m_Value
      Entry: 4
      Data: -0.133257
    - Name: m_InTangent
      Entry: 4
      Data: -0.07211781
    - Name: m_OutTangent
      Entry: 4
      Data: -0.07211781
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.171457544
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 0.551121354
    - Name: m_Value
      Entry: 4
      Data: 0.9905635
    - Name: m_InTangent
      Entry: 4
      Data: -0.0314362533
    - Name: m_OutTangent
      Entry: 4
      Data: -0.0314362533
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.333333343
    - Name: m_OutWeight
      Entry: 4
      Data: 0.29541716
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: UnityEngine.Keyframe, UnityEngine.CoreModule
    - Name: ver
      Entry: 3
      Data: 1
    - Name: m_Time
      Entry: 4
      Data: 1
    - Name: m_Value
      Entry: 4
      Data: 0
    - Name: m_InTangent
      Entry: 4
      Data: -2.70811963
    - Name: m_OutTangent
      Entry: 4
      Data: -2.70811963
    - Name: m_TangentMode
      Entry: 3
      Data: 0
    - Name: m_WeightedMode
      Entry: 3
      Data: 0
    - Name: m_InWeight
      Entry: 4
      Data: 0.7006963
    - Name: m_OutWeight
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: m_ConstantMin
      Entry: 4
      Data: 0
    - Name: m_ConstantMax
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _completeValueType
      Entry: 3
      Data: 2
    - Name: _customCompleteValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: _startValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 3.95334983
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.3090647
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _endValue
      Entry: 7
      Data: UnityEngine.Color, UnityEngine.CoreModule
    - Name: 
      Entry: 4
      Data: 9.734287
    - Name: 
      Entry: 4
      Data: 0
    - Name: 
      Entry: 4
      Data: 0.7610064
    - Name: 
      Entry: 4
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: _startMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: true
    - Name: _material
      Entry: 10
      Data: 6
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: 
      Entry: 8
      Data: 
    - Name: _endMaterialReference
      Entry: 7
      Data: Modules.Shared.CustomPlugin.PrimeTweenPlayer.TypeTween.MaterialColorBase+MaterialReference,
        Modules.Shared.Runtime
    - Name: _enabled
      Entry: 5
      Data: false
    - Name: _material
      Entry: 6
      Data: 
    - Name: _propertyName
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _propertyName
      Entry: 1
      Data: _Color
    - Name: _isSharedMaterial
      Entry: 5
      Data: false
    - Name: _renderer
      Entry: 10
      Data: 4
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _isLog: 0
  _renderer: {fileID: 3166976764807265113}
  _material: {fileID: 2100000, guid: 8f59593ba7b2a4f6c87aea0fd0bead93, type: 2}
--- !u!1 &3656986533487822716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8005237570586263308}
  - component: {fileID: 1715455177142055683}
  m_Layer: 5
  m_Name: TextContent
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &8005237570586263308
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656986533487822716}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8077259398494731106}
  - {fileID: 5950670791454829279}
  m_Father: {fileID: 2093683279620398238}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 1, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 324}
  m_SizeDelta: {x: 0, y: 296.12}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1715455177142055683
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656986533487822716}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b54b16f2fcdba244b49be1f398b6562, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 40
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
  paddingSizerFallback:
    screenConfigName: 
    OptimizedSize:
      left: 0
      right: 0
      top: 0
      bottom: 0
    MinSize:
      left: 0
      right: 0
      top: 0
      bottom: 0
    MaxSize:
      left: 1000
      right: 1000
      top: 1000
      bottom: 1000
    ModLeft:
      ExponentialScale: 1
      SizeModifiers:
      - Mode: 1
        Impact: 1
    ModRight:
      ExponentialScale: 1
      SizeModifiers:
      - Mode: 1
        Impact: 1
    ModTop:
      ExponentialScale: 1
      SizeModifiers:
      - Mode: 1
        Impact: 1
    ModBottom:
      ExponentialScale: 1
      SizeModifiers:
      - Mode: 1
        Impact: 1
  customPaddingSizers:
    items: []
  spacingSizerFallback:
    screenConfigName: 
    OptimizedSize: 40
    MinSize: 0
    MaxSize: 300
    Mod:
      ExponentialScale: 1
      SizeModifiers: []
  customSpacingSizers:
    items: []
  settingsFallback:
    ChildAlignment: 4
    ReverseArrangement: 0
    ChildForceExpandHeight: 0
    ChildForceExpandWidth: 0
    ChildScaleWidth: 0
    ChildScaleHeight: 0
    ChildControlWidth: 1
    ChildControlHeight: 0
    Orientation: 0
    screenConfigName: Fallback
  customSettings:
    items: []
  orientation: 0
--- !u!1 &4524993538176385827
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5950670791454829279}
  - component: {fileID: 6377029636230512473}
  - component: {fileID: 4210497903612756493}
  - component: {fileID: 4322212835378598358}
  m_Layer: 5
  m_Name: value
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5950670791454829279
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4524993538176385827}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7695689242255588916}
  m_Father: {fileID: 8005237570586263308}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 767.615, y: -148.06}
  m_SizeDelta: {x: 103.86, y: 296.12}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6377029636230512473
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4524993538176385827}
  m_CullTransparentMesh: 1
--- !u!114 &4210497903612756493
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4524993538176385827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '5

'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: d9503a962e1bd45e08281a70acdad9ea, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: c285a2c1918f3404cb87830c34f87058, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278252799
  m_fontColor: {r: 1.0622993, g: 0.95649254, b: 0, a: 1}
  m_enableVertexGradient: 1
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 0.75686276, g: 0.75686276, b: 0.75686276, a: 1}
    bottomRight: {r: 0.75686276, g: 0.75686276, b: 0.75686276, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 1
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 150.4
  m_fontSizeBase: 150.4
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 4096
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 0
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 1.64
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &4322212835378598358
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4524993538176385827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e467a9ed553646db9746068d4bab7f97, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _colorValue:
    _typeColor: Premium
    _isInvert: 0
    _invertFactor: 0
    _isShade: 0
    _shadeFactor: 0
    _isTint: 0
    _tintFactor: 0
    _isAlpha: 0
    _alpha: 0
    _isHdr: 1
    _exposure: 0.11
--- !u!1 &7288157616542920021
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8077259398494731106}
  - component: {fileID: 8710587991866971560}
  - component: {fileID: 8987541792402826558}
  - component: {fileID: 33485348603877384}
  - component: {fileID: 833382871296654394}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8077259398494731106
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288157616542920021}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.00032, y: 1.00032, z: 1.00032}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8005237570586263308}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 468.07, y: -148.06}
  m_SizeDelta: {x: 415.23, y: 296.12}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &8710587991866971560
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288157616542920021}
  m_CullTransparentMesh: 1
--- !u!114 &8987541792402826558
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288157616542920021}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 'Combo

'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: d9503a962e1bd45e08281a70acdad9ea, type: 2}
  m_sharedMaterial: {fileID: 2100000, guid: 9dec1460923ac45f2b6f8c6362dacc63, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4278255615
  m_fontColor: {r: 3.7768676, g: 3.4006855, b: 0, a: 1}
  m_enableVertexGradient: 1
  m_colorMode: 2
  m_fontColorGradient:
    topLeft: {r: 0.9245283, g: 0.9245283, b: 0.9245283, a: 1}
    topRight: {r: 0.9245283, g: 0.9245283, b: 0.9245283, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 1
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 100.9
  m_fontSizeBase: 100.9
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 4096
  m_textAlignment: 65535
  m_characterSpacing: 5.4
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 0
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 1.64
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &33485348603877384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288157616542920021}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d66a620093224ab7823a1eedfe667a5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LocalizedAssetReference:
    m_TableReference:
      m_TableCollectionName: GUID:53d7ceae41d8e4663b3b1c51edb1f1ca
    m_TableEntryReference:
      m_KeyId: 129997201408
      m_Key: 
    m_FallbackState: 0
    m_WaitForCompletion: 0
  _indexMaterial: 4
  _targetText: {fileID: 8987541792402826558}
  _isUseDefaultFont: 1
--- !u!114 &833382871296654394
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7288157616542920021}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e467a9ed553646db9746068d4bab7f97, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _colorValue:
    _typeColor: Premium
    _isInvert: 0
    _invertFactor: 0
    _isShade: 0
    _shadeFactor: 0
    _isTint: 0
    _tintFactor: 0
    _isAlpha: 0
    _alpha: 0
    _isHdr: 1
    _exposure: 1.94
--- !u!1 &8006320695559842669
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2093683279620398238}
  - component: {fileID: 1884653462637682488}
  - component: {fileID: 2054410497091646881}
  m_Layer: 5
  m_Name: ComboView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2093683279620398238
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8006320695559842669}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8005237570586263308}
  - {fileID: 760769384853720212}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1884653462637682488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8006320695559842669}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ebe6d67dcddf405c8b96b44f98c0d56, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  _duration: 0.6
  _scoreText: {fileID: 4210497903612756493}
  _animator: {fileID: 2054410497091646881}
  _content: {fileID: 3656986533487822716}
  _comboAlertPlayer: {fileID: 2722775213828665215}
--- !u!95 &2054410497091646881
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8006320695559842669}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 94f29a2b49739496abf37c9aef2b77a1, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &8345502346337127080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 384220006804100301}
  - component: {fileID: 572736964222732064}
  - component: {fileID: 3166976764807265113}
  m_Layer: 5
  m_Name: combo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &384220006804100301
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8345502346337127080}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 445.66, y: 445.66, z: 445.66}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 760769384853720212}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!33 &572736964222732064
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8345502346337127080}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3166976764807265113
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8345502346337127080}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8f59593ba7b2a4f6c87aea0fd0bead93, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1001 &2428784249974762837
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 760769384853720212}
    m_Modifications:
    - target: {fileID: 13352932286576318, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3242590527395068059, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_Name
      value: vfx_comboAlert_pop_v1
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7388783372162433097, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8014754301961292605, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9068984037958863497, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9192606363212035905, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
--- !u!4 &4916825975865852699 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7316332084948823630, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
  m_PrefabInstance: {fileID: 2428784249974762837}
  m_PrefabAsset: {fileID: 0}
--- !u!198 &8414402792246054634 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 6156755672885011391, guid: b14f7d0ef433b4be1aaa119403e835a6, type: 3}
  m_PrefabInstance: {fileID: 2428784249974762837}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4933098374632287023
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5950670791454829279}
    m_Modifications:
    - target: {fileID: 498252884670627556, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1585223483705746149, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2431916320943678579, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2476232846720414773, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalPosition.x
      value: -7.945902
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalPosition.z
      value: 268.77374
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4550076289710732121, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_Name
      value: vfx_combo_v1
      objectReference: {fileID: 0}
    - target: {fileID: 4550076289710732121, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6370118141159596283, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6428159368558301356, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6888165365452775327, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8963100105325282798, guid: 15c65260643ca4441b57516860192710, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 15c65260643ca4441b57516860192710, type: 3}
--- !u!4 &7695689242255588916 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3366852223961388315, guid: 15c65260643ca4441b57516860192710, type: 3}
  m_PrefabInstance: {fileID: 4933098374632287023}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8695366366855293567
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 760769384853720212}
    m_Modifications:
    - target: {fileID: 1587956118587030953, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3245367809457985038, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4620817021737772766, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5266708271947777131, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_Name
      value: vfx_comboAlert_combo_v1
      objectReference: {fileID: 0}
    - target: {fileID: 6035342218655265494, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7449190562688561491, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7449190562688561491, guid: 776b931b81229465e99875eb479a20ae, type: 3}
    - target: {fileID: 7449190562688561491, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: serializationData.SerializedFormat
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7481048114710874750, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8150302836937931765, guid: 776b931b81229465e99875eb479a20ae, type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 776b931b81229465e99875eb479a20ae, type: 3}
--- !u!4 &3384740554415044636 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6220920507517997667, guid: 776b931b81229465e99875eb479a20ae, type: 3}
  m_PrefabInstance: {fileID: 8695366366855293567}
  m_PrefabAsset: {fileID: 0}
--- !u!198 &7754457388958740579 stripped
ParticleSystem:
  m_CorrespondingSourceObject: {fileID: 1383018832930544156, guid: 776b931b81229465e99875eb479a20ae, type: 3}
  m_PrefabInstance: {fileID: 8695366366855293567}
  m_PrefabAsset: {fileID: 0}
