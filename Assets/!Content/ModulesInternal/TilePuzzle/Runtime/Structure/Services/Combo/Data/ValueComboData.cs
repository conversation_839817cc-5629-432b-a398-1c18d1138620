namespace Modules.TilePuzzle.Structure.Services.Combo.Data
{
    using Bayat.Json;
    using UnityEngine;


    [JsonObject(MemberSerialization.OptIn)]
    public class ValueComboData : ComboDataBase
    {
        [JsonProperty("c")] private int _combo;
        [JsonProperty("m")] private int _maxCombo;
        [JsonProperty("a")] private int _attemptLeft;

        [JsonIgnore]
        public int Combo => _combo;
        [JsonIgnore]
        public int AttemptLeft => _attemptLeft;
        [JsonIgnore]
        public int MaxCombo => _maxCombo;


        public bool TryChangeCombo(int combo)
        {
            combo = Mathf.Max(0, combo);

            if ( combo == _combo )
                return false;

            _combo = combo;
            _maxCombo = Mathf.Max(_maxCombo, combo);
            SetDirty();

            return true;
        }

        public bool TryChangeAttemptLeft(int attemptLeft)
        {
            attemptLeft = Mathf.Max(0, attemptLeft);

            if ( attemptLeft == _attemptLeft )
                return false;

            _attemptLeft = attemptLeft;
            SetDirty();

            return true;
        }

        public override void ResetData()
        {
            base.ResetData();

            _combo = 0;
            _maxCombo = 0;
            _attemptLeft = 0;

            SetDirty();
        }
    }
}
