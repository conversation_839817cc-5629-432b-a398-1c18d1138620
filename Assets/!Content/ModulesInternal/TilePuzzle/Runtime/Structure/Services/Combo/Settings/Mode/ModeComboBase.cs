namespace Modules.TilePuzzle.Structure.Services.Settings
{
    public abstract class ModeComboBase
    {
        public abstract ViewPresetBase ViewSettings { get; }
        public abstract ComboPresetBase ServiceSettings { get; }


        public virtual ComboServiceBase GetComboService() => ServiceSettings.GetComboService();

        public virtual void ViewApplySettings(ViewBase view) => ViewSettings.ApplySettings(view);
    }
}