namespace Modules.TilePuzzle.Structure.Services.Settings
{
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    public class ModeCombo : ModeComboBase
    {
        [FoldoutGroup("Service")]
        [InlineEditor]
        [OdinSerialize] public ComboPreset ComboPreset { get; private set; }

        [FoldoutGroup("View")]
        [InlineEditor]
        [OdinSerialize] public ComboViewPreset ViewPreset { get; private set; }
        [FoldoutGroup("View")]
        [OdinSerialize] public bool IsSound { get; private set; } = true;
        [FoldoutGroup("View")]
        [OdinSerialize] public bool IsComboText { get; private set; } = true;
        [FoldoutGroup("View")]
        [OdinSerialize] public bool IsScoreInformer { get; private set; } = true;


        public override ViewPresetBase ViewSettings => ViewPreset;
        public override ComboPresetBase ServiceSettings => ComboPreset;


        public override void ViewApplySettings(ViewBase view)
        {
            base.ViewApplySettings(view);

            if(view is ComboView comboView)
                comboView.ApplyModeSettings(this);
        }
    }
}