namespace Modules.TilePuzzle.Structure.Services
{
    using Zenject;
    using Services.Settings;
    using Cysharp.Threading.Tasks;
    using ModulesInternal.Bubbles;


    public abstract class AnimationBoardInitializerBase<T> : BoardInitializerBase where T : IAnimationInitializer
    {
        protected BubblesShaderChangerService _bubblesShaderChangerService;
        protected T _settings;


        public AnimationBoardInitializerBase(T settings)
        {
            _settings = settings;
        }

        [Inject]
        public void Construct(BubblesShaderChangerService bubblesShaderChangerService)
        {
            _bubblesShaderChangerService = bubblesShaderChangerService;
        }

        protected override void StartWorkInternal()
        {
            if (_settings.AnimationInitializerSettings.IsAnimation)
            {
                StartAnimation().Forget();
            }
            else
            {
                GenerateUnits();
                _onComplete();
            }
        }

        private async UniTask StartAnimation()
        {
            var isCanceled = await UniTask.Delay((int)(_settings.AnimationInitializerSettings.Delay * 1000), cancellationToken: _service.CancellationTokenRelease.Token).SuppressCancellationThrow();

            if (isCanceled)
                return;

            GenerateUnits();

            _bubblesShaderChangerService.StartAnimation(_settings.AnimationInitializerSettings.BoardAnimationBubblesSettings, () =>
            {
                _bubblesShaderChangerService.StopAnimation();
                _service.BoardService.TryClearLinesWithClimax();
                _onComplete();
            });
        }
    }
}