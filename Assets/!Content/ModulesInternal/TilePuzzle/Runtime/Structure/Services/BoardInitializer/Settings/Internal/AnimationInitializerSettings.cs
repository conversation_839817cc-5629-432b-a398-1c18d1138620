namespace Modules.TilePuzzle.Structure.Services.Settings
{
    using System;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using global::ModulesInternal.Bubbles;


    [HideLabel]
    [HideReferenceObjectPicker]
    [Serializable]
    public class AnimationInitializerSettings
    {
        [ToggleGroup("IsAnimation")]
        [OdinSerialize] public bool IsAnimation { get; private set; } = false;
        [ToggleGroup("IsAnimation")]
        [OdinSerialize] public float Delay { get; private set; }
        [ToggleGroup("IsAnimation")]
        [HideLabel]
        [OdinSerialize] public BoardAnimationBubblesSettings BoardAnimationBubblesSettings { get; private set; } = new();
    }
}