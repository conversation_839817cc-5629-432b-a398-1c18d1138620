namespace Modules.TilePuzzle.Structure.Services
{
    using Settings;
    using UnityEngine;
    using Modules.TilePuzzle.Structure.Entities;


    public class PatternBoardInitializer : AnimationBoardInitializerBase<PatternBoardInitializerSettings>
    {
        public PatternBoardInitializer(PatternBoardInitializerSettings settings) : base(settings) { }

        protected override ulong GetBitMap()
        {
            ulong bitMap = 0;
            foreach (var rule in _settings.UnitMap)
            {
                bitMap |= (1UL << rule.bitOffset);
            }
            return bitMap;
        }

        protected override void SpawnUnits()
        {
            foreach (var rule in _settings.UnitMap)
            {
                int y = rule.bitOffset / BitMapUtils.DIMENSION;
                int x = rule.bitOffset % BitMapUtils.DIMENSION;

                var cellPosition = new Vector2Int(x, y);
                var unit = _service.SpawnerService.SpawnUnit(rule.unitType);
                unit.OnSpawn(cellPosition, rule.indexColorPattern);
                unit.ElasticFollow.SetPosition(new Vector3(cellPosition.x, cellPosition.y), true);
                unit.ChangeState(UnitState.InBoard);
                _service.BoardService[cellPosition] = unit;
            }

            _service.BoardService.SyncUnitsData();

#if UNITY_EDITOR
            TestBitMap(GetBitMap());
#endif
        }
    }
}