namespace Modules.TilePuzzle.Structure.Services
{
    using Entities;
    using Settings;
    using UnityEngine;


    public class PerlinNoiseBoardInitializer : AnimationBoardInitializerBase<PerlinNoiseBoardInitializerSettings>
    {
        public PerlinNoiseBoardInitializer(PerlinNoiseBoardInitializerSettings settings) : base(settings) { }


        protected override ulong GetBitMap()
        {
            ulong board = 0;
            float offsetX = Random.Range(-1000f, 1000f);
            float offsetY = Random.Range(-1000f, 1000f);

            for (int y = 0; y < BitMapUtils.DIMENSION; y++)
            {
                for (int x = 0; x < BitMapUtils.DIMENSION; x++)
                {
                    float noise = Mathf.PerlinNoise(offsetX + x / _settings.Scale, offsetY + y / _settings.Scale);

                    if (noise > _settings.Threshold)
                    {
                        int bitIndex = y * BitMapUtils.DIMENSION + x;
                        board |= 1UL << bitIndex;
                    }
                }
            }

            return board;
        }
    }
}