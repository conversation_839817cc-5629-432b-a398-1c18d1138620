namespace Modules.TilePuzzle.Structure.Services
{
    using System;
    using Zenject;
    using UnityEngine;
    using Content.Sound;
    using Modules.Shared.Extensions;
    using Modules.TilePuzzle.Structure.Entities;


    public abstract class BoardInitializerBase
    {
        protected TilePuzzleService _service;
        protected SoundService _soundService;
        protected Action _onComplete;


        [Inject]
        public void Construct(SoundService soundService)
        {
            _soundService = soundService;
        }

        public virtual void Initialize(TilePuzzleService service)
        {
            _service = service;
            _soundService.LoadClipPack(ClipType.StartBoardAnimation);
        }

        public void StartWork(Action onComplete)
        {
            _onComplete = onComplete;
            StartWorkInternal();
        }

        protected void GenerateUnits()
        {
            if(!TryLoadUnits())
                SpawnUnits();

            _soundService.PlayOnce(ClipType.StartBoardAnimation);
        }

        private bool TryLoadUnits()
        {
            var boardData = _service.Data.BoardData;

            if (!boardData.IsInitialized)
            {
                boardData.IsInitialized = true;
                return false;
            }

            var bitMap = boardData.Bitmap;
            var dataUnits = boardData.Units;
            var indexDataUnits = 0;

            for (int y = 0; y < BitMapUtils.DIMENSION; y++)
            {
                for (int x = 0; x < BitMapUtils.DIMENSION; x++)
                {
                    int index = y * BitMapUtils.DIMENSION + x;

                    if ((bitMap & (1UL << index)) > 0)
                    {
                        var unitData = dataUnits[indexDataUnits++];
                        var unit = _service.SpawnerService.SpawnUnit(unitData.UnitType);
                        var cellPosition = new Vector2Int(x, y);

                        unit.OnSpawn(cellPosition, unitData.IndexColorPattern, unitData);
                        unit.ElasticFollow.SetPosition(new Vector3(cellPosition.x, cellPosition.y), true);
                        unit.ChangeState(UnitState.InBoard);
                        _service.BoardService[cellPosition] = unit;
                    }
                }
            }

#if UNITY_EDITOR
            TestBitMap(bitMap);
#endif

            return true;
        }

        protected virtual void SpawnUnits()
        {
            var bitMap = GetBitMap();

            for (int x = 0; x < BitMapUtils.DIMENSION; x++)
            {
                for (int y = 0; y < BitMapUtils.DIMENSION; y++)
                {
                    int index = y * BitMapUtils.DIMENSION + x;

                    if ((bitMap & (1UL << index)) > 0)
                    {
                        var unit = _service.SpawnerService.SpawnUnit();
                        var indexColorPattern = _service.TilePuzzleSettings.RangeShapeIndexColorPattern.RangeRandom();
                        var cellPosition = new Vector2Int(x, y);

                        unit.OnSpawn(cellPosition, indexColorPattern);
                        unit.ElasticFollow.SetPosition(new Vector3(cellPosition.x, cellPosition.y), true);
                        unit.ChangeState(UnitState.InBoard);
                        _service.BoardService[cellPosition] = unit;
                    }
                }
            }

            _service.BoardService.SyncUnitsData();

#if UNITY_EDITOR
            TestBitMap(bitMap);
#endif
        }

#if UNITY_EDITOR
        protected void TestBitMap(ulong bitMap)
        {
            if (bitMap != _service.BoardService.BitCell.Bitmap)
                throw new Exception("BitMap is not equal");
        }
#endif
        protected abstract void StartWorkInternal();
        protected abstract ulong GetBitMap();



        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            _soundService.ReleaseClipPack(ClipType.StartBoardAnimation);
        }

        ~BoardInitializerBase()
        {
            Dispose(false);
        }
    }
}