namespace Modules.TilePuzzle.Structure.Services.Settings
{
    using UnityEngine;
    using Sirenix.Serialization;
    using System.Collections.Generic;
    using Modules.TilePuzzle.Structure.Entities;


    public class PatternBoardInitializerSettings : BoardInitializerSettingsBase, IAnimationInitializer
    {
        public struct UnitRule
        {
            [SerializeField] public int bitOffset;
            [SerializeField] public UnitType unitType;
            [SerializeField] public int indexColorPattern;
        }

        [OdinSerialize] public List<UnitRule> UnitMap { get; private set; } = new();
        [OdinSerialize] public AnimationInitializerSettings AnimationInitializerSettings { get; private set; } = new();

        public override BoardInitializerBase GetBoardInitializer()
        {
            return new PatternBoardInitializer(this);
        }
    }
}