namespace Modules.TilePuzzle.Structure.Services.Settings
{
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    public class PerlinNoiseBoardInitializerSettings : BoardInitializerSettingsBase, IAnimationInitializer
    {
        [FoldoutGroup("Perlin Noise")]
        [OdinSerialize] public float Scale { get; private set; } = 5f;
        [FoldoutGroup("Perlin Noise")]
        [OdinSerialize] public float Threshold { get; private set; } = 0.5f;
        [OdinSerialize] public AnimationInitializerSettings AnimationInitializerSettings { get; private set; } = new();


        public override BoardInitializerBase GetBoardInitializer()
        {
            return new PerlinNoiseBoardInitializer(this);
        }
    }
}