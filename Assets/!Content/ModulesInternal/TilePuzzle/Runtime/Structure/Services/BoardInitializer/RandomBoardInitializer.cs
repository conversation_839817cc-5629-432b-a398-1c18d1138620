namespace Modules.TilePuzzle.Structure.Services
{
    using Entities;
    using Settings;
    using UnityEngine;


    public class RandomBoardInitializer : AnimationBoardInitializerBase<RandomBoardInitializerSettings>
    {
        public RandomBoardInitializer(RandomBoardInitializerSettings settings) : base(settings) { }

        protected override ulong GetBitMap()
        {
            System.Span<byte> buffer = stackalloc byte[BitMapUtils.DIMENSION];
            for (int i = 0; i < BitMapUtils.DIMENSION; i++)
            {
                buffer[i] = (byte)Random.Range(0, 256);
            }

            ulong randomValue = System.BitConverter.ToUInt64(buffer);
            return _settings.Min + (randomValue % (_settings.Max - _settings.Min + 1));
        }
    }
}