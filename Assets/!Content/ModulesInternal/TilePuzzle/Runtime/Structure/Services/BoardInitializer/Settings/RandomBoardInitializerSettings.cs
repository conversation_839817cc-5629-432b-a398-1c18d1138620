namespace Modules.TilePuzzle.Structure.Services.Settings
{
    using Sirenix.Serialization;


    public class RandomBoardInitializerSettings : BoardInitializerSettingsBase, IAnimationInitializer
    {
        [OdinSerialize] public ulong Min { get; private set; } = 500UL;
        [OdinSerialize] public ulong Max { get; private set; } = ulong.MaxValue;
        [OdinSerialize] public AnimationInitializerSettings AnimationInitializerSettings { get; private set; } = new();


        public override BoardInitializerBase GetBoardInitializer()
        {
            return new RandomBoardInitializer(this);
        }
    }
}