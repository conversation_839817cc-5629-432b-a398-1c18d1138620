namespace Modules.TilePuzzle.Structure.Entities
{
    using UnityEngine;
    using Modules.Shared.Utils.Component.Transform;


    public class UnitElasticFollow : ElasticLocalFollow
    {
        public override Vector3 Offset
        {
            get => _offset + AdditionalOffset;
            set => _offset = value;
        }

        public Vector3 AdditionalOffset { get; set; }

        public override void SetTarget(Transform target, bool updateOffset = true, bool immediately = false)
        {
            AdditionalOffset = Vector3.zero;
            base.SetTarget(target, updateOffset, immediately);
        }

        public override void SetPosition(Vector3 pos, bool immediately = false)
        {
            AdditionalOffset = Vector3.zero;
            base.SetPosition(pos, immediately);
        }
    }
}