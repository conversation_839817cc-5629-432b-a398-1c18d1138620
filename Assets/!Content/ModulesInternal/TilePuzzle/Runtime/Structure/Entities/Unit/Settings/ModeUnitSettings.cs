namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using System.Linq;
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Serialization;
    using Sirenix.OdinInspector;
    using Modules.Shared.Helpers;
    using System.Collections.Generic;
    using Modules.TilePuzzle.Structure.Services;


    [Serializable]
    [HideReferenceObjectPicker]
    public class ModeUnitSettings
    {
#if UNITY_EDITOR
        [ListDrawerSettings(DraggableItems = false, HideAddButton = true, ListElementLabelName = "NameEditor", OnTitleBarGUI = nameof(TitleBarGUI))]
        [OnValueChanged(nameof(SortUnit), true)]
#endif
        [OdinSerialize]
        private List<ModeUnitInfo> _modeUnitInfos = new();
        public IReadOnlyList<ModeUnitInfo> ModeUnitInfos => _modeUnitInfos;
        private List<ModeUnitInfo> _modeUnitCache;
        private int _lastCheckFrame = -1;
        private int _totalWeight;


#if UNITY_EDITOR
        [Button]
        public void SortUnit()
        {
            _modeUnitInfos = _modeUnitInfos.OrderByDescending(p => p.SpawnWeight).ToList();
        }

        private void TitleBarGUI()
        {
            if (Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(Sirenix.Utilities.Editor.EditorIcons.Plus))
            {
                var unitPresets = HelperEditor.LoadAllAssetOfType<UnitPresetBase>("", "t:UnitPresetBase");

                if (unitPresets.Length > 0)
                {
                    GenericMenu menu = new GenericMenu();

                    foreach (var unitPreset in unitPresets)
                    {
                        string name = unitPreset.name;
                        menu.AddItem(new GUIContent(name), false, () => {
                            var unitInfo = new ModeUnitInfo(unitPreset);
                            _modeUnitInfos.Add(unitInfo);
                            SortUnit();
                        });
                    }

                    menu.ShowAsContext();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "UnitPreset not found in project", "OK");
                }
            }
        }
#endif

        public ModeUnitInfo GetUnitInfo(UnitType unitType)
        {
            for (int i = 0; i < _modeUnitInfos.Count; i++)
            {
                if (_modeUnitInfos[i].UnitType == unitType)
                    return _modeUnitInfos[i];
            }

            return null;
        }

        public ModeUnitInfo GetUnitInfo(ScoreServiceBase score)
        {
            if (_lastCheckFrame != Time.frameCount)
            {
                _lastCheckFrame = Time.frameCount;

                _modeUnitCache ??= new(_modeUnitInfos.Count);
                _modeUnitCache.Clear();

                for (int i = 0; i < _modeUnitInfos.Count; i++)
                {
                    var unitInfo = _modeUnitInfos[i];
                    if (unitInfo.IsPass(score))
                    {
                        _modeUnitCache.Add(unitInfo);
                        _totalWeight += unitInfo.SpawnWeight;
                    }
                }
            }

            if (_modeUnitCache.Count == 1)
            {
                return _modeUnitCache[0];
            }
            else if (_modeUnitCache.Count > 0)
            {
                var randomNumber = UnityEngine.Random.Range(0, _totalWeight);
                var currentWeight = 0;

                for (int i = 0; i < _modeUnitCache.Count; i++)
                {
                    var unit = _modeUnitCache[i];

                    currentWeight += unit.SpawnWeight;

                    if (randomNumber < currentWeight)
                        return unit;
                }
            }

            return null;
        }
    }
}