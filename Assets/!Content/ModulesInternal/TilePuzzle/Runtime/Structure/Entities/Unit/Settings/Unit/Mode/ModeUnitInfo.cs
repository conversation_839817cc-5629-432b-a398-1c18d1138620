namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using Services;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    [Serializable]
    public class ModeUnitInfo
    {
        [InlineEditor]
        [OdinSerialize] public UnitPresetBase UnitPreset { get; private set; }

        [DelayedProperty]
        [OdinSerialize] public int SpawnWeight { get; private set; }

        [HideLabel]
        [OdinSerialize] private Conditions _conditions;

        public UnitType UnitType => UnitPreset.UnitPrefab.UnitType;


#if UNITY_EDITOR
        public string NameEditor => $"{UnitPreset?.name ?? "None"} : {SpawnWeight.ToString()}";

        public ModeUnitInfo(UnitPresetBase unitPreset)
        {
            UnitPreset = unitPreset;
            SpawnWeight = 1;
            _conditions = null;
        }
#endif


        internal virtual void ApplySettings(UnitBase unit)
        {
            UnitPreset.ApplySettings(unit);
        }

        public bool IsPass(ScoreServiceBase score)
        {
            return _conditions?.IsPass(score) ?? true;
        }
    }
}