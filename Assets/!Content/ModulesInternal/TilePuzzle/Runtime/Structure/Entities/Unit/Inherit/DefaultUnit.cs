namespace Modules.TilePuzzle.Structure.Entities
{
    using Zenject;
    using Unit.Data;
    using PrimeTween;
    using UnityEngine;
    using ColorSystem;
    using Content.Sound;
    using Shared.Extensions;
    using Redcode.Extensions;
    using Sirenix.OdinInspector;
    using Content.PlatformServices;
    using static Settings.DefaultUnitPreset;
    using Content.ModulesInternal.ParticlePool;
    using Shared.PlatformServices.Mediator.Vibration;


    public class DefaultUnit : UnitGeneric<DefaultUnitData>
    {
        [SerializeField] private SpriteRenderer _shadow;
        [ShowInInspector] private float _timeClimax = 0f, _powerFill = 0f, _powerOutline = 0f;
        private ParticlePoolService _particlePoolService;
        private SoundService _soundService;
        internal DefaultUnitSettings unitDefaultSettings;
        private DefaultUnitSettings.ClimaxSettings _currentClimaxSettings;
        private ColorChangerService _colorChangerService;
        private Sequence _tweenClimax;
        private Tween _tweenColor, _tweenOutline, _tweenShadow, _tweenShow;
        private Vector3 _startScaleClimax;
        private float _opacityShowShadow;

        public override UnitType UnitType => UnitType.Default;
        public override float TimeClimax => _timeClimax;
        public override float PowerFill => _powerFill;
        public override float PowerOutline => _powerOutline;


        [Inject]
        public void Construct(SoundService soundService, ParticlePoolService particlePoolService, ColorChangerService colorChangerService)
        {
            _soundService = soundService;
            _particlePoolService = particlePoolService;
            _colorChangerService = colorChangerService;
        }

        internal override void OnCreate(TilePuzzleService tilePuzzleService)
        {
            base.OnCreate(tilePuzzleService);

            _opacityShowShadow = _shadow.color.a;
        }

        internal override void OnSpawn(Vector2Int cellPosition, int indexColorPattern, UnitDataBase data = null)
        {
            base.OnSpawn(cellPosition, indexColorPattern, data);

            _currentClimaxSettings = unitDefaultSettings.ClimaxInBoardSettings;
            ElasticFollow.enabled = true;
        }

        internal override void StartShow()
        {
            transform.localScale = Vector3.zero;
            _tweenShow = Tween.Custom(this, 0f, 1f, unitDefaultSettings.durationShow, (unit, val) =>
            {
                unit.transform.localScale = Vector3.one * unit.unitDefaultSettings.curveScaleShow.Evaluate(val);
            }, startDelay: Random.Range(0f, unitDefaultSettings.delayShow));
        }

        protected override void ApplyState()
        {
            switch (State)
            {
                case UnitState.InSlot:
                    ApplyFill(0f);
                    SetOpacityShadow(_opacityShowShadow);
                    break;
                case UnitState.Movement:
                    ApplyFill(1f);
                    SetOpacityShadow(0f);
                    break;
                case UnitState.InBoard:
                    ApplyFill(0f);
                    SetOpacityShadow(0f, true);
                    TickOutline();
                    break;
                case UnitState.ClimaxInSlot:
                    SetOpacityShadow(0f);
                    ElasticFollow.enabled = false;
                    _currentClimaxSettings = unitDefaultSettings.ClimaxInSlotSettings;
                    break;
                case UnitState.ClimaxInBoard:
                    _currentClimaxSettings = unitDefaultSettings.ClimaxInBoardSettings;
                    break;
            }
        }

        private void SetOpacityShadow(float opacity, bool immediate = false)
        {
            _tweenShadow.Stop();

            if (immediate)
            {
                _shadow.color = _shadow.color.WithA(opacity);
                _shadow.gameObject.SetActive(opacity > 0f);
                return;
            }

            if (opacity == _shadow.color.a)
                return;

            _shadow.gameObject.SetActive(true);
            _tweenShadow = Tween
                .Alpha(_shadow, opacity, unitDefaultSettings.durationShadow, unitDefaultSettings.easeShadow)
                .OnComplete(this, unit => unit._shadow.gameObject.SetActive(unit._shadow.color.a > 0f));
        }

        private void ApplyFill(float powerFill)
        {
            _tweenColor.Stop();

            if (Mathf.Approximately(powerFill, _powerFill))
                return;

            _tweenColor = Tween.Custom(this, _powerFill, powerFill, unitDefaultSettings.durationColor, (unit, val) =>
            {
                unit._powerFill = val;
            });
        }

        private void TickOutline()
        {
            _tweenOutline.Stop();

            _tweenOutline = Tween.Custom(this, 0f, 1f, unitDefaultSettings.durationOutline, (unit, val) =>
            {
                unit._powerOutline = unitDefaultSettings.curveOutline.Evaluate(val);
            }, startDelay: unitDefaultSettings.delayOutline);
        }

        internal override void StartClimax(int queueNumber)
        {
            var delay = _currentClimaxSettings.startDelay + _currentClimaxSettings.delayBetweenClimaxLayer * queueNumber + Random.Range(_currentClimaxSettings.delayBetweenClimaxLayer * -0.5f, _currentClimaxSettings.delayBetweenClimaxLayer * 0.5f);
            _startScaleClimax = State == UnitState.ClimaxInSlot ? Vector3.one * _tilePuzzleService.TilePuzzleSettings.Slot.ScaleInSlot : transform.localScale;
            _tweenClimax = Sequence.Create();

            if (delay > 0f)
                _tweenClimax.ChainDelay(delay);

            _tweenClimax.Chain(Tween.Custom(this, 0f, 1f, _currentClimaxSettings.durationClimax, (unit, val) =>
            {
                unit.transform.localScale = unit._startScaleClimax * unit._currentClimaxSettings.curve.Evaluate(val);
                unit._timeClimax = val;
            }))
            .InsertCallback(delay + _currentClimaxSettings.endTimeClimax, this, (unit) =>
            {
                unit._soundService.PlayOnce(ClipType.Pop);
                unit.PlayParticle();
                Mediator.Vibration.Haptic(HapticType.LightImpact);

            })
            .OnComplete(this, (unit) =>
            {
#if UNITY_EDITOR
                if (!EditorTestingBoard.IsActive)
#endif
                    unit._tilePuzzleService.SpawnerService.DespawnUnit(unit);
            });
        }

        private void PlayParticle()
        {
            var particle = _particlePoolService.Spawn(ParticleType.BubblePop);
            var size = _currentClimaxSettings.sizeParticle.RangeRandom();
            particle.transform.position = transform.position;
            particle.ParticleProperty.SetSizeMultiplier(size);
            particle.ParticleProperty.SetColor(_colorChangerService.GetColorByIndex(IndexColorPattern));
            particle.Play();
        }

        internal override void Release()
        {
            ReleaseInternal();

            base.Release();
        }

        private void ReleaseInternal()
        {
            SetOpacityShadow(_opacityShowShadow, true);

            _tweenClimax.Stop();
            _tweenColor.Stop();
            _tweenOutline.Stop();
            _tweenShow.Stop();
            _timeClimax = 0f;
            _powerFill = 0f;
            _powerOutline = 0f;
        }

#if UNITY_EDITOR
        public override void ResetEditorTesting(bool andDespawn = false)
        {
            ReleaseInternal();
            SetOpacityShadow(0f, true);

            base.ResetEditorTesting(andDespawn);
        }
#endif
    }
}