namespace Modules.TilePuzzle.Structure.Entities
{
    using System;
    using UnityEngine;
    using Content.State;
    using Redcode.Extensions;
    using System.Collections.Generic;
    using Modules.Shared.GlobalServices;


    public sealed class UnitService : IDisposable
    {
        private TilePuzzleService _service;
        private ShapeBase _lastShape;


        public void Initialize(TilePuzzleService tilePuzzleService)
        {
            _service = tilePuzzleService;

            GameStateLifeСycle.AddListener(this, GameState.Playing, Update, GameStateLifeCycleType.LateTick);
            tilePuzzleService.OnChangeState += TilePuzzleService_OnChangeState;
        }

        private void TilePuzzleService_OnChangeState(TilePuzzleState state)
        {
            if (state != TilePuzzleState.ShapeMovement && _lastShape != null)
            {
                _lastShape.Units.ForEach(unit => unit.ElasticFollow.AdditionalOffset = Vector3.zero);
                _lastShape = null;
            }
        }

        private void Update()
        {
            float sqrRepulsionRadius = _service.TilePuzzleSettings.Unit.RepulsionRadius * _service.TilePuzzleSettings.Unit.RepulsionRadius;
            float repulsionSpeed = _service.TilePuzzleSettings.Unit.RepulsionSpeed;

            float deltaTime = Time.deltaTime;
            float lerpSpeed = deltaTime * repulsionSpeed;
            float inverseSqrRepulsionRadius = 1f / sqrRepulsionRadius;

            if (_service.ShapeDispenser.IsMovementShape)
            {
                _lastShape = _service.ShapeDispenser.CurrentShapeMovement;
                var shapeUnits = _lastShape.Units;
                var boardUnits = _service.BoardService.MatrixUnits;

                ProcessRepulsion(boardUnits, shapeUnits, lerpSpeed, inverseSqrRepulsionRadius, sqrRepulsionRadius);
                ProcessRepulsion(shapeUnits, boardUnits, lerpSpeed, inverseSqrRepulsionRadius, sqrRepulsionRadius, _service.TilePuzzleSettings.Unit.RepulsionFactor);
            }
            else
            {
                var boardUnits = _service.BoardService.MatrixUnits;
                ResetOffsets(boardUnits, lerpSpeed);
            }
        }

        private void ProcessRepulsion(IReadOnlyList<UnitBase> targetUnits, IReadOnlyList<UnitBase> influenceUnits, float lerpSpeed, float inverseSqrRepulsionRadius, float sqrRepulsionRadius, float repulsionFactor = 1f)
        {
            for (int i = 0; i < targetUnits.Count; i++)
            {
                var targetUnit = targetUnits[i];
                if (targetUnit == null) continue;

                Vector2 targetPos = targetUnit.transform.position;
                Vector2 repulsion = Vector2.zero;
                float totalInfluence = 0f;

                for (int j = 0; j < influenceUnits.Count; j++)
                {
                    var influenceUnit = influenceUnits[j];
                    if (influenceUnit == null) continue;

                    Vector2 influencePos = influenceUnit.transform.position;
                    Vector2 diff = influencePos - targetPos;

                    float sqrDistance = diff.sqrMagnitude;

                    if (sqrDistance < sqrRepulsionRadius && sqrDistance > 0.0001f)
                    {
                        float strength = 1f - sqrDistance * inverseSqrRepulsionRadius;
                        repulsion += strength * diff;
                        totalInfluence += strength;
                    }
                }

                Vector3 targetPosition;
                if (totalInfluence > 0)
                {
                    float scaleFactor = Mathf.Min(totalInfluence, 1f) * _service.TilePuzzleSettings.Unit.RepulsionRadius / Mathf.Sqrt(repulsion.sqrMagnitude);
                    repulsion *= (scaleFactor * repulsionFactor);
                    targetPosition = (Vector3)repulsion;
                }
                else
                {
                    targetPosition = Vector3.zero;
                }

                targetUnit.ElasticFollow.AdditionalOffset = Vector3.Lerp(targetUnit.ElasticFollow.AdditionalOffset, targetPosition, lerpSpeed);
            }
        }

        private void ResetOffsets(IReadOnlyList<UnitBase> units, float lerpSpeed)
        {
            for (int i = 0; i < units.Count; i++)
            {
                var unit = units[i];

                if (unit != null)
                    unit.ElasticFollow.AdditionalOffset = Vector3.Lerp(unit.ElasticFollow.AdditionalOffset, Vector3.zero, lerpSpeed);
            }
        }

        public void Dispose()
        {
            GameStateLifeСycle.RemoveListener(this);
            _service.OnChangeState -= TilePuzzleService_OnChangeState;
            _lastShape = null;
        }
    }
}