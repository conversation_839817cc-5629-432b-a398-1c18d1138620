namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using PrimeTween;
    using UnityEngine;
    using OdinContinuousGroup;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    [ManageableData, CreateAssetMenu(fileName = "DefaultUnitPreset", menuName = "Content/TilePuzzle/Structures/Unit/DefaultUnitPreset")]
    public class DefaultUnitPreset : UnitPresetBase
    {
        [Serializable]
        public class DefaultUnitSettings
        {
            [HideReferenceObjectPicker]
            [Serializable]
            public class ClimaxSettings
            {
                [OdinSerialize, PropertyRange(0f, 1f)]
                public float startDelay;
                [OdinSerialize, PropertyRange(0.01f, 1f)]
                public float delayBetweenClimaxLayer;
                [OdinSerialize, PropertyRange(0.01f, 1f)]
                public float durationClimax;
                [OdinSerialize, PropertyRange(0.01f, nameof(durationClimax))]
                public float endTimeClimax;
                [OdinSerialize]
                public Vector2 sizeParticle;
                [OdinSerialize, HideReferenceObjectPicker]
                public AnimationCurve curve;
            }

            [ContinuousGroup("Outline", GroupType.FoldoutGroup)]
            [OdinSerialize, PropertyRange(0.01f, 2f)]
            public float durationOutline;
            [OdinSerialize, PropertyRange(0f, 2f)]
            public float delayOutline;
            [OdinSerialize, HideReferenceObjectPicker]
            public AnimationCurve curveOutline;

            [ContinuousGroup("Color", GroupType.FoldoutGroup)]
            [OdinSerialize, PropertyRange(0.01f, 2f)]
            public float durationColor;

            [ContinuousGroup("Shadow", GroupType.FoldoutGroup)]
            [OdinSerialize, PropertyRange(0.01f, 1f)]
            public float durationShadow;
            [OdinSerialize, HideReferenceObjectPicker]
            public Ease easeShadow;

            [ContinuousGroup("Show", GroupType.FoldoutGroup)]
            [OdinSerialize, PropertyRange(0.01f, 2f)]
            public float durationShow;
            [OdinSerialize, PropertyRange(0f, 2f)]
            public float delayShow;
            [OdinSerialize, HideReferenceObjectPicker]
            public AnimationCurve curveScaleShow;

            [ContinuousGroup("Climax Effects", GroupType.FoldoutGroup)]
            [OdinSerialize]
            public ClimaxSettings ClimaxInBoardSettings { get; private set; } = new();
            [OdinSerialize]
            public ClimaxSettings ClimaxInSlotSettings { get; private set; } = new();
        }


        [HideLabel]
        [OdinSerialize]
        [HideReferenceObjectPicker]
        public DefaultUnitSettings Settings { get; private set; } = new();


        internal override void ApplySettings(UnitBase unit)
        {
            base.ApplySettings(unit);

            if (unit is DefaultUnit defaultUnit)
            {
                defaultUnit.unitDefaultSettings = Settings;
            }
        }
    }
}