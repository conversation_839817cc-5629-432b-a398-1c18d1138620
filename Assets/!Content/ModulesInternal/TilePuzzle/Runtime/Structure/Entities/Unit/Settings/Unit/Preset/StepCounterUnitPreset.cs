namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using UnityEngine;
    using Sirenix.Serialization;


    [ManageableData, CreateAssetMenu(fileName = "StepCounterUnitPreset", menuName = "Content/TilePuzzle/Structures/Unit/StepCounterUnitPreset")]
    public class StepCounterUnitPreset : DefaultUnitPreset
    {
        [OdinSerialize]
        public Vector2Int StepCounter { get; private set; }


        internal override void ApplySettings(UnitBase unit)
        {
            base.ApplySettings(unit);

            if (unit is StepCounterUnit stepCounterUnit)
            {
                stepCounterUnit.unitStepCounterSettings.stepCounter = Random.Range(StepCounter.x, StepCounter.y);
            }
        }
    }
}
