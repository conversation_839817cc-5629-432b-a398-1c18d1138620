namespace Modules.TilePuzzle.Structure.Entities.Unit.Data
{
    using Bayat.Json;
    using Modules.Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public abstract class UnitDataBase : DataStorage
    {
        [JsonProperty("c")] private int _color;

        public abstract UnitType UnitType { get; }
        public int IndexColorPattern
        {
            get => _color;
            set
            {
                if (_color != value)
                {
                    _color = value;
                    SetDirty();
                }
            }
        }
    }
}