namespace Modules.TilePuzzle.Structure.Entities
{
    using Unit.Data;
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Shared.Utils.Component.Transform;


    public enum UnitState
    {
        None = 0,
        InSlot = 10,
        Movement = 20,
        InBoard = 30,
        ClimaxInSlot = 200,
        ClimaxInBoard = 210,
    }

    [RequireComponent(typeof(ElasticFollow))]
    public abstract class UnitBase : SerializedMonoBehaviour
    {
        protected TilePuzzleService _tilePuzzleService;
        protected Vector2 _velocity;
        [ShowInInspector] public Vector4 cornerSquare;

        [ShowInInspector, ReadOnly] public UnitState State { get; private set; } = UnitState.None;
        [ShowInInspector, ReadOnly] public Vector2Int CellPosition { get; set; }
        [ShowInInspector] public int IndexColorPattern { get; set; }
        [field: SerializeField] public UnitElasticFollow ElasticFollow { get; private set; }

        public UnitDataBase Data { get; protected set; }
        public abstract float TimeClimax { get; }
        public abstract float PowerFill { get; }
        public abstract float PowerOutline { get; }
        public abstract UnitType UnitType { get; }
        public Vector2 Velocity => _velocity = ElasticFollow.Velocity;
        public Vector2 LastVelocity => _velocity;


        internal virtual void OnCreate(TilePuzzleService tilePuzzleService)
        {
            _tilePuzzleService = tilePuzzleService;
        }

        internal virtual void OnSpawn(Vector2Int cellPosition, int indexColorPattern, UnitDataBase data = null)
        {
            InitializeData(data);

            CellPosition = cellPosition;
            Data.IndexColorPattern = IndexColorPattern = indexColorPattern;
        }

        protected abstract void InitializeData(UnitDataBase data = null);

        internal abstract void StartShow();

        internal abstract void StartClimax(int queueNumber);

        internal void ResetIndexColorPattern()
        {
            IndexColorPattern = Data.IndexColorPattern;
        }

        internal void ChangeState(UnitState state)
        {
            if (State != state)
            {
                State = state;
                ApplyState();
            }
        }

        protected virtual void ApplyState() { }

        internal virtual void Release()
        {
            ChangeState(UnitState.None);

            CellPosition = Vector2Int.zero;
            IndexColorPattern = 0;
            transform.localScale = Vector3.one;
        }

#if UNITY_EDITOR
        public virtual void ResetEditorTesting(bool andDespawn = false)
        {
            transform.localScale = Vector3.one;

            if(andDespawn)
                _tilePuzzleService.SpawnerService.DespawnUnit(this);
        }
#endif
    }
}