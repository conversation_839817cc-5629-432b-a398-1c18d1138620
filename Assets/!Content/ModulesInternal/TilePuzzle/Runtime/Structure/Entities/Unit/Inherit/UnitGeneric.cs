namespace Modules.TilePuzzle.Structure.Entities
{
    using Unit.Data;


    public abstract class UnitGeneric<TData> : UnitBase where TData : UnitDataBase, new()
    {
        protected new TData Data => (TData)base.Data;


        protected override void InitializeData(UnitDataBase data = null)
        {
            if(data is TData)
            {
                base.Data = data;
                ApplyLoadedData();
            }
            else
            {
                base.Data = new TData();
            }
        }

        protected virtual void ApplyLoadedData() { }
    }
}