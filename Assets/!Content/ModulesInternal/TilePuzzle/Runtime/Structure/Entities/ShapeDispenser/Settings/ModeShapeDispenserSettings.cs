namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using UnityEngine;
    using Modules.ColorSystem;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using System.Collections.Generic;


    [Serializable]
    [HideReferenceObjectPicker]
    public class ModeShapeDispenserSettings
    {
        [Serializable]
        public struct DispenserStep
        {
            [Serializable]
            public struct Slot
            {
                [OdinSerialize]
                public ShapeInfo ShapeInfo { get; private set; }
                [OdinSerialize]
                [HideInInspector]
                public int IndexColor { get; private set; }

#if UNITY_EDITOR
                [HideLabel]
                [ShowInInspector]
                [ValueDropdown("@ColorSettingsBase.Get().TypeColors")]
                [GUIColor("@ColorSettingsBase.Get().GetStarterColors()[IndexColor]")]
                private string TypeColor
                {
                    get => ColorSettingsBase.Get().TypeColors[IndexColor];
                    set => IndexColor = ColorSettingsBase.Get().FindIndexTypeColor(value);
                }
#endif
            }

            [OdinSerialize]
            public Slot Slot1 { get; private set; }
            [OdinSerialize]
            public Slot Slot2 { get; private set; }
            [OdinSerialize]
            public Slot Slot3 { get; private set; }
        }


        [OdinSerialize]
        private DispenserStep[] _dispenserSteps;

        public IReadOnlyList<DispenserStep> DispenserSteps => _dispenserSteps;
    }
}