namespace Modules.TilePuzzle.Structure.Entities
{
    using Zenject;
    using PrimeTween;
    using UnityEngine;
    using Content.Sound;
    using Modules.ColorSystem;
    using Sirenix.OdinInspector;
    using Content.InteractionOnMap;
    using UnityEngine.EventSystems;
    using Modules.Shared.InputSystem;
    using Modules.Shared.WinLoseChecker;
    using Modules.Shared.GlobalServices.Saver;
    using Content.ModulesInternal.ParticlePool;
    using Content.ModulesInternal.WinLoseChecker;


    public class ShapeSlot : SerializedMonoBehaviour, IPointerDownHandler, IPointer<PERSON>p<PERSON><PERSON><PERSON>, <PERSON>ragHandler, ILoseChecker
    {
        private CameraService _cameraService;
        private WinLoseCheckerService _winLoseCheckerService;
        private TilePuzzleService _service;
        private InputService _inputService;
        private ParticlePoolService _particlePoolService;
        private ColorChangerService _colorChangerService;
        private SoundService _soundService;
        private Vector2 _offsetTap, _downPoint;
        private Sequence _tweenShape;

        public bool IsEmpty { get; private set; } = true;
        public bool IsMovementShape { get; private set; }
        public ShapeBase Shape { get; private set; }

        public bool IsLoseLocal => IsEmpty || !_service.BoardService.HasSpaceForShapeAnywhere(Shape.ShapeInfo);


        [Inject]
        public void Construct(CameraService cameraService, WinLoseCheckerService winLoseCheckerService, InputService inputService, ParticlePoolService particlePoolService, ColorChangerService colorChangerService, SoundService soundService)
        {
            _cameraService = cameraService;
            _winLoseCheckerService = winLoseCheckerService;
            _inputService = inputService;
            _particlePoolService = particlePoolService;
            _colorChangerService = colorChangerService;
            _soundService = soundService;
        }

        public void Initialize(TilePuzzleService tilePuzzleService)
        {
            _service = tilePuzzleService;
        }

        public void SetShape(ShapeBase shape)
        {
            Shape = shape;
            IsEmpty = false;
            Shape.UpdateParent(transform);
            PlaceShapeInSlot(true);
            PlayParticle(shape);
        }

        private void PlayParticle(ShapeBase shape)
        {
            var particle = _particlePoolService.Spawn(ParticleType.ShapeShow);
            particle.transform.position = transform.position;
            particle.ParticleProperty.SetColor(_colorChangerService.GetColorByIndex(shape.IndexColorPattern));
            particle.Play();
        }

        private void PlaceShapeInSlot(bool immediately = false)
        {
            _service.ShapeDispenser.ClearCurrentShapeMovement();

            _tweenShape.Stop();
            Shape.ChangeState(ShapeState.InSlot);

            if (immediately)
            {
                Shape.transform.localScale = Vector3.one * _service.TilePuzzleSettings.Slot.ScaleInSlot;
                Shape.ResetPositionUnits();
            }
            else
            {
                _tweenShape = Sequence.Create();
                _tweenShape.Chain(Tween.Scale(Shape.transform, Vector3.one * _service.TilePuzzleSettings.Slot.ScaleInSlot, _service.TilePuzzleSettings.Slot.DurationScaleToSlot, _service.TilePuzzleSettings.Slot.EaseScaleToSlot));
                _tweenShape.Group(Tween.LocalPosition(Shape.Pivot, Vector3.zero, _service.TilePuzzleSettings.Slot.DurationPositionToSlot, _service.TilePuzzleSettings.Slot.EasePositionToSlot));
            }
        }

        private void ShapeOutSlot()
        {
            _soundService.PlayOnce(ClipType.ShapeOutSlot);

            _tweenShape.Stop();
            Shape.ChangeState(ShapeState.Movement);
            _service.ShapeDispenser.SetCurrentShapeMovement(Shape);

            _tweenShape = Sequence.Create();
            _tweenShape.Chain(Tween.Scale(Shape.transform, Vector3.one, _service.TilePuzzleSettings.Slot.DurationScaleOutSlot, _service.TilePuzzleSettings.Slot.EaseScaleOutSlot));
        }

        public void Release()
        {
            _tweenShape.Stop();

            if (Shape != null)
            {
                var countUnits = Shape.Units.Count;

                for (int i = 0; i < countUnits; i++)
                {
                    var unit = Shape.Units[i];
                    unit.ChangeState(UnitState.ClimaxInSlot);
                    unit.StartClimax(Random.Range(0, countUnits));
                }

                ReleaseShape();
            }
        }

        public void Reset()
        {
            _tweenShape.Stop();
            IsEmpty = true;
            IsMovementShape = false;
            _inputService.TouchListener.ResetFocusPointerId();
            ReleaseShape(true);
        }

        void IPointerDownHandler.OnPointerDown(PointerEventData eventData)
        {
            if (!IsEmpty)
            {
                IsMovementShape = true;
                _inputService.TouchListener.SetFocusPointerId(eventData.pointerId);

                ShapeOutSlot();

                _downPoint = (Vector2)_cameraService.Camera.ScreenToWorldPoint(eventData.position);
                _offsetTap = (Vector2)transform.position + _service.TilePuzzleSettings.Slot.OffsetMover - _downPoint;
                Shape.SetPositionUnits(_downPoint + _offsetTap);

                _service.ChangeState(TilePuzzleState.ShapeMovement);
            }
        }

        void IPointerUpHandler.OnPointerUp(PointerEventData eventData)
        {
            if (!IsEmpty)
            {
                _service.ShapeDispenser.ClearCurrentShapeMovement();
                _service.ChangeState(TilePuzzleState.Working);

                if (_service.BoardService.TrySetShape(Shape))
                {
                    IsEmpty = true;
                    _service.BoardService.TryClearLinesWithClimax(Shape);
                    ReleaseShape();

                    _service.ShapeDispenser.TryFillSlots();
                }
                else
                {
                    PlaceShapeInSlot();
                    _soundService.PlayOnce(ClipType.ShapeInSlot);
                }

                _winLoseCheckerService.TryLose();

                _inputService.TouchListener.ResetFocusPointerId();
                IsMovementShape = false;

                StorageService.Save();
            }
        }

        void IDragHandler.OnDrag(PointerEventData eventData)
        {
            if (!IsEmpty && IsMovementShape)
            {
                var pos = (Vector2)_cameraService.Camera.ScreenToWorldPoint(eventData.position);
                var dynamicOffset = (pos - _downPoint) * _service.TilePuzzleSettings.Slot.DynamicOffsetMultiplayer;
                dynamicOffset = Vector2.Min(dynamicOffset, _service.TilePuzzleSettings.Slot.MaxDynamicOffset);
                Shape.SetPositionUnits(pos + _offsetTap + dynamicOffset);
            }
        }

        private void ReleaseShape(bool andUnits = false)
        {
            if (Shape != null)
            {
                _tweenShape.Complete();
                _service.SpawnerService.DespawnShape(Shape, andUnits);
                Shape = null;
            }
        }
    }
}