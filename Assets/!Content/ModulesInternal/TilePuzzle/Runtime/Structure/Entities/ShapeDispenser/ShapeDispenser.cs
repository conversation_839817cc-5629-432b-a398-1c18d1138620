namespace Modules.TilePuzzle.Structure.Entities
{
    using System;
    using Zenject;
    using Settings;
    using UnityEngine;
    using Dispenser.Data;
    using Sirenix.OdinInspector;
    using System.Collections.Generic;
    using Content.ModulesInternal.WinLoseChecker;


    public class ShapeDispenser : SerializedMonoBehaviour
    {
        [SerializeField] private ShapeSlot[] _shapeSlots;
        private ShapeDispenserData _data;
        private WinLoseCheckerService _winLoseCheckerService;
        private TilePuzzleService _tilePuzzleService;
        private List<ShapeInfo> _shapeInfosCache;

        public IReadOnlyList<ShapeSlot> ShapeSlots => _shapeSlots;
        public bool IsMovementShape => CurrentShapeMovement;
        public ShapeBase CurrentShapeMovement { get; private set; }


        [Inject]
        public void Construct(WinLoseCheckerService winLoseCheckerService)
        {
            _winLoseCheckerService = winLoseCheckerService;
        }

        public void Initialize(TilePuzzleService tilePuzzleService)
        {
            _tilePuzzleService = tilePuzzleService;
            _data = tilePuzzleService.Data.ShapeDispenserData;
            _shapeInfosCache = new(_shapeSlots.Length);

            for (int i = 0; i < _shapeSlots.Length; i++)
            {
                _shapeSlots[i].Initialize(tilePuzzleService);
                _winLoseCheckerService.AddLose(_shapeSlots[i], i);
            }
        }

        public void TryLoadSlots()
        {
            if (_data.ShapeDatas != null && _data.ShapeDatas.Count == _shapeSlots.Length && IsEmptySlots())
            {
                for (int i = 0; i < _shapeSlots.Length; i++)
                {
                    var shapeData = _data.ShapeDatas[i];

                    if (shapeData != null)
                    {
                        var shape = _tilePuzzleService.SpawnerService.SpawnLoadedShape(shapeData);
                        _shapeSlots[i].SetShape(shape);
                    }
                }
            }

            TryFillSlots();
        }

        public void TryFillSlots()
        {
            if (IsEmptySlots())
            {
                var dispenserSteps = _tilePuzzleService.ModeStructureSettings.ShapeDispenserSettings.DispenserSteps;
                if (dispenserSteps != null && _data.FillCount < dispenserSteps.Count)
                {
                    var dispenserStep = dispenserSteps[_data.FillCount];
                    var slot1 = dispenserStep.Slot1;
                    var slot2 = dispenserStep.Slot2;
                    var slot3 = dispenserStep.Slot3;

                    if(slot1.ShapeInfo != null)
                        SetShapeInfo(0, slot1.ShapeInfo, slot1.IndexColor);
                    if(slot2.ShapeInfo != null)
                        SetShapeInfo(1, slot2.ShapeInfo, slot2.IndexColor);
                    if(slot3.ShapeInfo != null)
                        SetShapeInfo(2, slot3.ShapeInfo, slot3.IndexColor);
                }
                else
                {
                    var bitMap = _tilePuzzleService.BoardService.BitCell.Bitmap;
                    var isPass = _tilePuzzleService.SpawnerService.TryGetShapeInfo(_shapeInfosCache, _shapeSlots.Length, ref bitMap);

                    if(!isPass)
                    {
                        _winLoseCheckerService.Lose();
                        return;
                    }

                    for (int i = 0; i < _shapeInfosCache.Count; i++)
                    {
                        SetShapeInfo(i, _shapeInfosCache[i]);
                    }
                }

                _data.FillCount++;
            }

            SyncShapeDispenserData();
        }

        private void SetShapeInfo(int indexSlot, ShapeInfo shapeInfo, int? indexColorPattern = null)
        {
            var shape = _tilePuzzleService.SpawnerService.SpawnShape(shapeInfo, indexColorPattern);
            _shapeSlots[indexSlot].SetShape(shape);
        }

        public void SyncShapeDispenserData()
        {
            _data.SyncShapeDatas(_shapeSlots);
        }

        public void SetCurrentShapeMovement(ShapeBase shape)
        {
            CurrentShapeMovement = shape;
        }

        public void ClearCurrentShapeMovement()
        {
            CurrentShapeMovement = null;
        }

        public bool IsEmptySlots()
        {
            return Array.TrueForAll(_shapeSlots, slot => slot.IsEmpty);
        }

        public void ReleaseSlots()
        {
            for (int i = 0; i < _shapeSlots.Length; i++)
            {
                _shapeSlots[i].Release();
            }
        }

        public void Reset()
        {
            for (int i = 0; i < _shapeSlots.Length; i++)
            {
                _shapeSlots[i].Reset();
            }
        }
    }
}