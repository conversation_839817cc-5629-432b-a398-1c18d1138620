namespace Modules.TilePuzzle.Structure.Entities.Dispenser.Data
{
    using System;
    using Bayat.Json;
    using System.Collections.Generic;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.TilePuzzle.Structure.Entities.Shape.Data;


    [JsonObject(MemberSerialization.OptIn)]
    public class ShapeDispenserData : DataStorage
    {
        [JsonProperty("s")] private ShapeDataBase[] _shapeDatas;
        [JsonProperty("c")] private int _fillCount;

        public IReadOnlyList<ShapeDataBase> ShapeDatas => _shapeDatas;
        public int FillCount
        {
            get => _fillCount;
            set
            {
                if (_fillCount != value)
                {
                    _fillCount = value;
                    SetDirty();
                }
            }
        }


        public void SyncShapeDatas(ShapeSlot[] shapeSlots)
        {
            _shapeDatas ??= new ShapeDataBase[shapeSlots.Length];
            var isDirty = false;

            for (int i = 0; i < shapeSlots.Length; i++)
            {
                var shape = shapeSlots[i].Shape;

                if (shape != null)
                {
                    var shapeData = shape.Data;

                    if (_shapeDatas[i] != shapeData)
                    {
                        _shapeDatas[i] = shapeData;
                        isDirty = true;
                    }
                }
                else if (_shapeDatas[i] != null)
                {
                    _shapeDatas[i] = null;
                    isDirty = true;
                }
            }

            if (isDirty)
                SetDirty();
        }

        public void ResetData()
        {
            Array.Clear(_shapeDatas, 0, _shapeDatas.Length);
            _fillCount = 0;
            SetDirty();
        }
    }
}