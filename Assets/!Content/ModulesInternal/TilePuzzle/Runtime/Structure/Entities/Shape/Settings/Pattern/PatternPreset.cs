namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using System.Collections.Generic;


    [ManageableData, CreateAssetMenu(fileName = "PatternPreset", menuName = "Content/TilePuzzle/PatternPreset", order = 0)]
    public class PatternPreset : SerializedScriptableObject
    {
        [OdinSerialize]
        [ListDrawerSettings(CustomAddFunction = nameof(CustomAddShape), ShowFoldout = true)]
        private ShapeInfo[] _shapes;

        public IReadOnlyList<ShapeInfo> Shapes => _shapes;

        private static ShapeInfo CustomAddShape()
        {
            return new ShapeInfo();
        }
    }
}