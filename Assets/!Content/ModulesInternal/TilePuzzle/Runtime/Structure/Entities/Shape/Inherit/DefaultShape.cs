namespace Modules.TilePuzzle.Structure.Entities
{
    using System;
    using Shape.Data;
    using System.Linq;
    using UnityEngine;


    public class DefaultShape : ShapeGeneric<DefaultShapeData>
    {
        [SerializeField] private Transform _pivot;
        public override Transform Pivot => _pivot;


        protected override void OnReadyInternal()
        {
            PrepareElasticFollow();
        }

        private void PrepareElasticFollow()
        {
            var sorted = Units.OrderBy(unit => (transform.position - unit.transform.position).sqrMagnitude);
            Transform lastUnit = null;

            foreach (var unit in sorted)
            {
                unit.ElasticFollow.SetTarget(lastUnit == null ? _pivot : lastUnit);
                lastUnit = unit.transform;
            }
        }

        public override void SetPositionUnits(Vector3 pos)
        {
            var diff = pos - transform.position;
            _pivot.localPosition = diff;

            var checkPosition = _pivot.position + OffsetCenter;
            var cellPosition = new Vector2Int((int)Math.Round(checkPosition.x), (int)Math.Round(checkPosition.y));

            if (CurrentCellPosition != cellPosition)
            {
                CurrentCellPosition = cellPosition;
                _tilePuzzleService.BoardService.CheckFreePlace(ShapeInfo, cellPosition, true, IndexColorPattern);
            }
        }

        public override void ResetPositionUnits()
        {
            _pivot.localPosition = Vector3.zero;
        }

        internal override void AddUnit(UnitBase unit)
        {
            unit.transform.SetParent(transform, false);
            unit.transform.localPosition = unit.CellPosition + (Vector2)OffsetCenter;

            base.AddUnit(unit);
        }
    }
}