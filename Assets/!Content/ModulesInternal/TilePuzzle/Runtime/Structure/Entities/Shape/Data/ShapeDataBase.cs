namespace Modules.TilePuzzle.Structure.Entities.Shape.Data
{
    using Unit.Data;
    using Bayat.Json;
    using Entities.Settings;
    using System.Collections.Generic;
    using Shared.GlobalServices.Saver;


    [JsonObject(MemberSerialization.OptIn)]
    public abstract class ShapeDataBase : DataStorage
    {
        [JsonProperty("s")] private ShapeInfo _shapeInfo;
        [JsonProperty("u")] private List<UnitDataBase> _units;

        public ShapeInfo ShapeInfo
        {
            get
            {
                return _shapeInfo;
            }
            set
            {
                if (_shapeInfo != value)
                {
                    _shapeInfo = value;
                    SetDirty();
                }
            }
        }
        public IReadOnlyList<UnitDataBase> Units => _units;


        public void SyncUnits(List<UnitBase> units)
        {
            _units ??= new List<UnitDataBase>(units.Count);
            _units.Clear();

            for (int i = 0; i < units.Count; i++)
            {
                _units.Add(units[i].Data);
            }

            SetDirty();
        }

        public virtual void Clear()
        {
            ShapeInfo = null;
            _units.Clear();
            SetDirty();
        }
    }
}