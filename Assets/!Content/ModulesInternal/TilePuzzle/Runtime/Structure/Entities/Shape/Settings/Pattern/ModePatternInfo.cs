namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using Services;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using System.Collections.Generic;


    [Serializable]
    public struct ModePatternInfo
    {
        [Required]
        [OdinSerialize]
        [InlineEditor(InlineEditorObjectFieldModes.Hidden)]
        private PatternPreset _pattern;

        [OdinSerialize]
        [DelayedProperty]
        public int SpawnWeight { get; private set; }

        [OdinSerialize]
        [HideLabel]
        private Conditions _conditions;

        public PatternPreset Pattern => _pattern;
        public IReadOnlyList<ShapeInfo> Shapes => _pattern.Shapes;

#if UNITY_EDITOR
        public string NameEditor => $"{_pattern?.name ?? "None"} : {SpawnWeight.ToString()}";


        public ModePatternInfo(PatternPreset pattern)
        {
            _pattern = pattern;
            SpawnWeight = 1;
            _conditions = null;
        }
#endif

        public bool IsPass(ScoreServiceBase score)
        {
            return _conditions?.IsPass(score) ?? true;
        }
    }
}
