namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using Bayat.Json;
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    [Serializable]
    [HideReferenceObjectPicker]
    [JsonObject(MemberSerialization.OptIn)]
    public class ShapeInfo
    {
        public const int DIMENSION = 5;
#if UNITY_EDITOR
        [JsonIgnore]
        [OdinSerialize] public int Count { get; set; }
#endif
        [JsonProperty("b")]
        [OdinSerialize] public int Bitmap { get; private set; }
        [JsonProperty("s")]
        [OdinSerialize] public Vector2Int Size { get; private set; } = new Vector2Int(2, 2);
    }
}