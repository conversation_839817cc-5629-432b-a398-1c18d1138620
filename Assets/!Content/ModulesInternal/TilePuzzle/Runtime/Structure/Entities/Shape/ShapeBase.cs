namespace Modules.TilePuzzle.Structure.Entities
{
    using System;
    using Shape.Data;
    using UnityEngine;
    using Entities.Settings;
    using Sirenix.OdinInspector;
    using System.Collections.Generic;


    public enum ShapeState
    {
        None = 0,
        InSlot = 1,
        Movement = 2,
    }

    public abstract class ShapeBase : SerializedMonoBehaviour
    {
        protected TilePuzzleService _tilePuzzleService;

        [ShowInInspector, ReadOnly] private List<UnitBase> _units = new(ShapeInfo.DIMENSION * ShapeInfo.DIMENSION);
        [ShowInInspector, ReadOnly] public ShapeInfo ShapeInfo => Data.ShapeInfo;
        [ShowInInspector, ReadOnly] public ShapeState State { get; private set; } = ShapeState.None;
        [ShowInInspector, ReadOnly] public Vector2Int CurrentCellPosition { get; protected set; }
        [ShowInInspector, ReadOnly] public Vector3 OffsetCenter { get; protected set; }
        public ShapeDataBase Data { get; protected set; }
        public abstract Transform Pivot { get; }
        public int IndexColorPattern => _units[0].IndexColorPattern;
        public IReadOnlyList<UnitBase> Units => _units;


        public void OnCreate(TilePuzzleService tilePuzzleService)
        {
            _tilePuzzleService = tilePuzzleService;
        }

        public void OnSpawn(ShapeInfo shapeInfo, ShapeDataBase data = null)
        {
            InitializeData(data);

            Data.ShapeInfo = shapeInfo;
            OffsetCenter = new Vector2(shapeInfo.Size.x * -0.5f + 0.5f, shapeInfo.Size.y * -0.5f + 0.5f);
        }

        protected abstract void InitializeData(ShapeDataBase data = null);


        public void OnReady()
        {
            ResetPositionUnits();
            OnReadyInternal();
            Data.SyncUnits(_units);
        }

        protected virtual void OnReadyInternal() { }

        internal virtual void AddUnit(UnitBase unit)
        {
            _units.Add(unit);
            unit.StartShow();
        }

        public abstract void SetPositionUnits(Vector3 pos);
        public abstract void ResetPositionUnits();

        public void UpdateParent(Transform parent)
        {
            transform.SetParent(parent, false);
        }

        public void ChangeState(ShapeState state)
        {
            if (State != state)
            {
                State = state;
                ApplyState();
            }
        }

        private void ApplyState()
        {
            var unitState = State switch
            {
                ShapeState.Movement => UnitState.Movement,
                ShapeState.InSlot => UnitState.InSlot,
                _ => throw new ArgumentOutOfRangeException()
            };

            foreach (var unit in _units)
            {
                unit.ChangeState(unitState);
            }
        }

        public virtual void Release()
        {
            transform.position = Vector3.zero;
            CurrentCellPosition = Vector2Int.zero;
            OffsetCenter = Vector3.zero;
            Data.Clear();
            _units.Clear();
        }
    }
}