namespace Modules.TilePuzzle.Structure.Entities
{
    using Shape.Data;


    public abstract class ShapeGeneric<TData> : ShapeBase where TData : ShapeDataBase, new()
    {
        protected new TData Data => (TData)base.Data;


        protected override void InitializeData(ShapeDataBase data = null)
        {
            if(data is TData)
            {
                base.Data = data;
                ApplyLoadedData();
            }
            else
            {
                base.Data = new TData();
            }
        }

        protected virtual void ApplyLoadedData() { }
    }
}