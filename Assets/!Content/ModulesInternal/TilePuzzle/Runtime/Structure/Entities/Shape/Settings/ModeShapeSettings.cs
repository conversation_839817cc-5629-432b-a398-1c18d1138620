namespace Modules.TilePuzzle.Structure.Entities.Settings
{
    using System;
    using Services;
    using System.Linq;
    using UnityEngine;
    using Redcode.Extensions;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Modules.Shared.Helpers;
    using System.Collections.Generic;
    using Random = UnityEngine.Random;
    using Modules.Shared.GlobalServices;
#if UNITY_EDITOR
    using UnityEditor;
#endif

    [Serializable]
    [HideReferenceObjectPicker]
    public class ModeShapeSettings
    {
        [OdinSerialize] public ShapeBase ShapePrefab { get; private set; }
#if UNITY_EDITOR
        [ListDrawerSettings(DraggableItems = false, HideAddButton = true, ListElementLabelName = "NameEditor", OnTitleBarGUI = nameof(TitleBarGUI))]
        [OnValueChanged(nameof(SortPatterns), true)]
#endif
        [OdinSerialize]
        private List<ModePatternInfo> _patterns = new();
        public IReadOnlyList<ModePatternInfo> Patterns => _patterns;

        private List<ModePatternInfo> _patternsCache;
        private int _lastCheckFrame = -1;
        private int _totalWeight;


#if UNITY_EDITOR
        [Button]
        public void SortPatterns()
        {
            _patterns = _patterns.OrderByDescending(p => p.SpawnWeight).ToList();
        }

        private void TitleBarGUI()
        {
            if (Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(Sirenix.Utilities.Editor.EditorIcons.Plus))
            {
                var patternPresets = HelperEditor.LoadAllAssetOfType<PatternPreset>("", "t:PatternPreset");

                if (patternPresets.Length > 0)
                {
                    GenericMenu menu = new GenericMenu();

                    foreach (var patternPreset in patternPresets)
                    {
                        string name = patternPreset.name;
                        menu.AddItem(new GUIContent(name), false, () => {
                            var patternInfo = new ModePatternInfo(patternPreset);
                            _patterns.Add(patternInfo);
                            SortPatterns();
                        });
                    }

                    menu.ShowAsContext();
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "PatternPreset not found in project", "OK");
                }
            }
        }
#endif

        public bool TryFindShapeInfo(ScoreServiceBase score, float randomValue, ref ulong bitMap, out ShapeInfo shapeInfo)
        {
            if (_lastCheckFrame != Time.frameCount)
            {
                _lastCheckFrame = Time.frameCount;

                _patternsCache ??= new(_patterns.Count);
                _patternsCache.Clear();
                _totalWeight = 0;

                for (int i = 0; i < _patterns.Count; i++)
                {
                    ModePatternInfo pattern = _patterns[i];
                    if (pattern.IsPass(score))
                    {
                        _patternsCache.Add(pattern);
                        _totalWeight += pattern.SpawnWeight;
                    }
                }
            }

            var patternPass = 0;
            var currentWeight = _totalWeight;

            var selectCountLine = -1;
            var selectShapeInfo = default(ShapeInfo);
            var selectBitmap = bitMap;
            var countLineMinClear = Mathf.RoundToInt(1f + randomValue);

            for (int i = 0; i < _patternsCache.Count; i++)
            {
                if (currentWeight == 0)
                    break;

                var selectedIndex = GetSelectedIndex(currentWeight, patternPass, _patternsCache);
                if (selectedIndex == -1)
                    break;

                var bitMapLocal = bitMap;
                if (TryGetShapesInfo(_patternsCache[selectedIndex].Shapes, countLineMinClear, ref bitMapLocal, out var shapeInfoLocal, out var countLineLocal) && countLineLocal > selectCountLine)
                {
                    selectCountLine = countLineLocal;
                    selectShapeInfo = shapeInfoLocal;
                    selectBitmap = bitMapLocal;
                }

                currentWeight -= _patternsCache[selectedIndex].SpawnWeight;
                patternPass |= 1 << selectedIndex;
            }

            if(selectCountLine >= 0)
            {
                DevDebug.LogModule($"Select shape size: {selectShapeInfo.Size.ToString()} with {selectCountLine.ToString()} lines, minLine: {countLineMinClear.ToString()}", "ShapeSpawner");

                bitMap = selectBitmap;
                shapeInfo = selectShapeInfo;
                return true;
            }

            DevDebug.LogModule("No shape found", "ShapeSpawner");
            shapeInfo = default;
            return false;
        }

        private int GetSelectedIndex(int weight, int patternPass, List<ModePatternInfo> patterns)
        {
            if (weight > 0)
            {
                var randomNumber = Random.Range(0, weight);
                var currentWeight = 0;

                for (var j = 0; j < patterns.Count; j++)
                {
                    if ((patternPass & (1 << j)) != 0)
                        continue;

                    currentWeight += patterns[j].SpawnWeight;
                    if (randomNumber < currentWeight)
                        return j;
                }
            }

            return -1;
        }

        private bool TryGetShapesInfo(IReadOnlyList<ShapeInfo> shapes, int countLineMinClear, ref ulong bitMap, out ShapeInfo shapeInfo, out int countLine)
        {
            var shapesCount = shapes.Count;

            var selectCountLine = -1;
            var selectBitmap = bitMap;
            var selectShapeInfo = default(ShapeInfo);
            var countLineCheck = -1;


            Span<int> indices = stackalloc int[shapesCount];
            for (int k = 0; k < shapesCount; k++)
            {
                indices[k] = k;
            }
            indices.Shuffle();

            foreach (int index in indices)
            {
                var bitMapLocal = bitMap;
                if (BitMapUtils.TryPlaceShapeWithMaxLine(ref bitMapLocal, shapes[index], out var countLineLocal) && countLineLocal > countLineCheck)
                {
                    selectCountLine = countLineLocal;
                    selectBitmap = bitMapLocal;
                    selectShapeInfo = shapes[index];

                    countLineCheck = Mathf.Max(selectCountLine, countLineMinClear - 1);
                }
            }


            if(selectCountLine != -1)
            {
                bitMap = selectBitmap;
                shapeInfo = selectShapeInfo;
                countLine = selectCountLine;
                return true;
            }

            countLine = 0;
            shapeInfo = default;
            return false;
        }
    }
}