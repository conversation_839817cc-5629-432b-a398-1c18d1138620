namespace Modules.TilePuzzle.Structure.Entities
{
    using Settings;
    using UnityEngine;
    using System.Runtime.CompilerServices;


    public static class BitMapUtils
    {
        public const int DIMENSION = 8;
        private const ulong BIT_COLUMN = 0b00000001_00000001_00000001_00000001_00000001_00000001_00000001_00000001;
        private const ulong BIT_ROW = 0b11111111;

        private static readonly Vector2Int _dimensionSize = new(DIMENSION, DIMENSION);
        private static readonly byte[] _selectSizeRow = new byte[]
        {
            0, 0b1, 0b11, 0b111, 0b1111, 0b11111
        };


        public static ulong ClearFullRowsAndColumns(ulong bitmap, out int countLine)
        {
            ulong newBitmap = bitmap;
            countLine = 0;

            for (int i = 0; i < DIMENSION; i++)
            {
                if (IsFullRow(bitmap, i))
                {
                    newBitmap = ClearRow(newBitmap, i);
                    countLine++;
                }
                if (IsFullColumn(bitmap, i))
                {
                    newBitmap = ClearColumn(newBitmap, i);
                    countLine++;
                }
            }

            return newBitmap;
        }

        public static bool IsFullRow(ulong bitmap, int row)
        {
            var selectRow = BIT_ROW << (row * DIMENSION);
            return (bitmap & selectRow) == selectRow;
        }

        public static bool IsFullColumn(ulong bitmap, int column)
        {
            var selectColumn = BIT_COLUMN << column;
            return (bitmap & selectColumn) == selectColumn;
        }

        public static ulong ClearRow(ulong bitmap, int row)
        {
            var selectRow = BIT_ROW << (row * DIMENSION);
            return bitmap & ~selectRow;
        }

        public static ulong ClearColumn(ulong bitmap, int column)
        {
            var selectColumn = BIT_COLUMN << column;
            return bitmap & ~selectColumn;
        }

        public static bool TryPlaceShapeAnywhere(ref ulong bitmap, ShapeInfo shapeInfo, bool andSet = false)
        {
            var size = shapeInfo.Size;
            var bitShapeOnMap = ConvertShapeToBitmap(shapeInfo.Bitmap, size);
            var offset = _dimensionSize - size;

            int countBit = offset.y * DIMENSION + offset.x;
            for (var i = 0; i <= countBit; i += ((i + size.x) % DIMENSION == 0) ? size.x : 1)
            {
                var bitShapeOffset = bitShapeOnMap << i;
                if ((bitmap & bitShapeOffset) == 0)
                {
                    if (andSet)
                        bitmap |= bitShapeOffset;

                    return true;
                }
            }

            return false;
        }

        public static bool TryPlaceShapeWithLine(ref ulong bitmap, ShapeInfo shapeInfo)
        {
            bitmap = ClearFullRowsAndColumns(bitmap, out _);

            var size = shapeInfo.Size;
            var bitShapeOnMap = ConvertShapeToBitmap(shapeInfo.Bitmap, size);
            var offset = _dimensionSize - size;

            int countBit = offset.y * DIMENSION + offset.x;
            for (var i = 0; i <= countBit; i += ((i + size.x) % DIMENSION == 0) ? size.x : 1)
            {
                var bitShapeOffset = bitShapeOnMap << i;
                if ((bitmap & bitShapeOffset) == 0)
                {
                    var bitmapLocal = bitmap | bitShapeOffset;
                    bitmapLocal = ClearFullRowsAndColumns(bitmapLocal, out var countLine);
                    if(countLine > 0)
                    {
                        bitmap = bitmapLocal;
                        return true;
                    }
                }
            }

            return false;
        }

        public static bool TryPlaceShapeWithMaxLine(ref ulong bitmap, ShapeInfo shapeInfo, out int countLine)
        {
            bitmap = ClearFullRowsAndColumns(bitmap, out _);
            var selectCountLine = -1;
            var selectBitmap = bitmap;

            var size = shapeInfo.Size;
            var bitShapeOnMap = ConvertShapeToBitmap(shapeInfo.Bitmap, size);
            var offset = _dimensionSize - size;

            int countBit = offset.y * DIMENSION + offset.x;
            for (var i = 0; i <= countBit; i += ((i + size.x) % DIMENSION == 0) ? size.x : 1)
            {
                var bitShapeOffset = bitShapeOnMap << i;
                if ((bitmap & bitShapeOffset) == 0)
                {
                    var bitmapLocal = bitmap | bitShapeOffset;
                    bitmapLocal = ClearFullRowsAndColumns(bitmapLocal, out var countLineLocal);
                    if(countLineLocal > selectCountLine)
                    {
                        selectCountLine = countLineLocal;
                        selectBitmap = bitmapLocal;
                    }
                }
            }

            bitmap = selectBitmap;
            countLine = selectCountLine;
            return selectCountLine != -1;
        }

        public static bool TryPlaceShapeAtPosition(ref ulong bitmap, ShapeInfo shapeInfo, Vector2Int pos, bool andSet = false)
        {
            if (!CheckContains(shapeInfo, pos))
                return false;

            var size = shapeInfo.Size;
            var bitShapeOnMap = ConvertShapeToBitmap(shapeInfo.Bitmap, size) << (pos.x + pos.y * DIMENSION);
            if ((bitmap & bitShapeOnMap) == 0)
            {
                if (andSet)
                    bitmap |= bitShapeOnMap;

                return true;
            }

            return false;
        }

        public static bool TryPlaceShapeAtPosition(ulong bitmap, ShapeInfo shapeInfo, Vector2Int pos)
        {
            if (!CheckContains(shapeInfo, pos))
                return false;

            var bitShapeOnMap = ConvertShapeToBitmap(shapeInfo.Bitmap, shapeInfo.Size) << (pos.x + pos.y * DIMENSION);
            return (bitmap & bitShapeOnMap) == 0;
        }

        public static ulong ConvertShapeToBitmap(int bitShape, Vector2Int size)
        {
            var selectRow = _selectSizeRow[size.x];
            ulong bitShapeOnMap = 0UL;

            for (int i = 0; i < size.y; i++)
            {
                bitShapeOnMap |= (ulong)(selectRow & (bitShape >> (i * size.x))) << (i * DIMENSION);
            }

            return bitShapeOnMap;
        }

        public static void SetBit(ref ulong bitmap, bool value, Vector2Int pos)
        {
            if (!CheckContains(pos))
                return;

            var index = pos.x + pos.y * DIMENSION;
            ulong mask = 1UL << index;

            if (value)
                bitmap |= mask;
            else
                bitmap &= ~mask;
        }

        public static void SetBit(ref ulong bitmap, bool value, int index)
        {
            if (!CheckContains(index))
                return;

            ulong mask = 1UL << index;

            if (value)
                bitmap |= mask;
            else
                bitmap &= ~mask;
        }

        public static bool TrySetBit(ref ulong bitmap, bool value, Vector2Int pos)
        {
            if (!CheckContains(pos))
                return false;

            var index = pos.x + pos.y * DIMENSION;
            var mask = 1UL << index;

            var currentValue = (bitmap & mask) != 0;
            if (currentValue == value)
                return false;

            if (value)
                bitmap |= mask;
            else
                bitmap &= ~mask;

            return true;
        }

        public static bool ToggleBit(ref ulong bitmap, Vector2Int pos)
        {
            if (!CheckContains(pos))
                return false;

            var index = pos.x + pos.y * DIMENSION;
            ulong mask = 1UL << index;
            bitmap ^= mask;
            return (bitmap & mask) != 0;
        }

        public static bool GetBit(ulong bitmap, Vector2Int pos)
        {
            if (!CheckContains(pos))
                return false;

            var index = pos.x + pos.y * DIMENSION;
            return (bitmap & (1UL << index)) != 0;
        }

        public static bool GetBit(ulong bitmap, int index)
        {
            if (!CheckContains(index))
                return false;

            return (bitmap & (1UL << index)) != 0;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool CheckContains(ShapeInfo shapeInfo, Vector2Int pos)
        {
            var size = shapeInfo.Size;
            return (pos.x >= 0 && pos.y >= 0) && (pos.x + size.x <= DIMENSION && pos.y + size.y <= DIMENSION);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool CheckContains(Vector2Int pos)
        {
            return pos.x is >= 0 and < DIMENSION && pos.y is >= 0 and < DIMENSION;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool CheckContains(int index)
        {
            return index is >= 0 and < DIMENSION * DIMENSION;
        }
    }
}