namespace Modules.TilePuzzle.Structure.Entities
{
    using Zenject;
    using Settings;
    using Board.Data;
    using UnityEngine;
    using Content.Sound;
    using UnityEngine.Pool;
    using Modules.Shared.Extensions;
    using System.Collections.Generic;


    public sealed class BoardService
    {
        public class BitMap
        {
            internal ulong bitmap;

            public ulong Bitmap => bitmap;

            public bool this[Vector2Int pos]
            {
                get => BitMapUtils.GetBit(bitmap, pos);
                set => BitMapUtils.SetBit(ref bitmap, value, pos);
            }

            public bool this[int index]
            {
                get => BitMapUtils.GetBit(bitmap, index);
                set => BitMapUtils.SetBit(ref bitmap, value, index);
            }
        }

        private TilePuzzleService _tilePuzzleService;
        private BoardData _data;
        private readonly UnitBase[] _matrixUnits = new UnitBase[BitMapUtils.DIMENSION * BitMapUtils.DIMENSION];
        private List<UnitBase> _colorChangeUnits = new(16);
        private SoundService _soundService;

        public delegate void ClimaxHandler(int countLines, int indexColorPattern);
        public delegate void SetShapeHandler(ShapeBase shape);
        public event ClimaxHandler OnClimax = delegate { };
        public event SetShapeHandler OnSetShape = delegate { };

        public BitMap BitCell { get; } = new();
        public IReadOnlyList<UnitBase> MatrixUnits => _matrixUnits;
        public int CountUnits {get; private set;}
        public int MaxClimaxLines => _data.MaxClimaxLines;
        public UnitBase this[Vector2Int pos]
        {
            get
            {
                if (BitMapUtils.CheckContains(pos))
                    return _matrixUnits[pos.x + (pos.y * BitMapUtils.DIMENSION)];

                return null;
            }
            set
            {
                if (BitMapUtils.CheckContains(pos))
                {
                    var index = pos.x + (pos.y * BitMapUtils.DIMENSION);

                    if (_matrixUnits[index] != value)
                    {
                        this[index] = value;

                        if(value != null)
                        {
                            value.CellPosition = pos;
                            value.ElasticFollow.SetPosition(new Vector3(pos.x, pos.y));
                            value.ChangeState(UnitState.InBoard);
                            BitCell[pos] = true;
                        }
                        else
                        {
                            BitCell[pos] = false;
                        }
                    }
                }
            }
        }

        public UnitBase this[int index]
        {
            get
            {
                if (BitMapUtils.CheckContains(index))
                    return _matrixUnits[index];

                return null;
            }
            private set
            {
                if(_matrixUnits[index] != value)
                {
                    _matrixUnits[index] = value;
                    CountUnits += value != null ? 1 : -1;
                }
            }
        }


        [Inject]
        public void Construct(SoundService soundService)
        {
            _soundService = soundService;
        }

        public void Initialize(TilePuzzleService tilePuzzleService)
        {
            _tilePuzzleService = tilePuzzleService;
            _data = _tilePuzzleService.Data.BoardData;
        }

        private void OnClimaxInternal(int countLines, int indexColorPattern)
        {
            _data.TrySetMaxClimaxLines(countLines);
            OnClimax(countLines, indexColorPattern);
        }

        public void SyncUnitsData()
        {
            _data.SyncUnits(_matrixUnits);
            _data.Bitmap = BitCell.bitmap;
        }

        public bool TrySetShape(ShapeBase shape)
        {
            _tilePuzzleService.SpawnerService.DespawnMarkers();

            if (BitMapUtils.TryPlaceShapeAtPosition(ref BitCell.bitmap, shape.ShapeInfo, shape.CurrentCellPosition, true))
            {
#if UNITY_EDITOR
                EditorTestingBoard.ClearUnits();
#endif

                for (var i = 0; i < shape.Units.Count; i++)
                {
                    var unit = shape.Units[i];
                    this[shape.CurrentCellPosition + unit.CellPosition] = unit;
                }

                _soundService.PlayOnce(ClipType.SetShape);
                OnSetShape(shape);
                return true;
            }

            return false;
        }

        public bool TryClearLinesWithClimax(ShapeBase shape)
        {
            var bitmapClean = BitMapUtils.ClearFullRowsAndColumns(BitCell.Bitmap, out var countLine);
            OnClimaxInternal(countLine, shape.IndexColorPattern);

            if (bitmapClean != BitCell.Bitmap)
            {
                var nextCheckLayer = ListPool<UnitBase>.Get();
                var layerClimaxed = ListPool<UnitBase>.Get();
                var countClimaxLine = 0;

                var bitFullLines = bitmapClean ^ BitCell.Bitmap;

                foreach (var unit in shape.Units)
                {
                    TryActivateUnitClimax(ref bitFullLines, layerClimaxed, countClimaxLine, unit.CellPosition);
                }

                while (layerClimaxed.Count > 0)
                {
                    countClimaxLine++;

                    for (int i = 0; i < layerClimaxed.Count; i++)
                    {
                        var unit = layerClimaxed[i];

                        TryActivateUnitClimax(ref bitFullLines, nextCheckLayer, countClimaxLine, unit.CellPosition + Vector2Int.left);
                        TryActivateUnitClimax(ref bitFullLines, nextCheckLayer, countClimaxLine, unit.CellPosition + Vector2Int.right);
                        TryActivateUnitClimax(ref bitFullLines, nextCheckLayer, countClimaxLine, unit.CellPosition + Vector2Int.up);
                        TryActivateUnitClimax(ref bitFullLines, nextCheckLayer, countClimaxLine, unit.CellPosition + Vector2Int.down);
                    }

                    var layerTemp = layerClimaxed;
                    layerClimaxed = nextCheckLayer;
                    nextCheckLayer = layerTemp;
                    nextCheckLayer.Clear();
                }

                BitCell.bitmap = bitmapClean;
                ListPool<UnitBase>.Release(nextCheckLayer);
                ListPool<UnitBase>.Release(layerClimaxed);

                SyncUnitsData();
                return true;
            }

            SyncUnitsData();
            return false;
        }

        public bool TryClearLinesWithClimax()
        {
            var bitmapClean = BitMapUtils.ClearFullRowsAndColumns(BitCell.Bitmap, out var countLine);

            if (bitmapClean != BitCell.Bitmap)
            {
                OnClimaxInternal(countLine, _tilePuzzleService.TilePuzzleSettings.RangeShapeIndexColorPattern.RangeRandom());

                var bitFullLines = bitmapClean ^ BitCell.Bitmap;

                for (int i = 0; i < BitMapUtils.DIMENSION * BitMapUtils.DIMENSION; i++)
                {
                    if((bitFullLines & (1UL << i)) != 0)
                    {
                        _matrixUnits[i].StartClimax(Random.Range(0, 8));
                        this[i] = null;
                    }
                }

                BitCell.bitmap = bitmapClean;

                SyncUnitsData();
                return true;
            }

            return false;
        }

        private void TryActivateUnitClimax(ref ulong bitFullLines, List<UnitBase> layer, int countClimaxLine, Vector2Int pos)
        {
            if (BitMapUtils.TrySetBit(ref bitFullLines, false, pos))
            {
                var index = pos.x + (pos.y * BitMapUtils.DIMENSION);
                var unit = _matrixUnits[index];
                this[index] = null;
                layer.Add(unit);

                unit.ChangeState(UnitState.ClimaxInBoard);
                unit.StartClimax(countClimaxLine);
#if UNITY_EDITOR
                EditorTestingBoard.AddUnit(unit, countClimaxLine);
#endif
            }
        }

        public bool HasSpaceForShapeAnywhere(ShapeInfo shapeInfo)
        {
            return BitMapUtils.TryPlaceShapeAnywhere(ref BitCell.bitmap, shapeInfo, false);
        }

        /// <summary>
        /// Проверяет возможность размещения фигуры на доске и выполняет соответствующие действия
        /// </summary>
        /// <param name="shapeInfo">Информация о фигуре</param>
        /// <param name="position">Позиция для размещения</param>
        /// <param name="isMark">Нужно ли отмечать позицию маркерами</param>
        /// <param name="indexColorPattern">Индекс цветового шаблона (опционально)</param>
        public void CheckFreePlace(ShapeInfo shapeInfo, Vector2Int position, bool isMark, int? indexColorPattern = null)
        {
            ResetColorChangeUnits();
            _tilePuzzleService.SpawnerService.DespawnMarkers();

            var bitmap = BitCell.Bitmap;
            if (BitMapUtils.TryPlaceShapeAtPosition(ref bitmap, shapeInfo, position, true))
            {
                if (isMark)
                    _tilePuzzleService.SpawnerService.SpawnMarkers(shapeInfo, position);

                if (indexColorPattern.HasValue)
                    ProcessCompletedLinesAndColumns(bitmap, indexColorPattern.Value);
            }
        }

        private void ProcessCompletedLinesAndColumns(ulong bitmap, int indexColorPattern)
        {
            for (int i = 0; i < BitMapUtils.DIMENSION; i++)
            {
                if (BitMapUtils.IsFullRow(bitmap, i))
                    ProcessCompletedLine(i * BitMapUtils.DIMENSION, (i + 1) * BitMapUtils.DIMENSION, 1, indexColorPattern);

                if (BitMapUtils.IsFullColumn(bitmap, i))
                    ProcessCompletedLine(i, BitMapUtils.DIMENSION * BitMapUtils.DIMENSION, BitMapUtils.DIMENSION, indexColorPattern);
            }
        }

        private void ProcessCompletedLine(int start, int end, int step, int indexColorPattern)
        {
            for (int j = start; j < end; j += step)
            {
                var unit = _matrixUnits[j];
                if (unit != null)
                {
                    unit.IndexColorPattern = indexColorPattern;
                    _colorChangeUnits.Add(unit);
                }
            }
        }

        private void ResetColorChangeUnits()
        {
            foreach (var unit in _colorChangeUnits)
            {
                unit.ResetIndexColorPattern();
            }

            _colorChangeUnits.Clear();
        }

        public void Reset()
        {
            for (var i = 0; i < _matrixUnits.Length; i++)
            {
                if(_matrixUnits[i] != null)
                {
                    _tilePuzzleService.SpawnerService.DespawnUnit(_matrixUnits[i]);
                    _matrixUnits[i] = null;
                }
            }
            CountUnits = 0;
            BitCell.bitmap = 0;

            _colorChangeUnits.Clear();
        }

#if UNITY_EDITOR
        public void ImitateClimax(int countLines, int indexColorPattern)
        {
            OnClimaxInternal(countLines, indexColorPattern);
        }
#endif
    }
}