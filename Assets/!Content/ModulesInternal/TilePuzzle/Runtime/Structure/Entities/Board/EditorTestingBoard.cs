#if UNITY_EDITOR
using UnityEngine;
using System.Collections.Generic;
using Modules.TilePuzzle.Structure.Entities;


public class EditorTestingBoard : MonoBehaviour
{
    public struct DataUnit
    {
        public UnitBase unit;
        public int countClimaxLine;
    }

    private static List<DataUnit> _units = new();
    public static bool IsActive { get; private set; } = false;


    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    public static void CreateNewSingleton()
    {
        var go = new GameObject();

        DontDestroyOnLoad(go);
        go.hideFlags = HideFlags.DontSave;

        var instance = go.AddComponent<EditorTestingBoard>();
        go.name = nameof(EditorTestingBoard);
    }

    public void Update()
    {
        if(!IsActive && Input.GetKey(KeyCode.LeftShift) && Input.GetKeyDown(KeyCode.R))
        {
            Debug.LogWarning("Active Editor Testing Board");
            IsActive = true;
        }

        if(IsActive && Input.GetKey(KeyCode.LeftAlt) && Input.GetKeyDown(KeyCode.R))
        {
            Debug.LogWarning("Deactive Editor Testing Board");
            IsActive = false;
            ClearUnits();
        }

        if(IsActive && Input.GetKeyDown(KeyCode.R))
        {
            Debug.LogWarning("Reset Units");
            ResetUnits();
        }

        if(IsActive && Input.GetKeyDown(KeyCode.T))
        {
            Debug.LogWarning("Start Climax Last");
            StartClimaxLast();
        }
    }

    public static void AddUnit(UnitBase unit, int countClimaxLine)
    {
        if(!IsActive)
            return;


        for (int i = 0; i < _units.Count; i++)
        {
            if(_units[i].unit == unit)
            {
                _units[i] = new DataUnit() { unit = unit, countClimaxLine = countClimaxLine };
                return;
            }
        }

        _units.Add(new DataUnit() { unit = unit, countClimaxLine = countClimaxLine });
    }

    public static void ClearUnits()
    {
        if(!IsActive)
            return;

        for (int i = 0; i < _units.Count; i++)
        {
            _units[i].unit.ResetEditorTesting(true);
        }

        _units.Clear();
    }

    public static void ResetUnits()
    {
        if(!IsActive)
            return;

        for (int i = 0; i < _units.Count; i++)
        {
            _units[i].unit.ResetEditorTesting();
        }
    }

    public static void StartClimaxLast()
    {
        if(!IsActive || _units.Count == 0)
            return;

        for (int i = 0; i < _units.Count; i++)
        {
            var data = _units[i];
            data.unit.StartClimax(data.countClimaxLine);
        }

    }
}
#endif