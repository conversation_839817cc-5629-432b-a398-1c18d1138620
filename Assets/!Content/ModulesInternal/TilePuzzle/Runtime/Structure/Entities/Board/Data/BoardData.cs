namespace Modules.TilePuzzle.Structure.Entities.Board.Data
{
    using Bayat.Json;
    using System.Collections.Generic;
    using Modules.Shared.GlobalServices.Saver;
    using Modules.TilePuzzle.Structure.Entities.Unit.Data;


    [JsonObject(MemberSerialization.OptIn)]
    public class BoardData : DataStorage
    {
        [JsonProperty("i")] private bool _isInitialized;
        [JsonProperty("b")] private ulong _bitmap;
        [JsonProperty("u")] private List<UnitDataBase> _units;
        [JsonProperty("m")] private int _maxClimaxLines;

        public IReadOnlyList<UnitDataBase> Units => _units;

        public bool IsInitialized
        {
            get => _isInitialized;
            set
            {
                if (_isInitialized != value)
                {
                    _isInitialized = value;
                    SetDirty();
                }
            }
        }

        public ulong Bitmap
        {
            get => _bitmap;
            set
            {
                if (_bitmap != value)
                {
                    _bitmap = value;
                    SetDirty();
                }
            }
        }

        public int MaxClimaxLines => _maxClimaxLines;


        public void SyncUnits(IReadOnlyList<UnitBase> units)
        {
            _units ??= new List<UnitDataBase>(units.Count);
            _units.Clear();

            for (int i = 0; i < units.Count; i++)
            {
                var unit = units[i];

                if (unit != null)
                    _units.Add(unit.Data);
            }

            SetDirty();
        }

        public bool TrySetMaxClimaxLines(int maxClimaxLines)
        {
            if (maxClimaxLines > _maxClimaxLines)
            {
                _maxClimaxLines = maxClimaxLines;
                SetDirty();

                return true;
            }

            return false;
        }

        public void ResetData()
        {
            _isInitialized = false;
            _bitmap = 0;
            _units.Clear();
            _maxClimaxLines = 0;

            SetDirty();
        }
    }
}
