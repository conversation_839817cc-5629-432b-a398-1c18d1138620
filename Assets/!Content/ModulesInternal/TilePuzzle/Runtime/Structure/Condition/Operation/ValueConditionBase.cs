namespace Modules.TilePuzzle.Structure
{
    using Services;
    using OdinContinuousGroup;
    using Sirenix.Serialization;


    public abstract class ValueConditionBase<T> : ConditionBase where T : IValueCondition
    {
        [ContinuousGroup("Condition", GroupType.HorizontalGroup)]
        [OdinSerialize] public ConditionValue Condition { get; private set; }
        protected abstract int Value { get; }


        public override bool IsPass(ScoreServiceBase score)
        {
            if (score is T valueCondition)
            {
                return Condition switch
                {
                    ConditionValue.Equal => GetValue(valueCondition) == Value,
                    ConditionValue.NotEqual => GetValue(valueCondition) != Value,
                    ConditionValue.Greater => GetValue(valueCondition) > Value,
                    ConditionValue.Less => GetValue(valueCondition) < Value,
                    _ => throw new System.NotImplementedException(),
                };
            }

            return false;
        }

        protected abstract int GetValue(T valueCondition);
    }
}