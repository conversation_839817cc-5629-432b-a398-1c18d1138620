namespace Modules.TilePuzzle.Structure
{
    using Services;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;


    [HideReferenceObjectPicker]
    public class Conditions
    {
        [OdinSerialize] private ConditionBase[] _conditions = new ConditionBase[0];


        public bool IsPass(ScoreServiceBase score)
        {
            foreach (var condition in _conditions)
            {
                if (!condition.IsPass(score)) return false;
            }

            return true;
        }
    }
}