namespace Modules.TilePuzzle
{
    using System;
    using UnityEngine;
    using Content.State;
    using Sirenix.OdinInspector;
    using Modules.Shared.Utils.Component.MeshRenderer;


    public class BoardView : SerializedMonoBehaviour
    {
        private static readonly int _outlineProperty = Shader.PropertyToID("_Outline");
        private static readonly int _gridSizeProperty = Shader.PropertyToID("_GridSize");
        private static readonly int _show = Animator.StringToHash("Show");

        [Required]
        [SerializeField] private Renderer _renderer;
        [Required]
        [SerializeField] private Animator _animator;
        [Range(0f, 0.99f)]
        [SerializeField] private float _outline;
        [SerializeField] private int _gridSize;

        [Header("Baking Output")]
        [Required]
        [SerializeField] private Material _bakedMaterial;
        [SerializeField] private Vector2Int _bakedTextureSize = new(512, 512);

        private RenderTexture _bakedTextureInstance;
        private Material _animatedMaterialInstance;

        [field: SerializeField] public MeshColorBase MeshColor { get; private set; }


        private void Start()
        {
            _animatedMaterialInstance = _renderer.material;
            StartAnimation();

            GameStateService.OnStateChanged += GameStateService_OnStateChanged;
        }

        private void GameStateService_OnStateChanged(GameState state)
        {
            if (state.IsReplay())
            {
                StartAnimation();
            }
        }

        private void OnDestroy()
        {
            if (_bakedTextureInstance != null)
                Destroy(_bakedTextureInstance);

            if (_animatedMaterialInstance != null)
                Destroy(_animatedMaterialInstance);

            GameStateService.OnStateChanged -= GameStateService_OnStateChanged;
        }

        private void StartAnimation()
        {
            _renderer.material = _animatedMaterialInstance;
            _animator.SetTrigger(_show);
        }

        [HideInEditorMode]
        [Button("Bake Board Now (Test)")]
        public void BakeBoard()
        {
            if (_bakedTextureInstance == null)
            {
                _bakedTextureInstance = new RenderTexture(_bakedTextureSize.x, _bakedTextureSize.y, 0);
                _bakedTextureInstance.Create();
                Graphics.Blit(null, _bakedTextureInstance, _animatedMaterialInstance);
            }

            MeshColor.Texture = _bakedTextureInstance;
            _renderer.material = _bakedMaterial;
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (_renderer.sharedMaterial.shader.name == "!Self/Modules/TilePuzzle")
            {
                _renderer.sharedMaterial.SetFloat(_outlineProperty, _outline);
                _renderer.sharedMaterial.SetInt(_gridSizeProperty, _gridSize);
                transform.localScale = Vector3.one * _gridSize / (1f - _outline);
            }
        }
#endif
    }
}