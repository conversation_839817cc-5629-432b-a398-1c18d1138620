namespace Modules.TilePuzzle.Data
{
    using Bayat.Json;
    using Shared.GlobalServices.Saver;
    using Structure.Services.Score.Data;
    using Structure.Services.Combo.Data;
    using Structure.Entities.Board.Data;
    using Structure.Entities.Dispenser.Data;


    [JsonObject(MemberSerialization.OptIn)]
    public class TilePuzzleData : DataStorage
    {
        [JsonProperty("s")] private ScoreDataBase _scoreData;
        [JsonProperty("c")] private ComboDataBase _comboData;
        [JsonProperty("b")] private BoardData _boardData;
        [JsonProperty("d")] private ShapeDispenserData _shapeDispenserData;

        public BoardData BoardData => _boardData ??= new BoardData();
        public ShapeDispenserData ShapeDispenserData => _shapeDispenserData ??= new ShapeDispenserData();


        public T GetScoreData<T>() where T : ScoreDataBase, new()
        {
            if(_scoreData is T scoreData)
                return scoreData;

            _scoreData = new T();
            return (T)_scoreData;
        }

        public T GetComboData<T>() where T : ComboDataBase, new()
        {
            if(_comboData is T comboData)
                return comboData;

            _comboData = new T();
            return (T)_comboData;
        }

        public void ResetData()
        {
            _scoreData?.ResetData();
            _comboData?.ResetData();
            _boardData?.ResetData();
            _shapeDispenserData?.ResetData();
        }
    }
}
