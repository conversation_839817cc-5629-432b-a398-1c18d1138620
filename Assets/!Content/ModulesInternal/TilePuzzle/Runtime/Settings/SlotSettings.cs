namespace Modules.TilePuzzle.Settings
{
    using System;
    using PrimeTween;
    using Bayat.Json;
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Modules.Shared.RemoteConfig;
    using Content.ModulesInternal.Tags;


    [ManageableData, CreateAssetMenu(fileName = "SlotSettings", menuName = "Content/TilePuzzle/SlotSettings")]
    public class SlotSettings : RemoteConfigBase
    {
        protected override string ParameterKey => Tags.Config.Slot.SLOT_SETTINGS;

        [FoldoutGroup("Basic Settings")]
        [OdinSerialize]
        [JsonProperty(Tags.Config.Slot.OFFSET_MOVER)]
        public Vector2 OffsetMover { get; private set; }
        [FoldoutGroup("Basic Settings")]
        [OdinSerialize]
        [JsonProperty(Tags.Config.Slot.DYNAMIC_OFFSET_MULTIPLAYER)]
        public Vector2 DynamicOffsetMultiplayer { get; private set; }
        [FoldoutGroup("Basic Settings")]
        [OdinSerialize]
        [JsonProperty(Tags.Config.Slot.MAX_DYNAMIC_OFFSET)]
        public Vector2 MaxDynamicOffset { get; private set; }

        [FoldoutGroup("Animation")]
        [OdinSerialize, PropertyRange(0f, 1f)]
        public float ScaleInSlot { get; private set; }

        [FoldoutGroup("Animation")]
        [HorizontalGroup("Animation/Scale Duration", LabelWidth = 120)]
        [OdinSerialize]
        public float DurationScaleToSlot { get; private set; }

        [HorizontalGroup("Animation/Scale Duration")]
        [OdinSerialize]
        public float DurationScaleOutSlot { get; private set; }

        [FoldoutGroup("Animation")]
        [OdinSerialize]
        public float DurationPositionToSlot { get; private set; }

        [FoldoutGroup("Animation")]
        [HorizontalGroup("Animation/Scale Ease", LabelWidth = 120)]
        [OdinSerialize]
        public Ease EaseScaleToSlot { get; private set; }

        [HorizontalGroup("Animation/Scale Ease")]
        [OdinSerialize]
        public Ease EaseScaleOutSlot { get; private set; }

        [FoldoutGroup("Animation")]
        [OdinSerialize]
        public Ease EasePositionToSlot { get; private set; }
    }
}