namespace Modules.TilePuzzle.Settings
{
    using System;
    using PrimeTween;
    using Bayat.Json;
    using UnityEngine;
    using Sirenix.OdinInspector;
    using Sirenix.Serialization;
    using Modules.Shared.RemoteConfig;
    using Content.ModulesInternal.Tags;


    [ManageableData, CreateAssetMenu(fileName = "TilePuzzleSettings", menuName = "Content/TilePuzzle/TilePuzzleSettings")]
    public class TilePuzzleSettings : SerializedScriptableObject
    {
        [Serializable]
        [HideReferenceObjectPicker]
        [HideLabel]
        [FoldoutGroup("Unit")]
        public class UnitSettings
        {
            [FoldoutGroup("Repulsion")]
            [OdinSerialize]
            public float RepulsionRadius { get; private set; }

            [FoldoutGroup("Repulsion")]
            [OdinSerialize]
            public float RepulsionFactor { get; private set; }

            [FoldoutGroup("Repulsion")]
            [OdinSerialize]
            public float RepulsionSpeed { get; private set; }
        }

        [OdinSerialize]
        [InlineEditor(InlineEditorObjectFieldModes.Boxed)]
        public SlotSettings Slot { get; private set; }

        [OdinSerialize]
        public UnitSettings Unit { get; private set; } = new();


        [FoldoutGroup("Shape")]
        [OdinSerialize]
        public Vector2Int RangeShapeIndexColorPattern { get; private set; }
    }
}