#if !PROD
namespace Content.JahroConsoleRegister
{
    using Zenject;
    using JahroConsole;
    using PlatformServices;
    using PlatformServices.Services.Settings;
    using Modules.Shared.PlatformServices.Settings;


    public static class MediatorRegisterConsole
    {
        public const string MEDIATOR_NAME = "Mediator";

/*         public static class Ads
        {
            private const string NAME = nameof(Ads);
            private const string NAME_PATH = MEDIATOR_NAME + "." + NAME + ".";
            private const string NAME_GROUP = MEDIATOR_NAME + "/" + NAME;

            public static class Interval
            {
                private const string NAME_INTERVAL = nameof(Interval);
                private const string NAME_GROUP_INTERVAL = NAME_GROUP + "/" + NAME_INTERVAL;

                [Jah<PERSON>Watch(nameof(IsActive), NAME_GROUP_INTERVAL)]
                public static bool IsActive => Mediator.Ads.Interval.IsActive;
                [J<PERSON><PERSON><PERSON>atch(nameof(RemainingTime), NAME_GROUP_INTERVAL)]
                public static float RemainingTime => Mediator.Ads.Interval.RemainingTime;
            }
        } */

        public static class Internet
        {
            private const string NAME = nameof(Internet);
            private const string NAME_PATH = MEDIATOR_NAME + "." + NAME + ".";
            private const string NAME_GROUP = MEDIATOR_NAME + "/" + NAME;

            [JahroCommand(NAME_PATH + nameof(SetForceAccessToInternet), NAME_GROUP)]
            public static void SetForceAccessToInternet(bool isForce)
            {
                ProjectContext
                    .Instance.Container.Resolve<UnifiedServicesSettings>()
                    .GetMediator<InternetSettings>()
                    .ForceAccessToInternet = isForce;
            }
        }
    }
}
#endif