#if !PROD
namespace Content.JahroConsoleRegister
{
    using Zenject;
    using JahroConsole;
    using ModulesInternal.Score;
    using UnityEngine.SceneManagement;
    // using ModulesInternal.Score.Settings;
    using Modules.Shared.PlatformServices.Mediator.Vibration;


    public class ScoreRegisterConsole
    {
        public const string MAIN_NAME = "Score";

        public static class Settings
        {
            private const string NAME = nameof(Settings);
            private const string NAME_PATH = MAIN_NAME + "." + NAME + ".";
            private const string NAME_GROUP = MAIN_NAME + "/" + NAME;

/*             private static ScoreLevelSettings _scoreLevelSettings = null;

            private static ScoreLevelSettings ScoreLevelSettings =>
                _scoreLevelSettings ??= ProjectContext.Instance.Container
                    .Resolve<SceneContextRegistry>()
                    ?.TryGetContainerForScene(SceneManager.GetActiveScene())
                    ?.Resolve<ScoreLevelService>()
                    ?.Settings; */


            /*[JahroWatch(nameof(MissVibration), NAME_GROUP)]
            public static HapticType MissVibration => ScoreLevelSettings.MissVibration;


            [JahroCommand(NAME_PATH + nameof(SetMissVibration), NAME_GROUP)]
            public static void SetMissVibration(HapticType hapticType)
            {
                if(ScoreLevelSettings)
                    ScoreLevelSettings.MissVibration = hapticType;
            }

            [JahroCommand(NAME_PATH + nameof(SetScoreMissTap), NAME_GROUP)]
            public static void SetScoreMissTap(int scoreMissTap)
            {
                if(ScoreLevelSettings)
                    ScoreLevelSettings.ScoreMissTap = scoreMissTap;
            }

            [JahroCommand(NAME_PATH + nameof(SetCountMissLimit), NAME_GROUP)]
            public static void SetCountMissLimit(int countMissLimit)
            {
                if(ScoreLevelSettings)
                    ScoreLevelSettings.CountMissLimit = countMissLimit;
            }*/
        }
    }
}
#endif