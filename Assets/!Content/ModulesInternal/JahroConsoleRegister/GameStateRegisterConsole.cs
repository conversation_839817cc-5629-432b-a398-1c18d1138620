#if !PROD
namespace Content.JahroConsoleRegister
{
    using State;
    using JahroConsole;


    public static class GameStateRegisterConsole
    {
        public const string NAME = "GameState";

        [<PERSON><PERSON><PERSON><PERSON><PERSON>(nameof(IsChanged), NAME)]
        public static bool IsChanged => GameStateService.IsChanged;
        [<PERSON><PERSON><PERSON><PERSON><PERSON>(nameof(Current), NAME)]
        public static GameState Current => GameStateService.Current;
        [<PERSON><PERSON><PERSON><PERSON><PERSON>(nameof(Previous), NAME)]
        public static GameState Previous => GameStateService.Previous;
    }
}
#endif