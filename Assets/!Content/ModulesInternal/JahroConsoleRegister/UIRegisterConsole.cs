#if !PROD
namespace Content.JahroConsoleRegister
{
    using System;
    using Zenject;
    using Modules.UI;
    using JahroConsole;
    using UI.Screens.Informer;
    using UI.Screens.Informer.Settings;


    public static class UIRegisterConsole
    {
        private const string MAIN_NAME = "UI";
        private const string NAME_PATH = MAIN_NAME + ".";


        public static class Screen
        {
            private const string NAME = nameof(Screen);
            private const string PATH = NAME_PATH + NAME + ".";
            private const string GROUP = MAIN_NAME + "/" + NAME;

            public static UiService UiService => ProjectContext.Instance.Container.Resolve<UiService>();


            [JahroCommand(PATH + nameof(Show), GROUP)]
            public static void Show(string typeScreen, bool immediately = false)
            {
                var type = GetTypeByString(typeScreen);

                if(type != null)
                    UiService.Show(type, immediately: immediately);
            }

            [J<PERSON><PERSON><PERSON>ommand(PATH + nameof(Hide), GROUP)]
            public static void Hide(string typeScreen, bool immediately = false)
            {
                var type = GetTypeByString(typeScreen);

                if(type != null)
                    UiService.Hide(type, immediately: immediately);
            }

            private static Type GetTypeByString(string typeScreen)
            {
                string fullName = $"Content.UI.Screens.{typeScreen}";
                return Type.GetType(fullName);
            }
        }

        public static class Informer
        {
            private const string NAME = nameof(Informer);
            private const string PATH = NAME_PATH + NAME + ".";
            private const string GROUP = MAIN_NAME + "/" + NAME;


            [JahroCommand(PATH + nameof(Show), GROUP)]
            public static async void Show(InformerPreset informerPreset, bool immediately = false)
            {
                var informer = await Screen.UiService.ShowAsync<InformerScreen>(immediately: immediately);
                informer.ApplyPreset(informerPreset);
                informer.AddEventOptionalControl(OptionalControl.All, () =>
                {
                    Screen.UiService.Hide<InformerScreen>(immediately: immediately);
                });
            }
        }
    }
}
#endif