#if !PROD
namespace Content.JahroConsoleRegister
{
    using Zenject;
    using ColorSystem;
    using JahroConsole;
    using Modules.ColorSystem;
    using UnityEngine.SceneManagement;


    public static class ColorRegisterConsole
    {
        public const string MAIN_NAME = "Color";
        private const string NAME_PATH = MAIN_NAME + ".";



        private static DiContainer Container =>
            ProjectContext.Instance.Container
                .Resolve<SceneContextRegistry>()
                .TryGetContainerForScene(SceneManager.GetActiveScene()) ?? ProjectContext.Instance.Container;


        private static ColorService ColorService => Container.Resolve<ColorService>();


        [JahroCommand("Color >", MAIN_NAME)]
        public static void NextColor()
        {
            var countPattern = ColorService.ColorsSettings.Patterns.Count;
            PatternColorBase pattern = null;

            for (int i = 0; i < countPattern; i++)
            {
                if ( ColorService.ColorChangerService.CurrentPattern.ID == ColorService.ColorsSettings.Patterns[i].ID )
                {
                    if ( i != countPattern - 1 )
                    {
                        pattern = ColorService.ColorsSettings.Patterns[i + 1];
                        break;
                    }

                    return;
                }
            }

            ColorService.ColorChangerService.ChangePattern(pattern);
        }

        [JahroCommand("Color <", MAIN_NAME)]
        public static void PrevColor()
        {
            var countPattern = ColorService.ColorsSettings.Patterns.Count;
            PatternColorBase pattern = null;

            for (int i = 0; i < countPattern; i++)
            {
                if ( ColorService.ColorChangerService.CurrentPattern.ID == ColorService.ColorsSettings.Patterns[i].ID )
                {
                    if ( i != 0 )
                    {
                        pattern = ColorService.ColorsSettings.Patterns[i - 1];
                        break;
                    }

                    return;
                }
            }

            ColorService.ColorChangerService.ChangePattern(pattern);
        }
    }
}
#endif