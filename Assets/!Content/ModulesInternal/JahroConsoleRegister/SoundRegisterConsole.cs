namespace Content.JahroConsoleRegister
{
    using Sound;
    using Zenject;
    using <PERSON><PERSON><PERSON><PERSON>ons<PERSON>;


    public static class SoundRegisterConsole
    {
        // public static bool isSecondMusic = false;
        public const string MAIN_NAME = "Sound";
        private const string NAME_PATH = MAIN_NAME + ".";

        public static SoundService SoundService => ProjectContext.Instance.Container.Resolve<SoundService>();


        [J<PERSON><PERSON><PERSON>ommand(NAME_PATH + nameof(SetVolume), MAIN_NAME)]
        public static void SetVolume(ClipType clipType, float volume)
        {
            if ( SoundService.Preset.TryGetConfig(clipType, out var info) )
                info.VolumeScale = volume;
        }

        [JahroCommand(NAME_PATH + nameof(GetVolume), MAIN_NAME)]
        public static float GetVolume(ClipType clipType)
        {
            if ( SoundService.Preset.TryGetConfig(clipType, out var info) )
                return info.VolumeScale;

            return -1f;
        }

        /*[JahroCommand(NAME_PATH + nameof(SetSecondMusic), MAIN_NAME)]
        public static void SetSecondMusic(bool isSecond)
        {
            isSecondMusic = isSecond;
        }*/
    }
}