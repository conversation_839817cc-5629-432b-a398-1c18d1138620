#if !PROD
namespace Content.JahroConsoleRegister
{
    using State;
    using Zenject;
    using Locations;
    using Modules.UI;
    using SceneLoader;
    using JahroConsole;
    using LocationFlowPipeline;
    using UI.Screens.MainMenu;
    using LocationFlowPipeline.Data;
    using System.Runtime.CompilerServices;
    using Modules.Shared.GlobalServices.Level;
    using Modules.Shared.GlobalServices.Saver;


    public static class LocationRegisterConsole
    {
        public const string NAME = "Location";
        public const string NAME_PATH = NAME + ".";

        // private static LocationCollectionData _data;

        // [JahroWatch(nameof(Current), NAME)]
        // public static PassagePhase Current => GetCurrentPassagePhase();

        // private static PassagePhase GetCurrentPassagePhase()
        // {
        //     _data ??= StorageService.Get<LocationFlowListData>();
        //     return _data.LocationDatas.TryGetValue(LevelService.Current, out var locationData) ? locationData.PassagePhase : 0;
        // }

        private static void Log(string name, string message, [CallerMemberName] string methodName = "")
        {
            Jahro.Log($">> [{name}][{methodName}] {message}");
        }

        /*[JahroCommand(NAME_PATH + nameof(LoadLevel), NAME, "Loading only in the main menu")]
        public static void LoadLevel(int level)
        {
            if ( !GameStateService.IsChanged || !GameStateService.Current.IsMainMenu() )
            {
                Log(NAME, "Loading only in the main menu");
                return;
            }

            var sceneLoaderService = ProjectContext.Instance.Container.Resolve<SceneLoaderService>();
            sceneLoaderService.OpenLocation(LocationIndex.FromLevel(level), () =>
            {
                var uiService = ProjectContext.Instance.Container.Resolve<UiService>();
                uiService.Hide<MainMenuScreen>(immediately: true);
            });
        }*/
    }
}
#endif