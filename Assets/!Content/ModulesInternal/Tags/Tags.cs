namespace Content.ModulesInternal.Tags
{
    using Modules.Shared.TagCollection;


    public class Tags : TagsBake
    {
        public static class Analytics
        {
            public static class User
            {
                public const string CURRENT_LEVEL = "current_level";
                public const string MODE = "mode";
            }

            public static class Event
            {
                public const string LEVEL_RESET = "level_reset";
            }

            public static class Parameter
            {
                public const string COUNT_PLAY = "count_play";
                public const string SCORE_TIER = "score_tier";
                public const string MAX_COMBO = "max_combo";
                public const string MAX_LINES = "max_lines";
            }
        }

        public static class Ads
        {
            public static class Reward
            {
                public const string REVIVE = "revive";
            }

            public static class Interstitial
            {
                public const string PLAYING_TO_MAIN_MENU = "playing_to_main_menu";
                public const string REPLAY = "replay";
                public const string BEFORE_WIN = "before_win";
                public const string BEFORE_LOSE = "before_lose";
                public const string AFTER_WIN = "after_win";
                public const string AFTER_LOSE = "after_lose";
            }
        }

        public static class Payments
        {
            public const string NO_ADS = "noads";
        }

        public static class Achievements
        {
            public const string COMPLETE = "complete";
            public const string CAT = "cat";
            public const string ITEM = "item";
            public const string POINT = "point";
        }

        public static class Config
        {
            public static class Internet
            {
                public const string FORCE_ACCESS_TO_INTERNET = "force_access_to_internet";
            }

            public static class Camera
            {
                public const string CAMERA_SETTINGS = "camera_settings";
            }

            public static class ScoreLevel
            {
                public const string SCORE_LEVEL_SETTINGS = "score_level_settings";
                public const string COUNT_MISS_LIMIT = "count_miss_limit";
                public const string SCORE_MISS_TAP = "score_miss_tap";
                public const string MISS_VIBRATION = "miss_vibration";
                public const string SUBJECTS_SCORE = "subjects_score";
            }

            public static class SearchSubject
            {
                public const string SEARCH_SUBJECT_SETTINGS = "search_subject_settings";
                public const string PERIOD_POINTS_FOR_SYNC_SCORE = "period_points_for_sync_score";
            }

            public static class Screen
            {
                public static class Gameplay
                {
                    public static class ButtonHint
                    {
                        public const string TIME_REVEAL = "time_reveal";
                        public const string TIME_INTERVAL = "time_interval";
                    }

                    public const string GAMEPLAY_SETTINGS = "gameplay_settings";
                    public const string BUTTON_HINT = "button_hint";
                }
            }

            public static class Location
            {
                public static class Metadata
                {
                    public const string LOCATION_METADATA = "location_metadata";
                    public const string OVERRIDE_LOCATION_STATES = "override_location_states";
                    public const string LEVEL = "level";
                    public const string STATE = "state";
                }

                public static class Visual
                {
                    public const string LOCATION_VISUAL = "location_visual";
                    public const string PRESETS = "presets";
                    public const string PRESET_INDEX = "preset_index";
                }
            }

            public static class Social
            {
                public const string SOCIAL_SETTINGS = "social_settings";
                public const string TELEGRAM = "telegram";
                public const string YOUTUBE = "youtube";
                public const string TIKTOK = "tiktok";
            }

            public static class Revive
            {
                public const string REVIVE_SETTINGS = "revive_settings";
                public const string TIME_REVIVE = "time_revive";
                public const string ACTIVATION_SCORE_LIMIT = "activation_score_limit";
                public const string DELAY_SHOW_BUTTON_REVIVE = "delay_show_button_revive";
            }

            public static class Notifications
            {
                public const string NOTIFICATIONS_SETTINGS = "notifications_settings";
                public const string INTERVALS = "intervals";
                public const string ENABLED = "enabled";
            }

            public static class Slot
            {
                public const string SLOT_SETTINGS = "slot_settings";
                public const string OFFSET_MOVER = "offset_mover";
                public const string DYNAMIC_OFFSET_MULTIPLAYER = "dynamic_offset_multiplayer";
                public const string MAX_DYNAMIC_OFFSET = "max_dynamic_offset";
            }
        }
    }
}