namespace Content.EntryPoint
{
    using System;
    using UnityEngine;
    using PlatformServices;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.PlatformServices.ServiceProvider;


    public sealed class LaunchTimeAnalytic
    {
        public static LaunchTimeAnalytic Instance { get; private set; }

        private float _startTime, _startEntryPoint, _startLoadTime, _startMainMenu, _startGameReady;
        private bool _isDownload;
        private bool _isEntryPoint, _isLoadScreen, _isMainMenu, _isGameReady;


        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void Initialize()
        {
            Instance = new LaunchTimeAnalytic();
        }

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void LogBeforeSceneLoad()
        {
            Instance._startTime = Time.realtimeSinceStartup;
        }

        public void LogEntryPoint()
        {
            Mediator.Game.OnGameReady += OnGameReady;

            RealtimeSinceStartup(value =>
            {
                _startEntryPoint = value;
                _isEntryPoint = true;
            }).Forget();
        }

        private void OnGameReady()
        {
            Mediator.Game.OnGameReady -= OnGameReady;

            RealtimeSinceStartup(value =>
            {
                _startGameReady = value;
                _isGameReady = true;
            }).Forget();
        }

        public void LogLoadScreen()
        {
            RealtimeSinceStartup(value =>
            {
                _startLoadTime = value;
                _isLoadScreen = true;
            }).Forget();
        }

        public void LogMainMenu()
        {
            RealtimeSinceStartup(value =>
            {
                _startMainMenu = value;
                _isMainMenu = true;
            }).Forget();
        }

        private async UniTaskVoid RealtimeSinceStartup(Action<float> onAvailable)
        {
            await UniTask.SwitchToMainThread();
            onAvailable(Time.realtimeSinceStartup);
            CheckLogEvent();
        }

        public void SetDownload()
        {
            _isDownload = true;
        }

        private void CheckLogEvent()
        {
            if ( _isEntryPoint & _isLoadScreen & _isMainMenu & _isGameReady )
            {
                var timeToBeforeSceneLoad = _startTime;
                var timeToEntryPoint = _startEntryPoint - timeToBeforeSceneLoad;
                var timeToLoadScreen = _startLoadTime - _startEntryPoint;
                var timeToMainMenu = _startMainMenu - _startLoadTime;
                var timeToGameReady = _startGameReady - timeToBeforeSceneLoad;

                Mediator.Analytics.LogEvent("launch_time", new []
                {
                    new Parameter("downloaded_bundle", _isDownload),
                    new Parameter("time_before_load_01", timeToBeforeSceneLoad),
                    new Parameter("time_entry_point_02", timeToEntryPoint),
                    new Parameter("time_load_screen_03", timeToLoadScreen),
                    new Parameter("time_main_menu_04", timeToMainMenu),
                    new Parameter("time_to_game_ready", timeToGameReady),
                });

                Instance = null;
            }
        }
    }
}