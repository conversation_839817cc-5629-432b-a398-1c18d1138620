namespace Content.EntryPoint
{
    using State;
    using Zenject;
    using UI.Screens;
    using Modules.UI;
    using PrimeTween;
    using SceneLoader;
    using UnityEngine;
    using PlatformServices;
    using Cysharp.Threading.Tasks;
    using Modules.Shared.EntryPoint;
    using Modules.Shared.GlobalServices;
    using Content.ModulesInternal.Score;
    using Modules.Shared.PlatformServices;
    using Modules.Shared.GlobalServices.Level;
    using UnityEngine.ResourceManagement.AsyncOperations;


    public class BootstrapEntryPoint : BootstrapEntryPointBase
    {
        // private readonly VolumeLabelLoader.PieceLabelAggregate _loadMainMenuMetadata = new(100, "load_mainMenu");
        // private readonly VolumeLabelLoader.PieceLabelAggregate _loadMainMenu = new(30, "load_mainMenu");

        // private const string MAIN_MENU_SCREEN_KEY = "MainMenuScreen";
        // private const int DELAY_AFTER_LOAD_MAIN_MENU = 300;

        // private AsyncOperationHandle<GameObject> _mainMenuScreenHandle;
        private AutoResetUniTaskCompletionSource _completionLoadScreen;
        private LoadBootstrapScreenBase _loadScreen;
        private UiService _uiService;


        public override async UniTask Execute()
        {
            PrimeTweenConfig.SetTweensCapacity(300);

            await InitializerProvider.Define();

            GameStateService.SetChanging(GameState.PreInitialize, true);
            CustomDebug.LogModule("Starting", nameof(BootstrapEntryPoint), this);
            LaunchTimeAnalytic.Instance.LogEntryPoint();

            await DiInjectionObjects(false);
            InitializeLoadScreen().Forget();

            await Mediator.PreInitialize();

            GameStateService.SetChanging(GameState.Initialize, true);
            await _completionLoadScreen.Task;

            await Mediator.PostInitialize();

            PrepareLevel();

            await DiInitializeObjects();

            LaunchTimeAnalytic.Instance.LogMainMenu();

            // await LoadMainMenu();

            await HideProgressLoad();

            await LoadNextScene();
        }

        private static void PrepareLevel()
        {
            LevelService.Load();

            if ( LevelService.Current < 0 )
                LevelService.Current = 0;
        }

        private UniTask HideProgressLoad()
        {
            var completion = AutoResetUniTaskCompletionSource.Create();
            _loadScreen.HideProgress(() => completion.TrySetResult());
            return completion.Task;
        }

        private async UniTask InitializeLoadScreen()
        {
            LaunchTimeAnalytic.Instance.LogLoadScreen();

            _uiService = ProjectContext.Instance.Container.Resolve<UiService>();
            _completionLoadScreen = AutoResetUniTaskCompletionSource.Create();

            _loadScreen = await _uiService.ShowAsync<
#if UNITY_WEBGL
                LoadBootstrapScreen
#else
                LoadBootstrapLogoScreen
#endif
            >(_ => _completionLoadScreen.TrySetResult());

            _loadScreen.Loader.BindPieceAggregate(Mediator.GetPieceLoader());
            // _loadScreen.Loader.BindPieceAggregate(_loadMainMenuMetadata);
            // _loadScreen.Loader.BindPieceAggregate(_loadMainMenu);
        }

/*         private async UniTask LoadMainMenu()
        {
            // await LoadMainMenuMetadata();

            await _loadMainMenu.UpdateLabel();
            _loadMainMenu.StartProgress();

            var downloadSize = await Addressables.GetDownloadSizeAsync(MAIN_MENU_SCREEN_KEY).ToUniTask();

            if (downloadSize > 0 )
            {
                LaunchTimeAnalytic.Instance.SetDownload();

                _mainMenuScreenHandle = Addressables.LoadAssetAsync<GameObject>(MAIN_MENU_SCREEN_KEY);

                while (!_mainMenuScreenHandle.IsDone)
                {
                    await UniTask.NextFrame();
                    var status = _mainMenuScreenHandle.GetDownloadStatus();

                    _loadMainMenu.SetProgressLoader(status.Percent);

                    _loadMainMenu.SetLabelInner(status);
                }

                _loadMainMenu.SetLabelInner(string.Empty);
            }

            _loadMainMenu.SetProgressLoader(1f);
            await UniTask.Delay(DELAY_AFTER_LOAD_MAIN_MENU);
        } */

        /*private async UniTask LoadMainMenuMetadata()
        {
            await _loadMainMenuMetadata.UpdateLabel();
            _loadMainMenuMetadata.StartProgress();

            var locationsService = ProjectContext.Instance.Container.Resolve<LocationsService>();
            var isDownload = await locationsService.LoadLocationMetadata(status =>
            {
                _loadMainMenuMetadata.SetProgressLoader(status.Percent);
                _loadMainMenuMetadata.SetLabelInner(status);
            });

            if(isDownload)
                LaunchTimeAnalytic.Instance.SetDownload();
        }*/

        private async UniTask LoadNextScene()
        {
            var isClear = ProjectContext.Instance.Container.Resolve<ScoreService>().ScoreData.IsClear;
            var sceneLoader = ProjectContext.Instance.Container.Resolve<SceneLoaderService>();

            if(isClear)
            {
                GameStateService.SetChanging(GameState.MainMenu, true);
                await sceneLoader.OpenClassicAsync();
                Release();
            }
            else
            {
                await sceneLoader.OpenMainMenuAsync(Release);
            }
        }

        private void Release()
        {
            _uiService.StartPreload().Forget();
            _uiService.Hide(_loadScreen.GetType(), Mediator.Game.GameReady, true);

            // if(_mainMenuScreenHandle.IsValid())
            //     Addressables.Release(_mainMenuScreenHandle);

            CustomDebug.LogModule("Finish", nameof(BootstrapEntryPoint), this);
        }
    }
}