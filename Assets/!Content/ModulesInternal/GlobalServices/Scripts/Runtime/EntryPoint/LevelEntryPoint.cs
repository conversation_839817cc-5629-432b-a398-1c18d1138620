namespace Modules.Shared.GlobalServices.EntryPoint
{
    using Zenject;
    using UnityEngine;
    using Content.Sound;
    using Shared.EntryPoint;
    using Cysharp.Threading.Tasks;
    using Content.LocationFlowPipeline;


    [DefaultExecutionOrder(-1)]
    public class LevelEntryPoint : SceneEntryPointBase
    {
        private SoundService _soundService;
        private LocationFlowServiceBase _locationFlowService;


        [Inject]
        public void Construct(SoundService soundService, LocationFlowServiceBase locationFlowService)
        {
            _soundService = soundService;
            _locationFlowService = locationFlowService;
        }

        protected override async UniTask ExecuteInternal()
        {
            await DiInjectionObjects();

            _soundService.LoadClipPack(ClipType.SetShape);
            _soundService.LoadClipPack(ClipType.Pop);
            _soundService.LoadClipPack(ClipType.ShapeOutSlot);
            _soundService.LoadClipPack(ClipType.ShapeInSlot);

            await _locationFlowService.InitializeAsync();
        }

        private void OnDestroy()
        {
            _soundService.ReleaseClipPack(ClipType.SetShape);
            _soundService.ReleaseClipPack(ClipType.Pop);
            _soundService.ReleaseClipPack(ClipType.ShapeOutSlot);
            _soundService.ReleaseClipPack(ClipType.ShapeInSlot);
        }
    }
}