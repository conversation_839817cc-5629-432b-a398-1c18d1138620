namespace Modules.Shared.GlobalServices.EntryPoint
{
    using Zenject;
    using Modules.UI;
    using Content.State;
    using Shared.EntryPoint;
    using Cysharp.Threading.Tasks;
    using Content.UI.Screens.MainMenu;


    public class MainMenuEntryPoint : SceneEntryPointBase
    {
        private UiService _uiService;


        [Inject]
        public void Construct(UiService uiService)
        {
            _uiService = uiService;
        }

        protected override async UniTask ExecuteInternal()
        {
            await DiInjectionObjects();

            if(!GameStateService.Current.IsMainMenu())
                GameStateService.SetChanging(GameState.MainMenu);

            await _uiService.ShowAsync<MainMenuScreen>(_ => GameStateService.SetChanged(GameState.MainMenu));
        }
    }
}