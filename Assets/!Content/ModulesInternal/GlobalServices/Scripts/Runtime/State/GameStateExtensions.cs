namespace Content.State
{
    using System.Runtime.CompilerServices;

    public static class GameStateExtensions
    {
        private const MethodImplOptions INLINE = MethodImplOptions.AggressiveInlining;


        [MethodImpl( INLINE )]
        public static bool IsPreInitialize(this GameState state) =>
            (state & (GameState.PreInitialize)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsInitialize(this GameState state) =>
            (state & (GameState.Initialize)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsFirstToMainMenu(this GameState from, GameState to) =>
            (from & (GameState.Initialize)) != 0 && (to & (GameState.MainMenu)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsMainMenu(this GameState state) =>
            (state & (GameState.MainMenu)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsTutorial(this GameState state) =>
            (state & (GameState.Tutorial)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsPlaying(this GameState state) =>
            (state & (GameState.Playing)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsPause(this GameState state) =>
            (state & (GameState.Pause)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsInMeta(this GameState state) =>
            (state & (GameState.InMeta)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsInGameplay(this GameState state) =>
            (state & (GameState.InGameplay)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsPlay(this GameState from, GameState to) =>
            (from & (GameState.MainMenu | GameState.Tutorial)) != 0 && (to & (GameState.Playing)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsReplay(this GameState state) =>
            (state & (GameState.Replay)) != 0;

        [MethodImpl( INLINE )]
        public static bool IsInStart(this GameState state) =>
            (state & (GameState.InStart)) != 0;
    }
}