namespace Content.State
{
    using System;

    [Flags]
    public enum GameState
    {
        None = 0,

        PreInitialize = 1 << 3, // -> Initialize
        Initialize = 1 << 5, // -> MainMenu

        MainMenu = 1 << 10, // -> Playing || -> Pause || -> Tutorial

        Tutorial = 1 << 20, //  -> Playing || -> MainMenu
        Playing = 1 << 25, // -> MainMenu || -> Pause
        Pause = 1 << 30, // -> MainMenu || -> Playing || -> Replay
        GameOver = 1 << 35, // -> Replay
        Replay = 1 << 40, // -> Playing

        InStart = PreInitialize | Initialize,
        InMeta = PreInitialize | Initialize | MainMenu,
        InGameplay = Tutorial | Playing | Pause,
        All = PreInitialize | Initialize | MainMenu | Tutorial | Playing | Pause
    }
}