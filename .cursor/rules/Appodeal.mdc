---
description: Appodeal
globs:
alwaysApply: false
---
# Initialize SDK

Before loading and displaying ads, you need to initialize Appodeal SDK, as follows:

    Import Namespaces

    UPM Distribution
    Manual Distribution

using AppodealStack.Monetization.Api;
using AppodealStack.Monetization.Common;

    Call Initialization Method

Add the following code snippet to Start() (or whatever you want) method of your main scene’s MonoBehaviour

    UPM Distribution
    Manual Distribution

class Test : MonoBehaviour
{
    private void Start()
    {
        int adTypes = AppodealAdType.Interstitial | AppodealAdType.Banner | AppodealAdType.RewardedVideo | AppodealAdType.Mrec;
        string appKey = "YOUR_APPODEAL_APP_KEY";
        AppodealCallbacks.Sdk.OnInitialized += OnInitializationFinished;
        Appodeal.Initialize(appKey, adTypes);
    }

    public void OnInitializationFinished(object sender, SdkInitializedEventArgs e) {}
}

Make sure to replace YOUR_APPODEAL_APP_KEY with the actual app key.

Use the type codes below to set the preferred ad format:

    AppodealAdType.Interstitial for interstitial.
    AppodealAdType.RewardedVideo for rewarded videos.
    AppodealAdType.Banner for banners.
    AppodealAdType.Mrec for 300*250 banners.

Useful Tip:

Ad types can be combined using | operator. For example, AppodealAdType.Interstitial | AppodealAdType.RewardedVideo.

Initialize only those ad types you want to use in your app to avoid getting ad requests to unused ones.






# Interstitial

Interstitial ads are full-screen advertisements. In Appodeal, they are divided into two types - static interstitial and video interstitial.

These types of ads are requested simultaneously during caching. If both of them filled an ad, the most expensive of the two will be shown.

Static interstitial - static full-screen banners.
Video interstitial - these are videos that the user can usually close 5 seconds after the start of viewing.

You can use our demo app as a reference project.

Demo App
Check If Ad Is Loaded

You can check whether or not an ad is loaded at a certain moment. This method returns a boolean value, representing the interstitial ad loading status.

    UPM Distribution
    Manual Distribution

Appodeal.IsLoaded(AppodealAdType.Interstitial);

Check Ad Loading Status

We recommend you always check whether an ad is available before trying to show it.

Example:

    UPM Distribution
    Manual Distribution

if(Appodeal.IsLoaded(AppodealAdType.Interstitial)) {
	Appodeal.Show(AppodealShowStyle.Interstitial);
}

Display

To show an interstitial ad, you need to call the following method:

    UPM Distribution
    Manual Distribution

Appodeal.Show(AppodealShowStyle.Interstitial);

Manual Caching

If you need more control of interstitial ads loading use manual caching. Manual caching for Interstitial can be useful to improve display rate or decrease SDK loads when several ad types are cached.

    To disable automatic caching for interstitials, use the code below before the SDK initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetAutoCache(AppodealAdType.Interstitial, false);

    To cache Interstitial ad manually use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.Cache(AppodealAdType.Interstitial);

Callbacks

The callbacks are used to track different events in the lifecycle of an ad, e.g. when an ad was clicked on or closed. Follow the steps below to implement them:

    UPM Distribution
    Manual Distribution

Subscribe to the desired interstitial event using one of the options from this guide. (you can subscribe to any number of events you want)

AppodealCallbacks.Interstitial.OnLoaded += (sender, args) => { };

You will find all existing interstitial events in the example below:

public void SomeMethod()
{
	AppodealCallbacks.Interstitial.OnLoaded += OnInterstitialLoaded;
	AppodealCallbacks.Interstitial.OnFailedToLoad += OnInterstitialFailedToLoad;
	AppodealCallbacks.Interstitial.OnShown += OnInterstitialShown;
	AppodealCallbacks.Interstitial.OnShowFailed += OnInterstitialShowFailed;
	AppodealCallbacks.Interstitial.OnClosed += OnInterstitialClosed;
	AppodealCallbacks.Interstitial.OnClicked += OnInterstitialClicked;
	AppodealCallbacks.Interstitial.OnExpired += OnInterstitialExpired;
}

#region InterstitialAd Callbacks

// Called when interstitial was loaded (precache flag shows if the loaded ad is precache)
private void OnInterstitialLoaded(object sender, AdLoadedEventArgs e)
{
	Debug.Log("Interstitial loaded");
}

// Called when interstitial failed to load
private void OnInterstitialFailedToLoad(object sender, EventArgs e)
{
	Debug.Log("Interstitial failed to load");
}

// Called when interstitial was loaded, but cannot be shown (internal network errors, placement settings, etc.)
private void OnInterstitialShowFailed(object sender, EventArgs e)
{
	Debug.Log("Interstitial show failed");
}

// Called when interstitial is shown
private void OnInterstitialShown(object sender, EventArgs e)
{
	Debug.Log("Interstitial shown");
}

// Called when interstitial is closed
private void OnInterstitialClosed(object sender, EventArgs e)
{
	Debug.Log("Interstitial closed");
}

// Called when interstitial is clicked
private void OnInterstitialClicked(object sender, EventArgs e)
{
	Debug.Log("Interstitial clicked");
}

// Called when interstitial is expired and can not be shown
private void OnInterstitialExpired(object sender, EventArgs e)
{
	Debug.Log("Interstitial expired");
}

#endregion

Unity Main Thread

All callbacks are called on native main threads that do not match the main thread of the Unity. If you need to receive callbacks in the main Unity thread follow our Callback Usage Guide.
Placements

Appodeal SDK allows you to tag each impression with different placement. To use placements, you need to create placements in Appodeal Dashboard. Read more about placements.

To show an ad with placement, you have to call show method with specifying placement's name:

    UPM Distribution
    Manual Distribution

Appodeal.Show(AppodealShowStyle.Interstitial, "placementName");

If the loaded ad can’t be shown for a specific placement, nothing will be shown. If auto caching is enabled, sdk will start to cache another ad, which can affect display rate. To save the loaded ad for future use (for instance, for another placement) check if the ad can be shown before calling show method:

    UPM Distribution
    Manual Distribution

if(Appodeal.CanShow(AppodealAdType.Interstitial, "placementName")) {
	Appodeal.Show(AppodealShowStyle.Interstitial, "placementName");
}

You can configure your impression logic for each placement.

If you have no placements, or call Appodeal.Show() method with a placement that does not exist, the impression will be tagged with default placement name and its settings will be applied.
Placement settings affect ONLY ad presentation, not loading or caching.
Get Predicted eCPM

This method returns expected eCPM for a currently cached advertisement. The amount is calculated based on historical data for the current ad unit.

    UPM Distribution
    Manual Distribution

Appodeal.GetPredictedEcpm(AppodealAdType.Interstitial);

Mute Video Ads
This method will take effect only on Android platform

You can mute video ads if calls are muted on the device. For muting you need to call the following method before initializing the SDK.

    UPM Distribution
    Manual Distribution

Appodeal.MuteVideosIfCallsMuted(true);





# Rewarded Video

Rewarded video is a user-initiated ad type. It allows end-users to get in-app rewards or other benefits in exchange for viewing a video ad.

You can use our demo app as a reference project.

Demo App
Check If Ad Is Loaded

You can check whether or not an ad is loaded at a certain moment. This method returns a boolean value, representing the rewarded video ad loading status.

    UPM Distribution
    Manual Distribution

Appodeal.IsLoaded(AppodealAdType.RewardedVideo);

Check Ad Loading Status

We recommend you always check whether an ad is available before trying to show it.

Example:

    UPM Distribution
    Manual Distribution

if(Appodeal.IsLoaded(AppodealAdType.RewardedVideo)) {
	Appodeal.Show(AppodealShowStyle.RewardedVideo);
}

Display

To show a rewarded video ad, you need to call the following method:

    UPM Distribution
    Manual Distribution

Appodeal.Show(AppodealShowStyle.RewardedVideo);

Manual Caching

If you need more control of rewarded video ads loading use manual caching. Manual caching for rewarded video can be useful to improve display rate or decrease SDK loads when several ad types are cached.

    To disable automatic caching for rewarded video, use the code below before the SDK initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetAutoCache(AppodealAdType.RewardedVideo, false);

    To cache rewarded video ad manually use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.Cache(AppodealAdType.RewardedVideo);

Callbacks

The callbacks are used to track different events in the lifecycle of an ad, e.g. when an ad was clicked on or closed. Follow the steps below to implement them:

    UPM Distribution
    Manual Distribution

Subscribe to the desired rewarded video event using one of the options from this guide. (you can subscribe to any number of events you want)

AppodealCallbacks.RewardedVideo.OnLoaded += (sender, args) => { };

You will find all existing rewarded video events in the example below:

public void SomeMethod()
{
	AppodealCallbacks.RewardedVideo.OnLoaded += OnRewardedVideoLoaded;
	AppodealCallbacks.RewardedVideo.OnFailedToLoad += OnRewardedVideoFailedToLoad;
	AppodealCallbacks.RewardedVideo.OnShown += OnRewardedVideoShown;
	AppodealCallbacks.RewardedVideo.OnShowFailed += OnRewardedVideoShowFailed;
	AppodealCallbacks.RewardedVideo.OnClosed += OnRewardedVideoClosed;
	AppodealCallbacks.RewardedVideo.OnFinished += OnRewardedVideoFinished;
	AppodealCallbacks.RewardedVideo.OnClicked += OnRewardedVideoClicked;
	AppodealCallbacks.RewardedVideo.OnExpired += OnRewardedVideoExpired;
 }

#region RewardedVideoAd Callbacks

//Called when rewarded video was loaded (precache flag shows if the loaded ad is precache).
private void OnRewardedVideoLoaded(object sender, AdLoadedEventArgs e)
{
	Debug.Log($"[APDUnity] [Callback] OnRewardedVideoLoaded(bool isPrecache:{e.IsPrecache})");
}

// Called when rewarded video failed to load
private void OnRewardedVideoFailedToLoad(object sender, EventArgs e)
{
	Debug.Log("[APDUnity] [Callback] OnRewardedVideoFailedToLoad()");
}

// Called when rewarded video was loaded, but cannot be shown (internal network errors, placement settings, etc.)
private void OnRewardedVideoShowFailed(object sender, EventArgs e)
{
	Debug.Log("[APDUnity] [Callback] OnRewardedVideoShowFailed()");
}

// Called when rewarded video is shown
private void OnRewardedVideoShown(object sender, EventArgs e)
{
	Debug.Log("[APDUnity] [Callback] OnRewardedVideoShown()");
}

// Called when rewarded video is closed
private void OnRewardedVideoClosed(object sender, RewardedVideoClosedEventArgs e)
{
	Debug.Log($"[APDUnity] [Callback] OnRewardedVideoClosed(bool finished:{e.Finished})");
}

// Called when rewarded video is viewed until the end
private void OnRewardedVideoFinished(object sender, RewardedVideoFinishedEventArgs e)
{
	Debug.Log($"[APDUnity] [Callback] OnRewardedVideoFinished(double amount:{e.Amount}, string name:{e.Currency})");

}

// Called when rewarded video is clicked
private void OnRewardedVideoClicked(object sender, EventArgs e)
{
	Debug.Log("[APDUnity] [Callback] OnRewardedVideoClicked()");
}

//Called when rewarded video is expired and can not be shown
private void OnRewardedVideoExpired(object sender, EventArgs e)
{
	Debug.Log("[APDUnity] [Callback] OnRewardedVideoExpired()");
}

#endregion

Unity Main Thread

All callbacks are called on native main threads that do not match the main thread of the Unity. If you need to receive callbacks in the main Unity thread follow our Callback Usage Guide.
Placements

Appodeal SDK allows you to tag each impression with different placement. To use placements, you need to create placements in Appodeal Dashboard. Read more about placements.

To show an ad with placement, you have to call show method with specifying placement's name:

    UPM Distribution
    Manual Distribution

Appodeal.Show(AppodealShowStyle.RewardedVideo, "placementName");

If the loaded ad can’t be shown for a specific placement, nothing will be shown. If auto caching is enabled, sdk will start to cache another ad, which can affect display rate. To save the loaded ad for future use (for instance, for another placement) check if the ad can be shown before calling show method:

    UPM Distribution
    Manual Distribution

if(Appodeal.CanShow(AppodealAdType.RewardedVideo, "placementName")) {
	Appodeal.Show(AppodealShowStyle.RewardedVideo, "placementName");
}

You can configure your impression logic for each placement.

If you have no placements, or call Appodeal.Show() method with a placement that does not exist, the impression will be tagged with default placement name and its settings will be applied.
Placement settings affect ONLY ad presentation, not loading or caching.
Server-to-Server Callbacks

To secure your apps economy we offer S2S reward callbacks. To validate each reward, you need to set up a callback URL on your server that will receive the reward information. We will pass the user data to your callback URL, which you will need to validate and adjust the user balance accordingly.

    Create the reward callback URL on your server that will receive the reward information.

    Fill the created URL and the encryption key in the app settings in your dashboard.

    The reward callback will be sent to your URL using GET request with two parameters:

{http:/example.com/reward}?data1={data1}&data2={data2}

    Your URL should decrypt the data and validate it.

    Check impression_id for uniqueness and store it in your system to prevent duplicate transactions.

To set user ID, use the following method before SDK initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetUserId("user#123");

We offer sample scripts in Go, PHP, Ruby, Java, Node.js, Python 3 and C# to decrypt the data. If you need samples in other languages, please contact our support team and we will provide them to you.

Sample in PHP: reward.php.

Sample in Ruby: reward.rb.

Sample in Java: reward.java.

Sample in Node.js: reward.js.

Sample in Python 3: reward.py.

Sample in C#: reward.cs.

Sample in Go: reward.go.
Getting Reward Data For A Specific Placement

To get reward data and notify your users of it before the video ad is shown, use this method. It returns KeyValuePair with the currency type and amount of the reward.

    UPM Distribution
    Manual Distribution

Appodeal.GetRewardParameters("placementName");

Get Predicted eCPM

This method returns expected eCPM for a currently cached advertisement. The amount is calculated based on historical data for the current ad unit.

    UPM Distribution
    Manual Distribution

Appodeal.GetPredictedEcpm(AppodealAdType.RewardedVideo);

Mute Video Ads
This method will take effect only on Android platform

You can mute video ads if calls are muted on the device. For muting you need to call the following method before initializing the SDK.

    UPM Distribution
    Manual Distribution

Appodeal.MuteVideosIfCallsMuted(true);






# Banner

Banner ads are classic static banners, which are usually located at the bottom or top of the screen.

Appodeal supports traditional 320x50 banners, tablet 728x90 ones, and adaptive banners (for Admob only) that adjust to the size and orientation of the device.
You can display only one banner view on the screen.

You can use our demo app as a reference project.

Demo App
Fixed Positioned Banner
Display

Banner ads are refreshed every 15 seconds automatically by default. To display banner, you need to call one of the following methods:

    UPM Distribution
    Manual Distribution

// Display banner at the bottom of the screen
Appodeal.Show(AppodealShowStyle.BannerBottom);

// Display banner at the top of the screen
Appodeal.Show(AppodealShowStyle.BannerTop);

// Display banner at the left of the screen
Appodeal.Show(AppodealShowStyle.BannerLeft);

// Display banner at the right of the screen
Appodeal.Show(AppodealShowStyle.BannerRight);

Hide Banner

To hide a banner that was shown via Appodeal.Show() method use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.Hide(AppodealAdType.Banner);

Check If Ad Is Loaded

You can check whether or not an ad is loaded at a certain moment. This method returns a boolean value, representing the banner ad loading status.

    UPM Distribution
    Manual Distribution

Appodeal.IsLoaded(AppodealAdType.Banner);

note

The Appodeal.Show() method for banners can be called at any moment. If there is no ad available, we will cache one and show it right away.
Callbacks

The callbacks are used to track different events in the lifecycle of an ad, e.g. when an ad was clicked on or closed. Follow the steps below to implement them:

    UPM Distribution
    Manual Distribution

Subscribe to the desired banner event using one of the options from this guide. (you can subscribe to any number of events you want)

AppodealCallbacks.Banner.OnLoaded += (sender, args) => { };

You will find all existing banner events in the example below:

public void SomeMethod()
{
	AppodealCallbacks.Banner.OnLoaded += OnBannerLoaded;
	AppodealCallbacks.Banner.OnFailedToLoad += OnBannerFailedToLoad;
	AppodealCallbacks.Banner.OnShown += OnBannerShown;
	AppodealCallbacks.Banner.OnShowFailed += OnBannerShowFailed;
	AppodealCallbacks.Banner.OnClicked += OnBannerClicked;
	AppodealCallbacks.Banner.OnExpired += OnBannerExpired;
}

#region BannerAd Callbacks

// Called when a banner is loaded (height arg shows banner's height, precache arg shows if the loaded ad is precache
private void OnBannerLoaded(object sender, BannerLoadedEventArgs e)
{
	Debug.Log("Banner loaded");
}

// Called when banner failed to load
private void OnBannerFailedToLoad(object sender, EventArgs e)
{
	Debug.Log("Banner failed to load");
}

// Called when banner failed to show
private void OnBannerShowFailed(object sender, EventArgs e)
{
	Debug.Log("Banner show failed");
}

// Called when banner is shown
private void OnBannerShown(object sender, EventArgs e)
{
	Debug.Log("Banner shown");
}

 // Called when banner is clicked
private void OnBannerClicked(object sender, EventArgs e)
{
	Debug.Log("Banner clicked");
}

// Called when banner is expired and can not be shown
private void OnBannerExpired(object sender, EventArgs e)
{
	Debug.Log("Banner expired");
}

#endregion

Unity Main Thread

All callbacks are called on native main threads that do not match the main thread of the Unity. If you need to receive callbacks in the main Unity thread follow our Callback Usage Guide.
Custom Positioned Banner
Displaying Banner At Custom Position

    UPM Distribution
    Manual Distribution

Banner ad can be moved along the axis to the desired position.

To show Banner at Custom Position use the following method:

Appodeal.ShowBannerView(yPosition, xPosition, "placementName");

Use int value or one of the constants below for yPosition:

    AppodealViewPosition.VerticalTop — to align a banner to the top of the screen.
    AppodealViewPosition.VerticalBottom — to align a banner to the bottom of the screen.

Use int value or one of the constants below for xPosition:

    AppodealViewPosition.HorizontalSmart — to use the full-screen width.
    AppodealViewPosition.HorizontalCenter — to center a banner horizontally.
    AppodealViewPosition.HorizontalRight — to align a banner to the right.
    AppodealViewPosition.HorizontalLeft — to align a banner to the left.

Banner positioning is relative to the top left corner of the screen.
Hide Banner

To hide a banner that was shown via Appodeal.ShowBannerView() method use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.HideBannerView();

Advanced
Placements

Appodeal SDK allows you to tag each impression with different placement. To use placements, you need to create placements in Appodeal Dashboard. Read more about placements.

To show an ad with placement, you have to call show method with specifying placement's name:

    UPM Distribution
    Manual Distribution

Appodeal.Show(AppodealShowStyle.BannerTop, "placementName");

Destroy Hidden Banner

To free memory from hidden banner call the code below:

    UPM Distribution
    Manual Distribution

Appodeal.Destroy(AppodealAdType.Banner);

Get Predicted eCPM

This method returns expected eCPM for a currently cached advertisement. The amount is calculated based on historical data for the current ad unit.

    UPM Distribution
    Manual Distribution

Appodeal.GetPredictedEcpm(AppodealAdType.Banner);

Enable 728x90 Banners

To enable 728*90 banner use the following method before initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetTabletBanners(true);

Disable Banner Refresh Animation

To disable banner refresh animation use the following method before initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetBannerAnimation(false);

Smart Banners

Smart banners are the banner ads which automatically fit the screen size. Using them helps to deal with the increasing fragmentation of the screen sizes on different devices. In the Appodeal SDK the smart banners are enabled by default. To disable them, use the following method before initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetSmartBanners(false);






# Mrec

Mrec is a 300x250 banner. This type can be useful if the application has a large free area for placing a banner in the interface.

You can use our demo app as a reference project.

Demo App
Display

Mrec ads are refreshed every 15 seconds automatically by default.

    UPM Distribution
    Manual Distribution

To display mrec, use the following method:

Appodeal.ShowMrecView(yPosition, xPosition, "placementName");

Use int value or one of the constants below for yPosition:

    AppodealViewPosition.VerticalTop — to align a mrec to the top of the screen.
    AppodealViewPosition.VerticalBottom — to align a mrec to the bottom of the screen.

Use int value or one of the constants below for xPosition:

    AppodealViewPosition.HorizontalSmart — to use the full-screen width.
    AppodealViewPosition.HorizontalCenter — to center a mrec horizontally.
    AppodealViewPosition.HorizontalRight — to align a mrec to the right.
    AppodealViewPosition.HorizontalLeft — to align a mrec to the left.

Mrec positioning is relative to the top left corner of the screen.
Hide Mrec

To hide a mrec ad use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.HideMrecView();

Callbacks

The callbacks are used to track different events in the lifecycle of an ad, e.g. when an ad was clicked on or closed. Follow the steps below to implement them:

    UPM Distribution
    Manual Distribution

Subscribe to the desired mrec event using one of the options from this guide. (you can subscribe to any number of events you want)

AppodealCallbacks.Mrec.OnLoaded += (sender, args) => { };

You will find all existing mrec events in the example below:

public void SomeMethod()
{
	AppodealCallbacks.Mrec.OnLoaded += (sender, args) => OnMrecLoaded(args.IsPrecache);
	AppodealCallbacks.Mrec.OnFailedToLoad += (sender, args) => OnMrecFailedToLoad();
	AppodealCallbacks.Mrec.OnShown += (sender, args) => OnMrecShown();
	AppodealCallbacks.Mrec.OnShowFailed += (sender, args) => OnMrecShowFailed();
	AppodealCallbacks.Mrec.OnClicked += (sender, args) => OnMrecClicked();
	AppodealCallbacks.Mrec.OnExpired += (sender, args) => OnMrecExpired();
}

#region MrecAd Callbacks

// Called when mrec is loaded precache flag shows if the loaded ad is precache)
private void OnMrecLoaded(bool isPrecache)
{
	Debug.Log("Mrec loaded");
}

// Called when mrec failed to load
private void OnMrecFailedToLoad()
{
	Debug.Log("Mrec failed to load");
}

// Called when mrec is failed to show
private void OnMrecShowFailed()
{
	Debug.Log("Mrec show failed");
}

// Called when mrec is shown
private void OnMrecShown()
{
	Debug.Log("Mrec shown");
}

// Called when mrec is clicked
private void OnMrecClicked()
{
	Debug.Log("Mrec clicked");
}

// Called when mrec is expired and can not be shown
private void OnMrecExpired()
{
	Debug.Log("Mrec expired");
}

#endregion

Unity Main Thread

All callbacks are called on native main threads that do not match the main thread of the Unity. If you need to receive callbacks in the main Unity thread follow our Callback Usage Guide.
Placements

Appodeal SDK allows you to tag each impression with different placement. To use placements, you need to create placements in Appodeal Dashboard. Read more about placements.

To show an ad with placement, you have to call show method with specifying placement's name:

    UPM Distribution
    Manual Distribution

Appodeal.ShowMrecView(yPosition, xPosition, "placementName");

Get Predicted eCPM

This method returns expected eCPM for a currently cached advertisement. The amount is calculated based on historical data for the current ad unit.

    UPM Distribution
    Manual Distribution

Appodeal.GetPredictedEcpm(AppodealAdType.Mrec);






# Segments & Placements
Segments

Segments are used to track statistics for various user categories and manage ads for this categories. A segment is a fraction of audience outlined based on certain parameters: e.g. gender, age or any other parameters known to the app and passed to Appodeal SDK. Additional ad management settings can be applied to each segment. Read more on segments in our FAQ.

Once user segments have been created, they can then be analyzed and used to configure ads.

To create a new segment go here.
info

If you have no segments, all users will be assigned to default segment.

If you have multiple segments, their order is important. Only the first segment related to the given user will apply. All of the rest will be ignored.
Manual Filters

Manual Filters allow to group users by any available metric. E.g. you know the sources that directed users to your app and you want to track the statistics for such sources — create a segment for each source and mark each user with the source they came from.

To create such a segment, you have to set its name and value:

    UPM Distribution
    Manual Distribution

Appodeal.SetCustomFilter("KEY_STRING", "SOME_VALUE");
Appodeal.SetCustomFilter("KEY_BOOL", true);
Appodeal.SetCustomFilter("KEY_INT", 42);

Appodeal.ResetCustomFilter("KEY_STRING");

Value can be boolean, numeric or string-based.
Bought In-Apps and In-Apps Amount Filters

Bought In-Apps allows to group users by the fact of purchasing in-apps. This will help you adjust the ads for such users or turn them off if needed.

In-Apps Amount filter allows you to group users who’ve made a particular amount of in-app purchases.

Please submit the purchase info via Appodeal SDK to make these settings work correctly.

    UPM Distribution
    Manual Distribution

Appodeal.TrackInAppPurchase(5.0,"USD");

If you have no segments, all users will be assigned to default segment.

If you have multiple segments, their order is important. Only the first segment related to the given user will apply. All of the rest will be ignored.
Placements

Appodeal SDK allows you to tag each impression with different placement. Read more on placements in our FAQ.

To show an ad with placement, you have to call show method like this:

    UPM Distribution
    Manual Distribution

Appodeal.Show(adType, "placementName");

To check if an impression is available for a given placement, use:

    UPM Distribution
    Manual Distribution

if(Appodeal.CanShow(adType, "placementName")){
	Appodeal.Show(adType, "placementName");
}

You can configure your impression logic for each placement.

If you have no placements or call Appodeal.Show() with placement that does not exist, the impression will be tagged with default placement with corresponding settings applied.
Important!

Placement settings affect ONLY ad presentation, not loading or caching.





# Useful SDK Methods
Enable Test Mode

Using test mode allows you to get our test ad creatives with 100% fillrate.

    UPM Distribution
    Manual Distribution

Appodeal.SetTesting(true);

Should be called before the SDK initialization.
Enable Logging

To enable debug logging, use the code below:

    UPM Distribution
    Manual Distribution

Appodeal.SetLogLevel(AppodealLogLevel.Verbose);

Should be called before the SDK initialization.

Logs will be written in the console using the Appodeal tag.

Available parameters:

    AppodealLogLevel.None- logs off;
    AppodealLogLevel.Debug - debug messages;
    AppodealLogLevel.Verbose - all SDK and ad network messages.

Disable Networks

    UPM Distribution
    Manual Distribution

Appodeal.DisableNetwork((string)network);

Should be called before the SDK initialization.

Use constants from AppodealStack.Monetization.Common.AppodealNetworks to choose necessary network.
Disable Networks For Specific Ad Types

To disable networks for the specific ad formats use the following method:

    UPM Distribution
    Manual Distribution

Appodeal.DisableNetwork((string)network, adTypes);

Important Should be called before the SDK initialization.
Test Adapters Integration
This method will take effect only on Android platform

To check integration of the third-party networks, you need to start a test screen by calling the following method.

    UPM Distribution
    Manual Distribution

Appodeal.ShowTestScreen();

Show Mediation Debugger

To show one of the predefined mediation debugger window providers, call the following method.

    UPM Distribution

Appodeal.ShowMediationDebugger(MediationDebuggerProvider.AppLovinSdk);

info

The method returns true if the chosen mediation debugger window was found and displayed, otherwise - false.

The table below shows all available values ​​for MediationDebuggerProvider enumeration.
Debugger Provider	Description
AppLovinSdk	Displays the mediation debugger window from AppLovin Max.





# Logging

SDK logging allows you to check SDK integration and activity, including information about waterfalls with ad units, ads requests, loading, and some other. We recommend always enabling logs and using the debug logs to get full SDK information.

Enable logging using the code below before SDK initialization:

    UPM Distribution
    Manual Distribution

Appodeal.SetLogLevel(AppodealLogLevel.Verbose);

Available parameters:

    AppodealLogLevel.None - logs off;
    AppodealLogLevel.Debug - debug messages;
    AppodealLogLevel.Verbose - all SDK and ad network messages.

SDK logs will not appear in the Unity debugger.




# Ad Revenue Callbacks

Appodeal SDK allows you to get impression-level revenue data with Ad Revenue Callbacks. This data includes information about network name, revenue, ad type, etc.

The impression-level ad revenue data can be used then to share with your mobile measurement partner of choice, such as Firebase, for all supported networks.

If you have integrated Firebase, which is included in Appodeal SDK, using this guide, then ad revenue data will be sent automatically, you can read more about it here.
Minimum Requirements:

Appodeal SDK 3.0.1+
Callback Implementation

    UPM Distribution
    Manual Distribution

    Subscribe to the Ad Revenue event using one of the options from this guide.
    You can use callbacks as shown below:

public void SomeMethod()
{
	AppodealCallbacks.AdRevenue.OnReceived += (sender, args) => {};
}

Admob Notice

To get impression-level ad revenue from Admob you also need to turn on the setting in your AdMob account.

Go to your Admob Account Settings → Account → turn on Impression-level ad revenue toggle.
Appodeal Ad Revenue Description

AppodealAdRevenue - represents revenue information from the ad network.
Parameter	Type	Description
NetworkName	String	The name of the ad network
DemandSource	String	The demand source name and bidder name in case of impression from real-time bidding
AdUnitName	String	Unique ad unit name
Placement	String	Appodeal's placement name
Revenue	Double	The ad's revenue amount or 0 if it doesn't exist
AdType	String	Appodeal's ad type as string presentation
Currency	String	Current currency supported by Appodeal (USD) as string presentation
RevenuePrecision	String	The revenue precision
Revenue Precision options

    exact - programmatic revenue is the resulting price of the auction
    publisher_defined - revenue from cross-promo campaigns
    estimated - revenue based on ad network price floors or historical eCPM
    undefined - revenue amount is not defined

Use Case
Please remember:

If you have integrated analytics for example Firebase using this guide with Appodeal, then no additional steps are required.

In case you are using your own analytics in the project, please find the example below:

    UPM Distribution
    Manual Distribution

#region IAdRevenueListener implementation

public void OnAdRevenueReceived(AppodealAdRevenue ad)
{
	//AppsFlyer
	var dict = new Dictionary<string, string>();
	dict.Add("AdUnitName", ad.AdUnitName);
	dict.Add("AdType", ad.AdType);
	AppsFlyerAdRevenue.logAdRevenue(ad.NetworkName,
		AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeAppodeal,
		ad.Revenue, ad.Currency, dict
	);

	//Adjust
	AdjustAdRevenue adRevenue = new AdjustAdRevenue(AdjustConfig.AdjustAdRevenueSourcePublisher);
	adRevenue.setRevenue(ad.Revenue, ad.Currency);
	adRevenue.setAdRevenueNetwork(ad.NetworkName);
	adRevenue.setAdRevenueUnit(ad.AdUnitName);
	Adjust.trackAdRevenue(adRevenue);

	//Firebase
	Firebase.Analytics.FirebaseAnalytics.LogEvent(
		Firebase.Analytics.FirebaseAnalytics.EventAdImpression,
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.ParameterAdPlatform, "Appodeal"),
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.ParameterAdFormat, ad.AdType),
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.ParameterAdSource, ad.NetworkName),
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.AdUnitName, ad.AdUnitName),
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.AdCurrency, ad.Currency),
			new Firebase.Analytics.Parameter(
				Firebase.Analytics.FirebaseAnalytics.Value, ad.Revenue)
	);
}

#endregion





# Run Callbacks in Main Unity Thread

Callbacks in Appodeal plugin (only the Manual version!!!) are executed in the main Android or iOS threads (not in the main Unity thread). What does it mean for you? It’s not recommended to perform any UI changes (change colours, positions, sizes, texts and so on) directly in our callback functions.

It's important to understand that it's not possible to receive callbacks in main thread while fullscreen advertising is shown. This is because Unity pauses main thread if game scene is out of screen. That’s why your script will receive some callbacks (like onInterstitialShown or onRewardedVideoFinished) in main thread only after closing fullscreen advertising. But the same callbacks work without such delays in Android and iOS main threads.

So, how to react on Appodeal events to prevent multithreading problems? The simplest way is to use flags and Update method of MonoBehaviour class:

public class SomeClass : MonoBehaviour, IRewardedVideoAdListener
{
    bool videoFinished = false;
    double rewardAmount;
    string rewardName;

    public void onRewardedVideoFinished(double amount, string name)
    {
        rewardAmount = amount;
        rewardName = name;

        // It's important to set flag to true only after all required parameters
        videoFinished = true;
    }

    // Update method always performs in the main Unity thread
    void Update()
    {
        if(videoFinished)
        {
            // Don't forget to set flag to false
            videoFinished = false;
            // Do something with rewardAmount and rewardName
        }
    }
}

Other, maybe more comfortable way is to use UnityMainThreadDispatcher. To use it:

    Download script and prefab.
    Import downloaded files to your project.
    Add UnityMainThreadDispatcher.prefab to your scene (or to all scenes, where you want to make UI changes after Appodeal callbacks).
    Use UnityMainThreadDispatcher.Instance().Enqueue() method to perform changes:

public void onRewardedVideoFinished(double amount, string name)
{
    UnityMainThreadDispatcher.Instance().Enqueue(()=> {
        Debug.Log($"Appodeal. Video Finished: {amount} {name}")
    });
}

And finally, the official way to send message to Unity Main thread is UnitySendMessage. It’s platform dependent, so it’s required to make changes in Android native code and iOS native code.

You can find more information in the official unity documentation for Android and iOS.