---
description: IronSource Mediator
globs:
alwaysApply: false
---
# Initialize


Application State

First, you’ll need to pass the state of the application by executing the following event function during the Application Lifecycle.

Call onApplicationPause in each of the Unity Scenes:

void OnApplicationPause(bool isPaused) {
  IronSource.Agent.onApplicationPause(isPaused);
}

Init the plugin and ad units

You can initialize the plugin in two ways. We recommend the first method as it will fetch the specific ad units you define.

    This method will init the specific ad units mentioned in the adUnits parameter:

    IronSource.Agent.init (YOUR_APP_KEY, IronSourceAdUnits.REWARDED_VIDEO, IronSourceAdUnits.INTERSTITIAL, IronSourceAdUnits.BANNER);

When using this init approach, you can now initialize each ad unit separately at different touchpoints in your app flow in one session.

//For Rewarded Video
IronSource.Agent.init (YOUR_APP_KEY, IronSourceAdUnits.REWARDED_VIDEO);
//For Interstitial
IronSource.Agent.init (YOUR_APP_KEY, IronSourceAdUnits.INTERSTITIAL);
//For Banners
IronSource.Agent.init (YOUR_APP_KEY, IronSourceAdUnits.BANNER);

Alternatively, you can init the SDK as detailed below and the SDK will init the ad units you’ve configured on the ironSource platform:

IronSource.Agent.init (YOUR_APP_KEY);

 ironSource Init Complete Listener

The ironSource SDK fires postback to inform you that the ironSource SDK was initialized successfully, for ironSource SDK 7.2.1+ . This listener will provide you an indication that the initialization process was completed, and you can start loading ads. The callback will be sent once per session, and will indicate the first initialization of the SDK.

IronSourceEvents.onSdkInitializationCompletedEvent += SdkInitializationCompletedEvent;
private void SdkInitializationCompletedEvent(){}

Step 7. Verify your integration

The ironSource SDK provides an easy way to verify that your Rewarded Video Mediation integration was completed successfully. To verify your ironSource SDK integration as well as any additional Ad Networks you have implemented, simply add this method to your project:

IronSource.Agent.validateIntegration();

For more details on the ironSource Integration Verification tool navigate to this article.





# Rewarded
Step 1. Implement the Rewarded Video Events

The ironSource Unity Plugin fires several events to inform you of ad availability.

Add the following code to register to the events:

//Add AdInfo Rewarded Video Events
IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoOnAdOpenedEvent;
IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoOnAdClosedEvent;
IronSourceRewardedVideoEvents.onAdAvailableEvent += RewardedVideoOnAdAvailable;
IronSourceRewardedVideoEvents.onAdUnavailableEvent += RewardedVideoOnAdUnavailable;
IronSourceRewardedVideoEvents.onAdShowFailedEvent += RewardedVideoOnAdShowFailedEvent;
IronSourceRewardedVideoEvents.onAdRewardedEvent += RewardedVideoOnAdRewardedEvent;
IronSourceRewardedVideoEvents.onAdClickedEvent += RewardedVideoOnAdClickedEvent;

The Plugin will notify the Listener of all possible events listed below:


/************* RewardedVideo AdInfo Delegates *************/
// Indicates that there’s an available ad.
// The adInfo object includes information about the ad that was loaded successfully
// This replaces the RewardedVideoAvailabilityChangedEvent(true) event
void RewardedVideoOnAdAvailable(IronSourceAdInfo adInfo) {
}
// Indicates that no ads are available to be displayed
// This replaces the RewardedVideoAvailabilityChangedEvent(false) event
void RewardedVideoOnAdUnavailable() {
}
// The Rewarded Video ad view has opened. Your activity will loose focus.
void RewardedVideoOnAdOpenedEvent(IronSourceAdInfo adInfo){
}
// The Rewarded Video ad view is about to be closed. Your activity will regain its focus.
void RewardedVideoOnAdClosedEvent(IronSourceAdInfo adInfo){
}
// The user completed to watch the video, and should be rewarded.
// The placement parameter will include the reward data.
// When using server-to-server callbacks, you may ignore this event and wait for the ironSource server callback.
void RewardedVideoOnAdRewardedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo){
}
// The rewarded video ad was failed to show.
void RewardedVideoOnAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo){
}
// Invoked when the video ad was clicked.
// This callback is not supported by all networks, and we recommend using it only if
// it’s supported by all networks you included in your build.
void RewardedVideoOnAdClickedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo){
}

Error Codes

ironSource provides an error code mechanism to help you understand any error you may encounter during integration or in live production. See the complete guide here.
Network Connectivity Status

You can determine and monitor the internet connection on the user’s device through the ironSource Network Change Status function.  This enables the SDK to change its availability according to network modifications, i.e. in the case of no network connection, the availability will turn to FALSE.The default of this function is false; if you’d like to listen to it for changes in connectivity, activate it in the SDK initialization with the following string:

IronSource.Agent.shouldTrackNetworkState (true);

Step 2. Show a Video Ad to Your Users
Ad Availability

You will receive the availability status of the Rewarded Video ad through the onAdAvailableEvent events.

In addition, you have the possibility to ask for ad availability directly by calling:

bool available = IronSource.Agent.isRewardedVideoAvailable();

Serve Video Ad

Once an ad network has an available video, you will be ready to show the video to your users. Before you display the ad, make sure to pause any game action, including audio, to ensure the best experience for your users. Use the showRewardedVideo() method on your IronSource.Agent instance, to show a video ad to your users:

IronSource.Agent.showRewardedVideo();

Ad Placements

With the LevelPlay Ad Placements tool, you can customize and optimize the Rewarded Video experience. This tool enables you to present videos to your users from different placements depending on the reward. You can use the below function to define the exact Placement you’d like to show an ad from. Navigate to the Ad Placement document for more details. The Reward settings of this Placement will be pulled from the ironSource server:

IronSource.Agent.showRewardedVideo("YOUR_PLACEMENT_NAME");

To get details about the specific reward associated with each ad placement, you can call the following:

Placement placement = IronSource.Agent.getPlacementInfo(placementName);
//Placement can return null if the placementName is not valid.
if(placement != null)
{
    String rewardName = placement.getRewardName();
    int rewardAmount = placement.getRewardAmount();
}

Note: Make sure you use the placement name as written in the platform when using it as a parameter.
Capping & Pacing

ironSource SDK gives you the ability to set your app’s capping and pacing by ad unit, ad placement, or both. When you use both, the more restrictive settings will be applied.

By ad unit: This enables you to control the number of rewarded video ads delivered over a user’s entire session. If an ad is capped, then you will not received available ad / load success indication when trying to load the ad.

By placement: This helps you ensure you’re serving the optimal number of ads at each location in your app. If you choose to use the capping and pacing tool by placement for Rewarded Video, we recommend calling the below method to verify if a certain placement has reached its ad limit. This is to ensure you don’t portray the Rewarded Video button when the placement has been capped or paced and thus will not serve the Video ad.

bool isRewardedVideoPlacementCapped(string placementName);

Read more about capping and pacing here.
Dynamic User ID

The Dynamic UserID is a parameter to verify AdRewarded transactions and can be changed throughout the session.  To receive this parameter through the server to server callbacks, it must be set before calling showRewardedVideo. You will receive a dynamicUserId  parameter in the callback URL with the reward details.

boolean setDynamicUserId(String dynamicUserID);

Step 3. Reward the User

The ironSource SDK will fire the onAdRewardedEvent each time the user successfully completes a video.
Note: The onAdRewardedEvent  and onAdClosedEvent are asynchronous. Make sure to set up your listener to grant rewards even in cases where onAdRewardedEvent  is fired after the onAdClosedEvent .


void RewardedVideoOnAdRewardedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo){
    //TODO - here you can reward the user according to the reward name and amount
    ssp.getPlacementName();
    ssp.getRewardName();
    ssp.getRewardAmount();
}

Note: The default setting in your LevelPlay account is to notify you of user completions/rewards via the IronSourceAdRewardedEvent event within the client of your app. Additionally, if you would also like to receive notifications to your back-end server, you can turn on server-to-server callbacks.
Server-to-server callbacks

If you turn on server-to-server callbacks, make sure you’ve set the userID before you initialize the ironSource SDK so your users can get rewarded, and make sure to reward your user only once for the same completion.

ironSource LevelPlay will be firing both the client-side callback and the server-to-server callback. So, you will get two notifications in total for each completion.

To utilize server-to-server callbacks, see here.

Done!
You are now all set to deliver Rewarded Video in your application.




# Rewarded Video Manual
In addition to loading ironSource rewarded video ads automatically, you can also set rewarded video ads to load manually. To do this, you must set the entire session loading mode prior to SDK initialization. This is supported from ironSource SDK 7.2.0+ (Beta 7.1.13 for Android).
Step 1. Implement the Rewarded Video Manual Events

The ironSource Unity Plugin fires events to inform you of ad availability. These events will be triggered only upon manual rewarded video implementation.

Add the following code to register to the events, in addition to the Rewarded Video events described here.

//Add AdInfo Rewarded Video Events
IronSourceRewardedVideoEvents.onAdReadyEvent += RewardedVideoOnAdReadyEvent;
IronSourceRewardedVideoEvents.onAdLoadFailedEvent += RewardedVideoOnAdLoadFailedEvent;
IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoOnAdOpenedEvent;
IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoOnAdClosedEvent;
IronSourceRewardedVideoEvents.onAdShowFailedEvent += RewardedVideoOnAdShowFailedEvent;
IronSourceRewardedVideoEvents.onAdRewardedEvent += RewardedVideoOnAdRewardedEvent;
IronSourceRewardedVideoEvents.onAdClickedEvent += RewardedVideoOnAdClickedEvent;

The Plugin will notify the Listener of all possible events listed below:

/************* RewardedVideo AdInfo Delegates *************/
// Indicates that the Rewarded video ad was loaded successfully.
// AdInfo parameter includes information about the loaded ad
void RewardedVideoOnAdReadyEvent(IronSourceAdInfo adInfo) {
}
// Indicates that the Rewarded video ad failed to be loaded
void RewardedVideoOnAdLoadFailedEvent(IronSourceError error) {
}
// The Rewarded Video ad view has opened. Your activity will loose focus.
void RewardedVideoOnAdOpenedEvent(IronSourceAdInfo adInfo){
}
// The Rewarded Video ad view is about to be closed. Your activity will regain its focus.
void RewardedVideoOnAdClosedEvent(IronSourceAdInfo adInfo){
}
// The user completed to watch the video, and should be rewarded.
// The placement parameter will include the reward data.
// When using server-to-server callbacks, you may ignore this event and wait for the ironSource server callback.
void RewardedVideoOnAdRewardedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo){
}
// The rewarded video ad was failed to show.
void RewardedVideoOnAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo){
}
// Invoked when the video ad was clicked.
// This callback is not supported by all networks, and we recommend using it only if
// it's supported by all networks you included in your build.
void RewardedVideoOnAdClickedEvent(IronSourcePlacement placement, IronSourceAdInfo adInfo){
}

Step 2: Set up rewarded video manual loading

Set the operation mode for rewarded video ads before you initialize the ironSource SDK. This will affect your current session.

IronSource.Agent.setManualLoadRewardedVideo(bool isRewardedVideoManualAPI);

Step 3: Manually load rewarded video ads

Request a rewarded video ad d before you plan on showing it to your users as the loading process can take time. Use the following API to load your ad:

IronSource.Agent.loadManualRewardedVideo();

Done!

After defining your operation loading mode to Manual, you can complete your Rewarded Video integration, as described here.






# Interstitial
Step 1. Implement the Interstitial Events

The ironSource Unity Plugin fires several events to inform you of ad availability.

Add the following code to register to the events:

//Add AdInfo Interstitial Events
IronSourceInterstitialEvents.onAdReadyEvent += InterstitialOnAdReadyEvent;
IronSourceInterstitialEvents.onAdLoadFailedEvent += InterstitialOnAdLoadFailed;
IronSourceInterstitialEvents.onAdOpenedEvent += InterstitialOnAdOpenedEvent;
IronSourceInterstitialEvents.onAdClickedEvent += InterstitialOnAdClickedEvent;
IronSourceInterstitialEvents.onAdShowSucceededEvent += InterstitialOnAdShowSucceededEvent;
IronSourceInterstitialEvents.onAdShowFailedEvent += InterstitialOnAdShowFailedEvent;
IronSourceInterstitialEvents.onAdClosedEvent += InterstitialOnAdClosedEvent;

The Plugin will notify the Listener of all possible events listed below:

/************* Interstitial AdInfo Delegates *************/
// Invoked when the interstitial ad was loaded succesfully.
void InterstitialOnAdReadyEvent(IronSourceAdInfo adInfo) {
}
// Invoked when the initialization process has failed.
void InterstitialOnAdLoadFailed(IronSourceError ironSourceError) {
}
// Invoked when the Interstitial Ad Unit has opened. This is the impression indication.
void InterstitialOnAdOpenedEvent(IronSourceAdInfo adInfo) {
}
// Invoked when end user clicked on the interstitial ad
void InterstitialOnAdClickedEvent(IronSourceAdInfo adInfo) {
}
// Invoked when the ad failed to show.
void InterstitialOnAdShowFailedEvent(IronSourceError ironSourceError, IronSourceAdInfo adInfo) {
}
// Invoked when the interstitial ad closed and the user went back to the application screen.
void InterstitialOnAdClosedEvent(IronSourceAdInfo adInfo) {
}
// Invoked before the interstitial ad was opened, and before the InterstitialOnAdOpenedEvent is reported.
// This callback is not supported by all networks, and we recommend using it only if
// it's supported by all networks you included in your build.
void InterstitialOnAdShowSucceededEvent(IronSourceAdInfo adInfo) {
}

Step 2. Load Interstitial Ad

We recommend requesting an interstitial ad after receiving the init success callback. To request an interstitial ad, call the following method:

IronSource.Agent.loadInterstitial();

Note:  If you’d like to serve several Interstitial Ads in your application, you must repeat this step after you’ve shown and closed the previous Interstitial Ad. Once the InterstitialOnAdClosedEvent function is fired, you will be able to load a new Interstitial ad.
Step 3. Check Ad Availability

After you’ve called the loadInterstitial in Step 2, you will be notified when the ad is loaded and ready to be shown to your user. The InterstitialOnAdReadyEvent will inform you about ad availability.

public void InterstitialOnAdReadyEvent(){}

In addition, you have the possibility to request the ad availability directly by calling the following function:

IronSource.Agent.isInterstitialReady()

We don’t recommend making consecutive requests for an interstitial ad in a short timespan. Numerous requests in a short period of time have no added value as the chance of available inventory at this time is unlikely.
Step 4. Show Interstitial Ad

Once you receive the onInterstitialAdReadyEvent callback, you are ready to show an Interstitial Ad to your users. To provide the best experience for your users, make sure to pause any game action, including audio, during the time the ad is displayed.

Invoke the following method to serve an Interstitial ad to your users:

IronSource.Agent.showInterstitial();

With LevelPlay‘s Ad Placements, you can customize and optimize the Interstitial experience. This tool enables you to present Interstitial ads to your users in different places, i.e. app launch, between levels, etc. You can use the below function to define the exact placement to show an ad from. Navigate to the Ad Placement document for more details.

void showInterstitial(string placementName);

In addition to LevelPlay’s Ad Placements, you can now configure capping and pacing settings for selected placements. Capping and pacing improve the user experience in your app by limiting the number of ads served within a defined timeframe. Read more about capping and pacing here.

We recommend calling the following to verify if a placement is capped before you serve the Interstitial to your user:

IronSource.Agent.isInterstitialPlacementCapped(placementName)

Important! Once you’ve successfully completed step 4, you will have shown your user an Interstitial Ad. In the case you want to serve another Interstitial ad, you must repeat Step 2 to request an additional Interstitial.





# Banner
Step 1. Load Banner Ad

To load a Banner ad, call the following method:

    Load the Banner view by calling this method (in this example it’s the BANNER banner size):

    IronSource.Agent.loadBanner(IronSourceBannerSize.BANNER, IronSourceBannerPosition.BOTTOM);

See table below for details about our supported standard banner sizes:
IronSourceBannerSize 	Description 	Dimensions (WxH) (points in iOS, dp in Android)
BANNER 	Standard Banner 	320 x 50
LARGE 	Large Banner 	320 x 90
RECTANGLE 	Medium Rectangular (MREC) 	300 x 250
SMART
	Smart Banner
(Automatically renders ads to adjust size and orientation for mobile & tablets) 	iOS:
If (iPhone) 320 x 50
If (iPad) 728 x 90
Android:
If (screen width ≤ 720) 320 x 50
If (screen width > 720) 728 x 90

See table below for details about our supported standard banner positions:
IronSourceBannerPosition 	Description
TOP 	Banner will be positioned at the top center of the screen
BOTTOM 	Banner will be positioned at the bottom center of the screen
Another option is initiating the banner with Custom size, using this signature (WxH) (points in iOS, dp in Android):

IronSource.Agent.loadBanner(new IronSourceBannerSize(320, 50), IronSourceBannerPosition.BOTTOM);

You will receive the onBannerAdLoadedEvent and the banner will show on your app.
Hide & Display Banner – Optional

In order to provide maximum flexibility in the ad experience, you now have the ability to hide and present banners on your app.

Using hideBanner and displayBanner APIs, you can control whether banners can be loaded and destroyed while in the background.

Once you’ve loaded and served a banner, you can choose to hide this banner and re-show it at a later point in your app.

To hide the banner, call this function:

IronSource.Agent.hideBanner();

To then show this same banner again, call this function:

IronSource.Agent.displayBanner();

Step 2. Implement the Banner Events

The ironSource Unity Plugin fires several events to inform you of ad availability and activity. Under IronSource/Assets/Prefabs, you’ll find the IronSourceEventsPrefab. Add it to your project to receive these events.

//Add AdInfo Banner Events
IronSourceBannerEvents.onAdLoadedEvent += BannerOnAdLoadedEvent;
IronSourceBannerEvents.onAdLoadFailedEvent += BannerOnAdLoadFailedEvent;
IronSourceBannerEvents.onAdClickedEvent += BannerOnAdClickedEvent;
IronSourceBannerEvents.onAdScreenPresentedEvent += BannerOnAdScreenPresentedEvent;
IronSourceBannerEvents.onAdScreenDismissedEvent += BannerOnAdScreenDismissedEvent;
IronSourceBannerEvents.onAdLeftApplicationEvent += BannerOnAdLeftApplicationEvent;

The Plugin will notify the Listener of all possible events listed below:

/************* Banner AdInfo Delegates *************/
//Invoked once the banner has loaded
void BannerOnAdLoadedEvent(IronSourceAdInfo adInfo)
{
}
//Invoked when the banner loading process has failed.
void BannerOnAdLoadFailedEvent(IronSourceError ironSourceError)
{
}
// Invoked when end user clicks on the banner ad
void BannerOnAdClickedEvent(IronSourceAdInfo adInfo)
{
}
//Notifies the presentation of a full screen content following user click
void BannerOnAdScreenPresentedEvent(IronSourceAdInfo adInfo)
{
}
//Notifies the presented screen has been dismissed
void BannerOnAdScreenDismissedEvent(IronSourceAdInfo adInfo)
{
}
//Invoked when the user leaves the app
void BannerOnAdLeftApplicationEvent(IronSourceAdInfo adInfo)
{
}

Step 3. Additional Load Settings

We support placements, as well as capping and pacing for Banners on the LevelPlay dashboard. Learn how to set up placements, capping and pacing for Banners to optimize your app’s user experience here.

If you’ve set up placements for your Banner, call the following method to serve a Banner ad in a specific location:

IronSource.Agent.loadBanner(IronSourceBannerSize.BANNER, IronSourceBannerPosition.BOTTOM, (string) "YOUR_PLACEMENT_NAME");

Note: SDK will not show more than one banner at a time.

Step 4. Destroy the Banner Ad

To destroy a banner, call the following method:

IronSource.Agent.destroyBanner();

A destroyed banner can no longer be loaded. If you want to serve it again, you must initiate it again.
Step 5.  Integrate a Banner Provider

Next, integrate the ad network adapters to serve Banners through LevelPlay Mediation.

You can find the supported networks below, and bannerSize behaviour for each network, as defined for relevant platform:

iOS Banner Support

Android Banner Support
Step 6.  Adaptive banners

The adaptive banner feature will allow you to receive the optimal banner height , based on a predefined ad-width.

While using this feature, networks that support adaptive banners (Currently AdMob and Ad Manager only) will return ads with best-fit-height based on the container width you defined. All other networks will continue to deliver banners according to the specified default ad size.

In this implementation (Supported from ironSource SDK 7.8.0+) the container defines the boundaries of the banner for all networks, whether they support adaptive banners or not. This ensures a consistent experience across each refresh during the same load session.

    Set the banner default size.
    Define adaptive banner parameters –
        Width: the banner width you would like to display. Use either fix size, or getDeviceScreenWidth API.
        Height: use getMaximalAdaptiveHeight API to get the recommended banner height for the width you defined in the previous step.
    Create a container, using the parameter sizes defined in step #1
    Set the adaptive flag to true.
    Load the banner

Define a container

You can either create a manual container with specific sizes or implement an automatic fetch of the screen size.

Manual Container

This example defines a manual container with a width of 360.

IronSourceBannerSize ironSourceBannerSize = IronSourceBannerSize.BANNER;
float Width = 360;
float Height = IronSource.Agent.getMaximalAdaptiveHeight(Width);
ISContainerParams isContainerParams = new ISContainerParams{ Width = Width, Height = Height };
ironSourceBannerSize.setBannerContainerParams(isContainerParams);
ironSourceBannerSize.SetAdaptive(true);
IronSource.Agent.loadBanner(ironSourceBannerSize, IronSourceBannerPosition.BOTTOM);

Fetch automatic screen size

IronSourceBannerSize ironSourceBannerSize = IronSourceBannerSize.BANNER;
float Width = IronSource.Agent.getDeviceScreenWidth();
float Height = IronSource.Agent.getMaximalAdaptiveHeight(Width);
ISContainerParams isContainerParams = new ISContainerParams{ Width = Width, Height = Height };
ironSourceBannerSize.setBannerContainerParams(isContainerParams);
ironSourceBannerSize.SetAdaptive(true);
IronSource.Agent.loadBanner(ironSourceBannerSize, IronSourceBannerPosition.BOTTOM);

    The getDeviceScreenWidth method provides the full screen size while considering the safe area in iOS, and returns the width based on the device orientation.
    The getMaximalAdaptiveHeight method provides the maximum height for a given width in adaptive banner supported networks. If there are no networks that support adaptive banners, the returned value will be -1.

Done!

You are now all set up to serve Banners in your application. Verify your integration with our Integration Helper.




# LevelPlay integration test suite
The LevelPlay integration test suite enables you to quickly and easily test your app’s integration, verify platform setup, and review ads related to your configured networks.
Important!
The test suite is supported from ironSource SDK version 7.3.0+. It exclusively supports portrait orientation.

Watch this video to learn how to use the test suite to confirm the integration of your app via your on-device UI tool. Detailed instructions are listed below.
Video Player
00:00
02:09
Enable the test suite with SetMetaData flag (beta)

To enable the test suite in your app, call the setMetaData API before setting the init:

IronSource.Agent.setMetaData("is_test_suite", "enable");

Launch the LevelPlay integration test suite

LevelPlay integration test suite should be launched after mediation init is completed successfully. Call the following method to launch the test suite:

IronSource.Agent.launchTestSuite();

Implementation example:

On success callback, launch test suite

IronSourceEvents.onSdkInitializationCompletedEvent += SdkInitializationCompletedEvent;
private void SdkInitializationCompletedEvent(){
    ...
    //Launch test suite
    IronSource.Agent.launchTestSuite();
}




# Integration Helper
The ironSource SDK and Unity Plugin provides an easy way to verify that you’ve successfully integrated the ironSource SDK and any additional adapters; it also makes sure all required dependencies and frameworks were added for the various Mediated Ad Networks.
New! The Integration Helper will now also portray the compatibility between the SDK and adapter versions.
⚡ Before you start
The Integration Helper tool can only be used with Unity Plugin 6.3.4+.
Integration Helper Method

The Integration Helper will now also portray the compatibility between the SDK and adapter versions. After you have finished your integration, call the following static method and confirm that everything in your integration is marked as VERIFIED:

IronSource.Agent.validateIntegration();

Device Advertising ID

You can easily retrieve your AID/GAID at the bottom of the log after you call the method, as exemplified below.

Output example:
Integration Validator

The Integration Helper reviews everything, including Ad Networks you may have intentionally chosen NOT to include in your application. These will appear as MISSING and is no reason for concern.

Important!
Once you’ve successfully verified your integration, please remember to remove the integration helper from your code.
