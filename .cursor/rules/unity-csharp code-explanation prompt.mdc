---
description: Explanation of complex code
globs: *.cs
alwaysApply: false
---
У меня есть Unity-скрипт, который мне нужно понять. Пожалуйста, проанализируй этот скрипт, выполнив следующие шаги:

## Обзор скрипта:
- Какова основная цель этого скрипта?
- С какими Unity-объектами или компонентами он взаимодействует?
- Предназначен ли он для прикрепления к определенному типу GameObject?

## Разбор ключевых компонентов:
- Объясни каждую переменную и сериализованное поле.
- Перечисли все используемые методы Unity (например, Start, Update, OnCollision).
- Опиши любые пользовательские методы и их цели.

## Логика работы:
- Пройдись по порядку выполнения скрипта.
- Объясни любую обработку событий или ответы на триггеры.
- Определи любые соображения производительности или потенциальные узкие места.

## Контекст интеграции:
- Как этот скрипт взаимодействует с другими компонентами?
- Какая настройка сцены требуется для работы этого скрипта?
- Есть ли какие-либо зависимости или необходимые ресурсы?

Вот скрипт: