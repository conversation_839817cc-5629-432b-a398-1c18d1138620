---
description: Unity/C# Code simple + UnityMCP
globs: *.cs
alwaysApply: true
---
Вы являетесь экспертом по разработке на C# для Unity с глубокими знаниями лучших практик разработки игр. Используйте UnityMCP если потребуется.

# Ключевые Правила
## 1. Структура и Чистота Кода
- Пишите лаконичный, понятный и документированный код.
- Используйте компонентную архитектуру для масштабируемости и переиспользования.
- Организуйте проект по папкам, имена пространств – как структура.

## 2. Именование
- `PascalCase`: для публичных методов, свойств, классов, интерфейсов (с префиксом I) и событий.
- `_camelCase`: для приватных полей.
- `camelCase`: для публичных полей и локальных переменных.
- `CAMEL_CASE`: для констант.
- Суффиксы: `Base` для абстрактных классов, `Service` для синглтонов Zenject.

## 3. Организация Кода
- Группируйте директивы `using` внутри пространства имён.
- Применяйте модификаторы доступа для инкапсуляции.
- Размещайте публичные члены в начале класса.
- Используйте вложенные классы для логической группировки.

## 4. Атрибуты и Аннотации
- `[HideReferenceObjectPicker]` - для классов, видимых в инспекторе.
- `[OdinSerialize]` – для полей, видимых в инспекторе.
- `[RequireComponent]` – для обязательных компонентов.
- `[PropertyRange]` – для ограничения значений.

## 5. Архитектурные Паттерны
- Придерживайтесь SOLID.
- Применяйте абстрактные классы и интерфейсы для слабой связанности.

## 6. Асинхронность и События
- Используйте `UniTask` (методы с суффиксом Async) и `CancellationToken`.
- Применяйте делегаты и `event Action<T>` для событий.

## 7. Обработка Ошибок и Форматирование
- Проводите проверки на null (для UnityEngine.Object – достаточно if(obj)).
- Оставляйте пустые строки между методами, используйте стиль Allman.

## 8. Платформо-зависимый Код
- Применяйте директивы `#if UNITY_EDITOR` и `#if` для разделения платформенного кода.

---

# Специфичные Сервисы и Инструменты

- **Debug:**  
  Используйте `Modules.Shared.GlobalServices.CustomDebug` для логирования.

- **Level:**  
  Управление уровнями – `Modules.Shared.GlobalServices.Level.LevelService`.

- **Random:**  
  Применяйте `RandomService` вместо `UnityEngine.Random`.

- **Input:**  
  Используйте `Modules.Shared.InputSystem.InputService` вместо `UnityEngine.Input`.

- **Pool:**  
  Для объектов – `ObjectPool<T>`, для компонентов – `ComponentPool<T>`.
  Вместо `new List<T>()` по возможности используй: `UnityEngine.Pool.ListPool<T>.Get(out var list); UnityEngine.Pool.ListPool<T>.Release(list);`

- **Extensions & Helpers:**  
  Методы расширения для частых операций, утилиты для безопасного вызова делегатов и работы с enum.

---
# Технологический Стек

- **Unity Engine:** Unity UI, Addressables.
- **Zenject:** для внедрения зависимостей.
- **UniTask:** для асинхронного программирования.
- **Odin Inspector:** для улучшения инспектора (SerializedMonoBehaviour/ScriptableObject).
- **DoTween:** для анимаций и эффектов.

---

# Лучшие Практики
- Используйте `TryGetComponent` вместо `GetComponent`.
- Прямые ссылки или `GetComponent` вместо `GameObject.Find`/`Transform.Find`.
- Применяйте TextMeshPro для рендеринга текста.
- Используйте ScriptableObject для управления данными.
---

# UnityMCP

## Руководство по командам в UnityMCP
Используйте UnityMCP

### 1. Уточнение по коммандам
- `get_editor_state`: Использую его РЕДКО.
	- Всегда выбирайте формат **scripts only** или **no scripts**, чтобы уменьшить объем данных, передаваемых из Unity. НИКОГДА НЕ ВЫБИРАЙ format **Raw**.
	- Приоритетно используй **no scripts**, если тебе нужны просто префабы и ресурсы. 
	- Помните, эта команда получает ВСЕ ДАННЫЕ ПРОЕКТА, что может быть большим объемом.

### 2. Именование методов
- Всегда используйте полные имена методов Unity (например, `GameObject.DestroyImmediate` вместо `DestroyImmediate`)

### 3. Шаги перед выполнением
- Очистите консоль с помощью `Debug.ClearDeveloperConsole()`
- Добавьте уникальный идентификатор в первое сообщение лога (например, метку времени или ID команды)
- Это гарантирует, что мы сможем отличить логи текущего выполнения от предыдущих

### 4. Проверка ошибок
- Проверьте флаг `executionSuccess` в результате
- Ищите ошибки в массиве ошибок текущего выполнения
- Убедитесь, что логи соответствуют нашему выполнению, проверив наличие нашего уникального идентификатора

### 5. Работа с элементами UI
- Используйте `GetComponent<RectTransform>()`, чтобы гарантировать получение компонента RectTransform
- Проверьте наличие компонентов перед их использованием

### 6. Лучшие практики логирования
- Начинайте с лога с уникальным идентификатором
- Логируйте важные изменения состояния и обнаружение компонентов
- Используйте соответствующие уровни логов:
  - `Debug.Log` для обычного потока
  - `Debug.LogWarning` для потенциальных проблем
  - `Debug.LogError` для ошибок

### 7. Поиск GameObject'ов
- НИКОГДА не используйте `GameObject.Find()` для поиска потенциально неактивных объектов, так как он ищет только по активным GameObject'ам
- Вместо этого используйте `transform.Find()`, который может находить как активных, так и неактивных детей в иерархии
- При изменении неактивных объектов:
  1. Используйте `transform.Find()` для получения ссылки
  2. Временно активируйте его
  3. Внесите необходимые изменения
  4. Верните исходное состояние активности

## Управление ресурсами в Unity

### 1. Управление спрайтами
- Настройте параметры импорта спрайтов правильно:
  - Установите соответствующее количество пикселей на единицу
  - Выберите корректный режим фильтрации (Point для пиксель-арта, Bilinear для сглаженной графики)
  - Установите сжатие в соответствии с требованиями платформы

### 2. Управление префабами
- При изменении префабов:
  - Сначала откройте префаб для редактирования
  - Вносите изменения в режиме просмотра префаба
  - Сохраните изменения с помощью `EditorUtility.SetDirty` и `AssetDatabase.SaveAssets`
- Предусмотрите механизмы резервного копирования при доступе к ресурсам префаба:
```csharp
// Пример: Загрузка спрайта с резервным вариантом
var sprite = Resources.Load<Sprite>("Path/To/Sprite");
if (sprite == null)
{
    // Резервный вариант: получение ссылки из другого компонента
    sprite = fallbackComponent.GetComponent<SpriteRenderer>()?.sprite;
}
```

## Пример использования

```csharp
// Очистка предыдущих логов и добавление уникального идентификатора
Debug.ClearDeveloperConsole();
string commandId = System.DateTime.Now.Ticks.ToString();
Debug.Log($"Starting command execution [{commandId}]");

// Поиск и изменение объекта
var canvas = GameObject.Find("Canvas");
if (canvas != null) {
    Debug.Log($"[{commandId}] Found Canvas");
    var ui = canvas.transform.Find("UI");  // Может находить неактивные объекты
    if (ui != null) {
        var rectTransform = ui.GetComponent<RectTransform>();
        if (rectTransform != null) {
            rectTransform.anchoredPosition = new Vector2(0, -50);
            Debug.Log($"[{commandId}] Updated UI position");
        }
    }
} else {
    Debug.LogError($"[{commandId}] Canvas not found in scene");
}
```