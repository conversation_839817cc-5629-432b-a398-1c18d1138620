---
description: 
globs: 
alwaysApply: false
---
# Odin Inspector Custom Editors

## Custom Value Drawers

### Basic Value Drawer

```csharp
// Base class for creating custom display of a specific type
public class MyCustomTypeDrawer<T> : OdinValueDrawer<T>
{
    protected override void DrawPropertyLayout(GUIContent label)
    {
        // Get the current property value
        T value = this.ValueEntry.SmartValue;

        // Draw your custom interface
        // Use GUILayout or EditorGUILayout
    }
}
```

### Example Value Drawer for Structure

```csharp
[Serializable]
public struct MyStruct
{
    public float X;
    public float Y;
}

public class MyStructDrawer : OdinValueDrawer<MyStruct>
{
    protected override void DrawPropertyLayout(GUIContent label)
    {
        // Get the drawing area
        Rect rect = EditorGUILayout.GetControlRect();

        // Draw label if it exists
        if (label != null)
        {
            rect = EditorGUI.PrefixLabel(rect, label);
        }

        // Get and modify the value
        MyStruct value = this.ValueEntry.SmartValue;

        GUIHelper.PushLabelWidth(20);
        value.X = EditorGUI.Slider(rect.AlignLeft(rect.width * 0.5f), "X", value.X, 0, 1);
        value.Y = EditorGUI.Slider(rect.AlignRight(rect.width * 0.5f), "Y", value.Y, 0, 1);
        GUIHelper.PopLabelWidth();

        this.ValueEntry.SmartValue = value;
    }
}
```

### Generic Constraints

```csharp
// For any class with a public parameterless constructor
public class ClassDrawer<T> : OdinValueDrawer<T> where T : class, new()
{
    // ...
}

// For specific type and its descendants
public class ItemDrawer<T> : OdinValueDrawer<T> where T : Item
{
    // ...
}

// For interfaces
public class InterfaceDrawer<T> : OdinValueDrawer<T> where T : IMyInterface
{
    // ...
}

// For collections
public class ItemListDrawer<TList, TElement> : OdinValueDrawer<TList>
    where TList : IList<TElement>
    where TElement : Item
{
    // ...
}
```

### Additional Drawing Conditions

```csharp
public class CustomDrawer<T> : OdinValueDrawer<T> where T : Item
{
    public override bool CanDrawTypeFilter(Type type)
    {
        return type != typeof(SpecificType);
    }
}
```

## Custom Attribute Drawers

### Creating an Attribute

```csharp
public class HealthBarAttribute : Attribute
{
    public float MaxHealth;

    public HealthBarAttribute(float maxHealth)
    {
        this.MaxHealth = maxHealth;
    }
}
```

### Creating a Drawer for the Attribute

```csharp
public class HealthBarAttributeDrawer : OdinAttributeDrawer<HealthBarAttribute, float>
{
    protected override void DrawPropertyLayout(GUIContent label)
    {
        // Draw the standard field
        this.CallNextDrawer(label);

        // Get the drawing area
        Rect rect = EditorGUILayout.GetControlRect();

        // Draw the health bar
        float width = Mathf.Clamp01(this.ValueEntry.SmartValue / this.Attribute.MaxHealth);
        SirenixEditorGUI.DrawSolidRect(rect, new Color(0f, 0f, 0f, 0.3f), false);
        SirenixEditorGUI.DrawSolidRect(rect.SetWidth(rect.width * width), Color.red, false);
        SirenixEditorGUI.DrawBorders(rect, 1);
    }
}
```

## Custom Value Drawer with Attribute

### Using CustomValueDrawer

```csharp
public class MyComponent : MonoBehaviour
{
    [CustomValueDrawer("MyCustomDrawerStatic")]
    public float CustomDrawerStatic;

    [CustomValueDrawer("MyCustomDrawerInstance")]
    public float CustomDrawerInstance;

    private static float MyCustomDrawerStatic(float value, GUIContent label)
    {
        return EditorGUILayout.Slider(label, value, 0f, 10f);
    }

    private float MyCustomDrawerInstance(float value, GUIContent label)
    {
        return EditorGUILayout.Slider(label, value, this.minValue, this.maxValue);
    }
}
```

## Attribute Processors

### Creating an Attribute Processor

```csharp
public class MyProcessedClassAttributeProcessor : OdinAttributeProcessor<MyProcessedClass>
{
    // Process attributes of the class itself
    public override void ProcessSelfAttributes(InspectorProperty property, List<Attribute> attributes)
    {
        attributes.Add(new InfoBoxAttribute("Dynamically added attributes!"));
        attributes.Add(new InlinePropertyAttribute());
    }

    // Process attributes of child properties
    public override void ProcessChildMemberAttributes(
        InspectorProperty parentProperty,
        MemberInfo member,
        List<Attribute> attributes)
    {
        // Add attributes to all child elements
        attributes.Add(new HideLabelAttribute());
        attributes.Add(new BoxGroupAttribute("Box", showLabel: false));

        // Add specific attributes
        if (member.Name == "Mode")
        {
            attributes.Add(new EnumToggleButtonsAttribute());
        }
        else if (member.Name == "Size")
        {
            attributes.Add(new RangeAttribute(0, 5));
        }
    }
}
```

## Important Notes

1. All custom drawers must be placed in the Editor folder or within a #if UNITY_EDITOR block
2. Drawers must handle cases where the label is null
3. Use generic constraints for Value Drawers to optimize performance
4. Attribute Processors only work with inspector attributes, they don't affect serialization
5. When creating Custom Editors, use GUILayout and EditorGUILayout for compatibility with Unity's IMGUI system

## Useful Classes and Methods

- `ValueEntry.SmartValue` - get/set property value
- `CallNextDrawer(label)` - call the next drawer in the chain
- `EditorGUILayout.GetControlRect()` - get drawing area
- `SirenixEditorGUI` - UI utilities from Odin
- `GUIHelper` - helper methods for GUI