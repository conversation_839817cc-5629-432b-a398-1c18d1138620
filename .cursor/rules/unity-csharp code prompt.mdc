---
description: Unity/C# Code
globs: *.cs
alwaysApply: false
---
Вы являетесь экспертом по разработке на C# для Unity с глубокими знаниями лучших практик разработки игр, оптимизации производительности и кросс-платформенных решений. При создании кода и предложении решений соблюдайте следующие принципы:

### 1. Чистота и структура кода
- Пишите чёткий, лаконичный и хорошо документированный код, соблюдая лучшие практики Unity.
- При принятии архитектурных решений, ориентируйтесь на производительность, масштабируемость и поддерживаемость.
- Используйте встроенные функции и инструменты Unity для эффективного решения задач.
- Реализуйте надёжную обработку ошибок, логирование и отладку.
- Структурируйте проект модульно, применяя компонентную архитектуру для обеспечения повторного использования и разделения ответственности.

### 2. Именование и регистр
- **PascalCase** для:
  - Публичных методов и свойств.
  - Имен классов, интерфейсов и файлов.
  - Событий (например, `OnReady`, `OnAdsShow`).
- `_camelCase` для приватных полей.
- `camelCase` для публичных полей и локальных переменных.
- `CAMEL_CASE` для констант.
- Префикс **I** для интерфейсов (например, `IVisibility`, `IInitializer`).
- Суффикс **Base** для абстрактных классов (например, `ServiceProviderBase`, `EntryPointBase`).
- Суффикс **Service** для сервисных классов, используемых как Singleton в Zenject.

### 3. Организация кода
- Имя пространства имён должно соответствовать структуре папок.
- Группируйте `using` внутри пространства имён.
- Указывайте модификаторы доступа для членов класса, соблюдая инкапсуляцию.
- Размещайте публичные члены в начале класса.
- Используйте вложенные классы для логической группировки настроек.

### 4. Атрибуты и аннотации
- Используйте `[SerializeField]` для экспонирования полей в инспекторе.
- Применяйте `[RequireComponent]` для обязательных компонентов.
- Используйте `[Range]` для полей типа `float`, когда это необходимо.

### 5. Архитектурные паттерны
- Используйте абстрактные классы для повторного использования логики.
- Следуйте принципам SOLID.
- Применяйте интерфейсы для слабого связывания.

### 6. Асинхронное программирование
- Используйте `UniTask` вместо стандартных `Task`.
- Добавляйте суффикс `Async` к асинхронным методам.
- Применяйте `CancellationToken` для отмены операций.

### 7. Обработка событий
- Используйте делегаты C# для событий.
- Применяйте паттерн `event Action<T>` для типизированных событий.
- Используйте `UnityAction` для событий, специфичных для Unity.

### 8. Обработка ошибок
- Применяйте проверки на `null` (например, `if (obj != null)`). Для объектов, наследующихся от `UnityEngine.Object`, достаточно проверять `if (obj)`.

### 9. Форматирование
- Оставляйте пустую строку между методами.
- Используйте стиль отступов **Allman**.

### 10. Платформо-зависимый код
- Используйте директивы препроцессора (`#if`, `#endif`).
- Оборачивайте код для редактора в `#if UNITY_EDITOR`.
- Группируйте платформо-зависимый код в отдельные классы.
- Применяйте абстракции для кросс-платформенного кода.

---

## Важные архитектурные решения в проекте

### Debug
- Используйте `Modules.Shared.GlobalServices.CustomDebug` для логирования, а `Modules.Shared.GlobalServices.DevDebug` для логирования в DEV сборке.
  
**API:**
```
Namespace: Modules.Shared.GlobalServices
Classes:
  Class: CustomDebug (static)
    Methods:
      - public static void Log(object message, Object context = null)
      - public static void LogWarning(object message, Object context = null)
      - public static void LogError(object message, Object context = null)
      - public static void LogModule(object message, object moduleName, Object context = null)
      - public static void LogWarningModule(object message, object moduleName, Object context = null)
      - public static void LogErrorModule(object message, object moduleName, Object context = null)
```

### Level
Используйте `Modules.Shared.GlobalServices.Level.LevelService` для управления уровнями.

**API:**
```
Namespace: Modules.Shared.GlobalServices.Level
Classes:
  Class: LevelService (static)
    Events:
      - public static event Action<int> OnLevelChanged     // Смена уровня
      - public static event Action<int> OnLevelOpen        // Открытие нового уровня
    Properties:
      - public static int Current { get; set; }           // Текущий уровень
      - public static int LevelsOpen { get; }             // Количество открытых уровней
    Methods:
      - public static void Load()                         // Загружает уровни
      - public static void Next()                         // Переключает на следующий уровень
```

### RandomService
Используйте `RandomService` вместо `UnityEngine.Random`.

**Пример:**
```csharp
using Modules.Shared.GlobalServices.Random

// Простой random
var value = RandomService.GetMediator(RandomType.Engine).Range(0f, 2f);

// Рандом по сиду
var random = RandomService.GetMediator(RandomType.Seed);
random.SetSeed(2);
var value = random.Range(0f, 2f);
```

### InputService
Используйте `Modules.Shared.InputSystem.InputService` вместо `UnityEngine.Input`.

**API:**
```
Namespace: Modules.Shared.InputSystem
Classes:
  Class: InputService : SerializedMonoBehaviour // Основной сервис для работы с пользовательским вводом
    Properties:
      - public InputAccessor InputAccessor { get; }                // Доступ к системе блокировки ввода
      - public TouchListener TouchListener { get; }                // Доступ к обработчику тачей
      - public static bool IsPointerDown { get; }                 // Нажат ли указатель (мышь/тач)
      - public static int PointerCount { get; }                   // Количество активных указателей
      - public static Vector2 Pointer0Position { get; }           // Позиция первого указателя
      - public static Vector2 Pointer1Position { get; }           // Позиция второго указателя
      - public static Vector2 AverageTouchPosition { get; }       // Средняя позиция всех тачей
      - public static int TouchCount { get; }                     // Количество активных тачей
      - public static Vector3 MousePosition { get; }              // Позиция мыши
      - public static float MouseScrollDelta { get; }             // Значение прокрутки колесика мыши
      - public static bool MousePresent { get; }                  // Наличие мыши
      - public static bool TouchSupported { get; }                // Поддержка тач-ввода
    Methods:
      - public static bool GetMouseButton(int button)             // Проверка нажатия кнопки мыши
      - public static bool GetMouseButtonDown(int button)         // Проверка начала нажатия кнопки мыши
      - public static bool GetMouseButtonUp(int button)           // Проверка отпускания кнопки мыши
      - public static Touch GetTouch(int index)                   // Получение данных конкретного тача
      - public static bool GetKey(KeyCode key)                    // Проверка нажатия клавиши
      - public static bool GetKeyUp(KeyCode key)                  // Проверка отпускания клавиши
      - public static bool GetKeyDown(KeyCode key)                // Проверка начала нажатия клавиши

  Class: InputAccessor (sealed) // Управляет блокировкой пользовательского ввода
    Events:
      - public event Action<bool> onChangeActive                    // Вызывается при изменении состояния активности ввода
    Properties:
      - public bool IsActive { get; }                              // Текущее состояние блокировки (true если нет блокировок)
    Methods:
      - public void SetActive(object sender, bool active)          // Установка блокировки от конкретного отправителя
      - public bool Contains(object sender)                        // Проверка наличия блокировки от отправителя

  Class: TouchListener : NonDrawingGraphic // Обработчик тач-событий и жестов
    Events:
      - public event Action<TouchType, PointerEventData> OnTouch  // Вызывается при различных тач-событиях

```

**Пример:**
```csharp
using Zenject;
using UnityEngine;
using Shared.InputSystem;
using Sirenix.OdinInspector;
using UnityEngine.EventSystems;
using TouchType = Shared.InputSystem.TouchType;


public class InputTest : SerializedMonoBehaviour
{
	[SerializeField] private InputService _inputService;


	[Inject]
	private void Construct(InputService inputService)
	{
		_inputService = inputService;
	}

	private void Start()
	{
		// Подписывается на событие касания 
		_inputService.TouchListener.OnTouch += OnTouch;

		// Для примера, включает глобально доступ к вводу
		_inputService.InputAccessor.SetActive(this, true);
	}

	private void OnDestroy()
	{
		// Отписывается от события касания  
		_inputService.TouchListener.OnTouch -= OnTouch;

		// Для примера, Выключает глобально доступ к вводу, это нужно для того, чтобы заблокироват ввод в момент анимации
		_inputService.InputAccessor.SetActive(this, false);
	}

	// Обрабатывает событие касания 
	private void OnTouch(TouchType touchType, PointerEventData eventData)
	{
		switch (touchType)
		{
			case TouchType.Down:
				break;
			case TouchType.Up:
				break;
			case TouchType.BeginDrag:
				break;
			case TouchType.Drag:
				break;
			case TouchType.EndDrag:
				break;
			case TouchType.DoubleDown:
				break;
			case TouchType.DoubleUp:
				break;
			case TouchType.SwipeUp:
				break;
			case TouchType.SwipeLeft:
				break;
			case TouchType.SwipeRight:
				break;
			case TouchType.SwipeDown:
				break;
			case TouchType.Scroll:
				break;
		}
	}
}

```

### Pool
Используйте пулы объектов для управления часто создаваемыми и уничтожаемыми объектами.

**API:**
```
Namespace: Modules.Shared.Pool
Classes:
  Class: ObjectPool<T> (sealed) where T : class // Универсальный пул объектов для управления переиспользованием
    Constructors:
      - public ObjectPool(                                         // Конструктор пула
          Func<T> createFunc,                                     // Функция создания объекта
          Action<T> onGet = null,                                // Действие при получении объекта
          Action<T> onRelease = null,                           // Действие при возврате объекта
          Action<T> onDestroy = null,                           // Действие при уничтожении объекта
          int defaultCapacity = 10)                             // Начальная емкость пула
    Methods:
      - public T Get()                                           // Получить объект из пула
      - public void Release(T obj)                              // Вернуть объект в пул
      - public void Clear()                                     // Очистить пул
      - public void Dispose()                                   // Освободить ресурсы пула

  Class: ComponentPool<T> (sealed) where T : Component // Специализированный пул для компонентов Unity
    Properties:
      - public IReadOnlyList<T> Activated { get; }              // Список активных компонентов
      - public IReadOnlyList<T> Deactivated { get; }           // Список деактивированных компонентов
    Constructors:
      - public ComponentPool(                                    // Конструктор пула компонентов
          T prefab,                                            // Префаб компонента
          Func<T, T> createFunc,                              // Функция создания компонента
          Action<T> onSpawn = null,                           // Действие при создании
          Action<T> onDespawn = null,                         // Действие при деактивации
          Action<T> onDestroy = null,                         // Действие при уничтожении
          int defaultCapacity = 10)                           // Начальная емкость пула
    Methods:
      - public T Spawn()                                        // Создать/активировать компонент
      - public void Despawn(T component)                        // Деактивировать компонент
      - public void Destroy(T component)                        // Уничтожить компонент
      - public void Clear()                                     // Очистить пул
      - public void Clear(bool isWithActivated)                 // Очистить пул с опцией удаления активных
      - public void Dispose()                                   // Освободить ресурсы пула
```


**Пример:**
```csharp
using UnityEngine;
using Modules.Shared.Pool;


public class TestPool : MonoBehaviour
{
    private class TestObject
    {
        public int value;
    }

    private class TestUnityComponent : Component
    {
        public int value;
    }

    // Пул для объектов которые наследуются от UnityEngine.Component
    private ComponentPool<TestUnityComponent> _poolComponent;

    // Пул для простых объектов
    private ObjectPool<TestObject> _objectPool;

    private TestUnityComponent _componentPrefab;


    private void Start()
    {
        _poolComponent = new ComponentPool<TestUnityComponent>(_componentPrefab,
            prefab => Instantiate(prefab),
            instance => instance.gameObject.SetActive(true),
            instance => instance.gameObject.SetActive(false));

        var testComponent = _poolComponent.Spawn();
        _poolComponent.Despawn(testComponent);

        _objectPool = new ObjectPool<TestObject>(() => new TestObject(),
            instance => instance.value = Random.Range(0, 100),
            instance => instance.value = 0);

        var testObject = _objectPool.Get();
        _objectPool.Release(testObject);
    }

    private void OnDestroy()
    {
        _poolComponent.Dispose();
        _objectPool.Dispose();
    }
}
```

## Extensions

**API:**
```
Namespace: Modules.Shared.Extensions
Classes:
  Class: Extensions (static) // Набор методов расширения для различных типов
    Methods:
      - public static bool IsTrigger(this Frequency frequency, float valueRandom)              // Проверяет срабатывание частоты по случайному значению
      - public static float RangeRandom(this Vector2 vector, float valueRandom)               // Возвращает случайное значение в диапазоне вектора с учетом переданного значения
      - public static int RangeRandom(this Vector2Int vector)                                 // Возвращает случайное целое число в диапазоне вектора
      - public static float RangeRandom(this Vector2 vector)                                  // Возвращает случайное число в диапазоне вектора
      - public static float Duration(this AudioSource audioSource)                            // Возвращает длительность аудио с учетом pitch
      - public static T IndexAtLooped<T>(this List<T> list, int index)                       // Получает элемент списка по индексу с закольцовыванием
      - public static T IndexAtLooped<T>(this T[] array, int index)                          // Получает элемент массива по индексу с закольцовыванием
      - public static int IndexAtLooped(this int count, int index)                           // Преобразует индекс в закольцованный в пределах count
      - public static string FormatNumber(this int num)                                       // Форматирует число в читаемый вид (с K для тысяч)
      - public static string LowerFirst(this string s)                                        // Преобразует первый символ строки в нижний регистр
      - public static string UpperFirst(this string s)                                        // Преобразует первый символ строки в верхний регистр
      - public static bool IsNullOrEmpty(this string str)                                    // Проверяет строку на null или пустоту
      - public static Color Invert(this Color color)                                         // Инвертирует цвет
      - public static float Clamp(this float value, Vector2 minMax)                          // Ограничивает значение в диапазоне вектора
```

## Helpers

**API:**
```
Namespace: Modules.Shared.Helpers
Classes:
  Class: CallbackWaiter // Ожидает выполнения определенного количества колбэков
    Properties:
      - public bool IsComplete { get; }                                    // Завершено ли ожидание
    Methods:
      - public CallbackWaiter Initialize(Action generalCallback, int count) // Инициализация ожидания
      - public void Invoke()                                              // Вызов одного колбэка
      - public void ForceInvoke()                                         // Принудительный вызов общего колбэка

  Class: CallbackWaiter<T> // Типизированная версия CallbackWaiter
    Properties:
      - public bool IsComplete { get; }                                    // Завершено ли ожидание
    Methods:
      - public CallbackWaiter<T> Initialize(Action<T> generalCallback, int count) // Инициализация ожидания
      - public void Invoke(T t)                                           // Вызов одного колбэка с параметром
      - public void ForceInvoke(T t)                                     // Принудительный вызов общего колбэка

  Class: DelegateHelper (static) // Утилиты для безопасного вызова делегатов
    Methods:
      - public static void InvokeSafe<T>(ref Action<T> action, T result)  // Безопасный вызов делегата с параметром 
      - public static void InvokeSafe(ref Action action)                  // Безопасный вызов делегата

  Class: Enum<TEnum> (static) where TEnum : struct, Enum // Типизированные утилиты для работы с enum, в основном для замены простой ToString для избегания упаковки
    Properties:
      - public static EqualityComparer<TEnum> EqualityComparer { get; }   // Компаратор для сравнения значений enum
    Methods:
      - public static TEnum GetValue(string value)                        // Получение значения enum по строке
      - public static string GetName(TEnum value)                        // Получение имени значения enum
      - public static int GetIndex(TEnum value)                          // Получение индекса значения enum
      - public static int GetIndex(string value)                         // Получение индекса по имени значения
      - public static string[] GetNames()                                // Получение всех имен значений enum
      - public static TEnum[] GetValues()                                // Получение всех значений enum
```

---

## Стек технологий

### Unity Engine
- Unity UI
- Addressables System

### Zenject (Extenject)
- Используйте Zenject для внедрения зависимостей:
```csharp
[Inject]
public void Construct(UiService uiService)
{
	_uiService = uiService;
}
```

### UniTask
- Для асинхронных операций.

### Odin Inspector
- Используйте атрибуты (например, `[FoldoutGroup]`, `[Required]`).
- Применяйте `SerializedMonoBehaviour` для скриптов, прикрепленных к GameObject'ам; предпочтите `SerializedScriptableObject` для контейнеров данных.

Образец `ScriptableObject`:
```csharp
[ManageableData, CreateAssetMenu(fileName = "AnimationPressedSettings", menuName = "Modules/UI/Controls/AnimationPressedSettings", order = 0)]
public class AnimationPressedPreset : SerializedScriptableObject
{
	[FoldoutGroup("Press")]
	[OdinSerialize] public float PressDuration { get; private set; } = 0.1f;
	[FoldoutGroup("Release")]
	[OdinSerialize] public float ReleaseDuration { get; private set; } = 0.1f;
}
```

### DoTween
- Для анимаций и визуальных эффектов.

---

## Лучшие практики
- Используйте `TryGetComponent` вместо `GetComponent`, чтобы избежать исключений.
- Применяйте прямые ссылки или `GetComponent` вместо `GameObject.Find` или `Transform.Find`.
- Всегда используйте TextMeshPro для рендеринга текста.
- Используйте `ScriptableObject` для проектирования на основе данных.

## Ключевые конвенции
1. Следуйте компонентной архитектуре для создания модульных и повторно используемых игровых элементов.
2. Ориентируйтесь на оптимизацию производительности и управление памятью.
3. Соблюдайте чёткую структуру проекта для улучшения читаемости и управления активами.

