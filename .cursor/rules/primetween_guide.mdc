---
description: 
globs: 
alwaysApply: false
---
# PrimeTween Guide

PrimeTween is a high-performance, allocation-free animation library for Unity. Animate anything with one line of code and tweak properties in the Inspector.

## Core Features

- Zero runtime allocations
- Inspector integration for all animation properties
- Support for sequences, coroutines, and async/await
- Built-in shake animations
- Custom tweens for any type
- Speed-based animations

## Basic Animations

```csharp
// Position animation
Tween.PositionY(transform, endValue: 10, duration: 1, ease: Ease.InOutSine);

// Rotation animation
Tween.Rotation(transform, endValue: Quaternion.Euler(0, 90, 0), duration: 1);

// Euler angles animation
Tween.EulerAngles(transform, startValue: Vector3.zero, endValue: new Vector3(0, 360), duration: 1);
```

## Shake Animations

```csharp
// Camera shake
Tween.ShakeCamera(camera, strengthFactor: 0.5f);

// Position shake
Tween.ShakeLocalPosition(transform, strength: new Vector3(0, 1), duration: 1, frequency: 10);

// Rotation shake
Tween.ShakeLocalRotation(transform, strength: new Vector3(0, 0, 15), duration: 1, frequency: 10);
```

## Callbacks and Delays

```csharp
// Completion callback
Tween.Scale(transform, endValue: 0, duration: 1)
    .OnComplete(target: this, target => target.OnCompleted());

// Delay
Tween.Delay(target: this, duration: 1f, target => target.OnDelayComplete());
```

## Animation Cycles

```csharp
// Repeat animation twice with yoyo effect
Tween.PositionY(transform, endValue: 10, duration: 0.5f, cycles: 2, cycleMode: CycleMode.Yoyo);
```

### CycleMode Options
- Restart: Restarts from beginning
- Yoyo: Animates forth and back
- Incremental: Increases endValue each cycle
- Rewind: Reverses time on backward cycle

## Sequences

```csharp
Sequence.Create(cycles: 10, CycleMode.Yoyo)
    .Group(Tween.PositionX(transform, endValue: 10f, duration: 1.5f))
    .Group(Tween.Scale(transform, endValue: 2f, duration: 0.5f, startDelay: 1))
    .Chain(Tween.Rotation(transform, endValue: new Vector3(0f, 0f, 45f), duration: 1f))
    .ChainDelay(1)
    .Insert(atTime: 0.5f, Tween.Color(image, Color.red, duration: 0.5f));
```

## Inspector Integration

```csharp
[SerializeField] TweenSettings<float> tweenSettings;
[SerializeField] ShakeSettings shakeSettings;

// Use settings from Inspector
Tween.PositionY(transform, tweenSettings);
Tween.ShakeLocalPosition(transform, shakeSettings);
```

## Custom Tweens

```csharp
// Animate any float value
Tween.Custom(this, 0, 10, duration: 1, (target, newVal) => target.floatField = newVal);

// Animate any Color value
Tween.Custom(this, Color.white, Color.black, duration: 1, (target, newVal) => target.colorField = newVal);
```

## Advanced Features

### TimeScale Control
```csharp
// Set custom timeScale for tween
tween.timeScale = 2f;

// Animate global timeScale
Tween.GlobalTimeScale(endValue: 0.5f, duration: 1f);
```

### Fixed Update
```csharp
// Update animation in FixedUpdate
Tween.PositionX(transform, endValue: 10f, new TweenSettings(duration: 1f, useFixedUpdate: true));
```

### Speed-Based Animations
```csharp
// Move at constant speed
Tween.LocalPositionAtSpeed(transform, endValue: targetPos, speed: 5f);
```

## Controlling Animations

```csharp
// Stop animation
tween.Stop();

// Complete animation
tween.Complete();

// Pause/Resume
tween.isPaused = true;

// Set progress
tween.progress = 0.5f;

// Set elapsed time
tween.elapsedTime = 0.5f;
```

## Important Properties

- `isAlive`: Check if tween is active
- `progress`: Current normalized progress (0-1)
- `elapsedTime`: Time elapsed since start
- `duration`: Total duration of animation
- `interpolationFactor`: Current interpolation factor

## Best Practices

1. Use Inspector integration for tweaking animations
2. Pass target reference to avoid allocations in callbacks
3. Use Sequence for complex animation chains
4. Prefer speed-based animations for consistent motion
5. Use ShakeSettings for customizable shake effects
```