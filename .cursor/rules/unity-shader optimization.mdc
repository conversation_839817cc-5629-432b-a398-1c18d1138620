---
description: 
globs: 
alwaysApply: false
---
# Comprehensive Guide to Unity HLSL/Cg Shader Optimization for Mobile Devices

Optimizing HLSL/Cg shaders is vital for performant mobile games in Unity. Mobile GPUs face significant constraints compared to desktops, mainly in power, heat, memory bandwidth, and processing power. This guide combines best practices for balancing visuals and performance on mobile platforms.

## 1. Understanding Mobile GPU Constraints and Rendering

### Hardware Limitations
Mobile GPUs have strict power, thermal, and size limits, resulting in lower memory bandwidth, ALU performance, and texturing capabilities than desktops. These differences demand optimization strategies focused on minimizing power and bandwidth.

### Mobile Rendering Pipeline (Unity Context)
Unity usually cross-compiles HLSL/Cg shaders to GLSL ES for mobile platforms. Understanding this translation is key, as optimizations often relate to how mobile hardware handles the resulting GLSL ES.

### Common Performance Bottlenecks
-   **Fillrate:** Mobile apps are often fillrate-bound; performance is limited by the pixels the GPU must process (influenced by resolution, shader complexity, overdraw). Test by lowering resolution; if faster, optimize shader complexity and reduce overdraw.
-   **Memory Bandwidth:** Accessing textures and memory is often a bottleneck. Test by reducing texture quality/resolution; if faster, optimize texture usage, formats, and sampling.
-   **Shader Complexity:** Complex shaders with many instructions or costly operations frequently cause poor performance.
-   **Draw Calls:** Primarily a CPU bottleneck, but excessive draw calls also affect GPU state changes.

## 2. General Optimization Principles

-   **Simplicity First:** Use the simplest possible shaders. Start with Unity's Mobile shaders or URP's lightweight Lit/Unlit shaders, which are mobile-optimized. Avoid unneeded features.
-   **CPU vs. GPU:** Perform calculations not requiring per-pixel/vertex GPU execution on the CPU (C# scripts) and pass results as uniforms.
-   **Target Device Testing:** Always profile and test on actual target mobile hardware. Performance varies greatly between devices and differs from the editor.

## 3. Shader Code Optimization Techniques

### 3.1. Precision Control (Data Types)

-   **Use Appropriate Precision:** Mobile GPUs benefit greatly from lower precision data types.
    -   `float` (highp): Use *only* for essentials like world positions and texture coordinates.
    -   `half` (mediump): Use for most calculations (colors, normalized vectors, HDR). Reduces bandwidth and computation.
    -   `fixed` (lowp): Use for simple color/texture ops where lowest precision is okay. Support varies.
-   **HLSL/Cg to GLSL ES:** `float`/`half`/`fixed` generally map to `highp`/`mediump`/`lowp`.
-   **Syntax:** Unity lacks full support for suffixes like `2.0h`. Use explicit casts: `half(2.0)`.

```hlsl
// BAD: Using float for everything
float4 color;
float3 normal;
float2 uv;

// GOOD: Using appropriate precision
half4 color;      // half is sufficient for colors
half3 normal;     // half is sufficient for normalized vectors
float2 uv;        // keep float for texture coordinates
```

### 3.2. Mathematical Optimizations

-   **Avoid Expensive Functions:** Minimize or replace costly functions (`pow`, `exp`, `log`, `cos`, `sin`, `tan`, `sqrt`). Consider alternatives:
    -   Pre-calculated Lookup Textures (LUTs).
    *   Simpler math approximations.
-   **MAD Instructions:** Structure calculations as Multiply-Add (`a * b + c`) where possible for single-instruction execution. Avoid forms like `(a + b) * c`.
-   **Built-in Functions:** Use Unity's HLSL functions (`normalize`, `dot`, `saturate`, `lerp`). Often optimized at driver/hardware level.
-   **Scalar vs. Vector:** Separate scalar/vector math if it reduces operations: `(vec4 * vec4) * (scalar * scalar)` might be faster than `vec4 * scalar * vec4 * scalar`.
-   **Abs/Neg/Saturate:** `abs()`, `neg()` on inputs, and `saturate()` on outputs are often very cheap or free.
-   **Vectorization:** Use vector operations to leverage SIMD processing.
-   **Integer Math:** Avoid integers except where needed (e.g., array indexing); floats are often faster on GPUs.

```hlsl
// BAD: Using expensive math functions
float specular = pow(max(0, dot(reflect(-lightDir, normal), viewDir)), _Shininess);

// GOOD: Using lookup texture (example)
float2 lookupUV = float2(saturate(dot(normal, lightDir)), saturate(dot(halfDir, normal)));
float4 lighting = tex2D(_LightingLUT, lookupUV);
float specular = lighting.a; // Assuming specular stored in alpha
```

### 3.3. Reducing Shader Complexity & Instructions

-   **Vertex vs. Fragment Shaders:** Move calculations from fragment (pixel) to vertex shader when interpolation works. Vertex shaders run per-vertex, fragment shaders per-pixel (usually far more pixels).

```hlsl
// Example: Moving Fresnel calculation to vertex shader

// BAD: Calculating Fresnel per-pixel
struct v2f {
    float4 pos : SV_POSITION;
    float3 worldPos : TEXCOORD0;
    float3 worldNormal : TEXCOORD1;
};
fixed4 frag(v2f i) : SV_Target {
    float3 viewDir = normalize(_WorldSpaceCameraPos - i.worldPos);
    // Calculation done per-pixel
    half fresnel = pow(1.0h - saturate(dot(viewDir, i.worldNormal)), 5.0h);
    return fixed4(fresnel.xxx, 1.0h);
}

// GOOD: Calculating Fresnel per-vertex
struct v2f {
    float4 pos : SV_POSITION;
    half fresnel : TEXCOORD0; // Interpolated result
};
v2f vert(appdata_base v) {
    v2f o;
    o.pos = UnityObjectToClipPos(v.vertex);
    float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
    half3 worldNormal = UnityObjectToWorldNormal(v.normal);
    float3 viewDir = normalize(_WorldSpaceCameraPos - worldPos);
    // Calculation done per-vertex
    o.fresnel = pow(1.0h - saturate(dot(viewDir, worldNormal)), 5.0h);
    return o;
}
fixed4 frag(v2f i) : SV_Target {
    // Use interpolated result
    return fixed4(i.fresnel.xxx, 1.0h);
}
```

-   **Minimize Instructions:** Every instruction adds GPU load. Simplify logic.
-   **Conditionals & Loops:** Use fragment shader branching (`if`/`else`) and loops cautiously; they can hurt performance. Try math alternatives (`lerp`, `step`) or pre-computation.
-   **Pre-computation:** Pre-calculate values not changing per-frame/pixel and store in textures or constants.

### 3.4. Mobile-Specific Operations to Avoid/Minimize

-   **Alpha Testing (`discard`, `clip`):** Avoid when possible, especially on PowerVR. Often disables early-Z culling. Prefer alpha blending or opaque materials. If required, use `clip()` sparingly.
-   **Writing to Depth (`SV_Depth`):** Modifying depth in fragment shaders usually disables early-Z culling, critical for mobile. Let hardware manage depth.
-   **ColorMask:** Can hurt performance on some mobile GPUs. Seek alternatives for controlling channel output.

## 4. Memory and Bandwidth Optimization

Memory bandwidth is often the main mobile GPU bottleneck. Optimize data transfer and access.

### 4.1. Constant Buffer (Uniform) Optimization

-   **Buffer Size:** Keep uniform buffers small. On GPUs like Mali, small buffers (e.g., < 128 bytes) might become registers (fast access).
-   **Layout & Alignment:** Respect GLSL alignment rules (scalar 4B, vec2 8B, vec3/4 16B). Group related fields for cache coherency.
-   **Conditional Compilation:** Use `#if defined(FEATURE)` in `CBUFFER` definitions for ubershaders to exclude unused uniforms, reducing size.
-   **Pack Constants:** Group scalar constants to reduce register use.
-   **Toggle Variables:** Use `int` for branching toggles (`if (_UseFeature)`). Use `half`/`float` for math toggles (`lerp(a, b, _BlendAmount)`).

```hlsl
// GOOD: Conditionally compiled fields in CBUFFER
CBUFFER_START(UnityPerMaterial)
    half4 _MainTex_ST;
    half4 _MainColor;

    #if defined(_NORMALMAP)
    half4 _NormalMap_ST;
    half _BumpScale;
    #endif

    #if defined(_EMISSION)
    half4 _EmissionMap_ST;
    half4 _EmissionColor;
    #endif
CBUFFER_END
```

### 4.2. Texture Memory Optimization

(See also Section 6: Texture Optimization)

-   **Reduce Reads:** Minimize texture samples (`tex2D`) in fragment shaders. Sample once, reuse results for same texture/coords.
-   **Texture Arrays:** Consider for sampling similar textures with different parameters.
-   **Filtering:** Use `Bilinear` where possible. Avoid `Trilinear`/`Anisotropic` unless needed; they use more bandwidth.
-   **1D Lookups:** Use 1D texture fetches if possible; can be more efficient than 2D.

### 4.3. Varying (Interpolator) Optimization

Varyings pass interpolated data from vertex to fragment shader.

-   **Precision:** Use `half` for most varyings (UVs, colors, normals). `half` is often 2 bytes here (not 4 like in constants). Keep `float` for `SV_POSITION`, maybe world positions.
-   **Packing:** Pack related data into fewer varyings (e.g., two `half2` UVs into one `half4`; tangent `xyz` + bitangent sign `w` into `half4`).
-   **GPU Architecture:**
    -   *PowerVR:* Varyings relatively cheap. More varyings might be okay if they save fragment math.
    -   *Mali/Adreno:* Smaller varyings reduce bandwidth. Optimize for fewer/smaller varyings.

```hlsl
// GOOD: Using half precision and packing varyings
struct v2f {
    float4 pos : SV_POSITION; // Keep float for position
    half4 uv : TEXCOORD0;     // Packed uv0 (xy) and uv1 (zw)
    half3 normal : TEXCOORD1; // half is sufficient
    // Pack tangent.xyz and bitangent sign w
    half4 tangentAndBitangentSign : TEXCOORD2;
};
```

### 4.4. Framebuffer & Render Target Optimization

-   **Format/Bit Depth:** Use lowest practical bit-depth for render targets (e.g., `RGBA16`, `RGB565` instead of `RGBA32`). Avoid alpha channels (`RGB` vs `RGBA`) if unneeded.
-   **Multiple Render Targets (MRT):** Avoid on mobile where possible; increases bandwidth needs.
-   **Post-Processing Resolution:** Apply costly post-effects at lower resolution (e.g., half) and upscale to reduce processed pixels.

### 4.5. Bandwidth Reduction via Overdraw Minimization

Overdraw is rendering the same screen pixel multiple times per frame.

-   **Early-Z Culling:** Hardware feature discards hidden fragments *before* fragment shading. Enable by:
    -   Rendering opaque objects roughly front-to-back.
    -   *Not* writing depth (`SV_Depth`) in fragment shader.
    -   *Avoiding* alpha testing (`discard`/`clip`) in fragment shaders.
-   **Alpha Blending:** Use carefully; transparency reduces early-Z benefits and causes overdraw. Minimize transparent surfaces.

### 4.6. Optimizing for Tile-Based Rendering (TBR / TBDR)

Most mobile GPUs (PowerVR, Mali, Adreno) use Tile-Based Rendering.

-   **Minimize Writes:** Avoid rendering the same pixel multiple times (overdraw). Combine effects into fewer passes.
-   **Bandwidth Efficiency:** Reducing texture reads, using lower precision, moving calculations to vertex shader are especially good for TBR.
-   **(PowerVR specific):** Avoid `ColorMask`. Varyings cheap. Alpha test very costly.
-   **(Adreno specific):** Use lowest bit render targets. Optimize bandwidth. Variable bin size helps.
-   **(Mali specific):** Optimize tile efficiency (16x16 tiles). Reduce bandwidth. Minimize fragment complexity.

## 5. Lighting Optimization

-   **Minimize Realtime Lights:** Dynamic lights are very costly. Use few or none.
-   **Lightmapping:** Bake lighting for static objects into lightmaps. Most performant complex lighting. Use Mobile/Unlit (Supports Lightmap) or equivalent.
-   **Light Probes:** Provide approximate bounced lighting for dynamic objects in baked scenes.
-   **Reflection Probes:** Use sparingly. Use low resolutions, compressed formats. Consider simpler cubemaps.
-   **Simplify Lighting Models:** Use simpler math (e.g., Lambert) instead of complex models (e.g., PBR) if needed. Create custom simplified lighting functions or use URP simple Lit.
-   **Shadows:** Extremely expensive.
    -   Disable if possible.
    -   Use baked blob/projected shadows.
    -   If realtime: Use Hard, Low Res, minimal Distance, disable Cascades.

## 6. Texture Optimization

Textures greatly affect memory and bandwidth.

-   **Compression:** Use platform-specific formats:
    -   ASTC: Flexible, high quality, widely supported. Choose block size (4x4, 6x6, 8x8).
    -   ETC2: Standard on GLES 3.0+ Android.
    -   PVRTC: Older iOS (A7-).
    -   Use smallest acceptable format.
-   **Resolution:** Use smallest dimensions possible without quality loss. Halving dimensions cuts memory 75%. Use power-of-two sizes (256, 512).
-   **Mipmaps:** Enable for 3D model textures viewed at varying distances. Reduces bandwidth/aliasing for distant objects. Disable for UI/fixed-size sprites.
-   **Texture Atlasing:** Combine multiple small textures into one large atlas. Reduces draw calls by enabling material sharing. Use Unity's Sprite Atlas tool.
-   **Texture Packing:** Store multiple grayscale maps (Metallic, Roughness, AO, Height) in R, G, B, A channels of one texture. Reduces texture samples needed.
-   **Grayscale + Tinting:** Use grayscale textures, apply color tints in shader. Can save memory vs. multiple colored textures.
-   **Normal Map Packing:** Store normals using two channels (RG), reconstruct Z in shader (`normal.z = sqrt(...)`). Allows better 2-channel compression (BC5/DXT5nm).

```hlsl
// GOOD: Using packed mask map
sampler2D _MaskMap; // R=Metallic, G=Roughness, B=Occlusion, A=Height
// Usage:
half4 maskMap = tex2D(_MaskMap, uv);
half metallic = maskMap.r;
half roughness = maskMap.g; // Often 1.0 - smoothness
half occlusion = maskMap.b;
// half height = maskMap.a; // If using height

// GOOD: Reconstructing Z from RG normal map
sampler2D _NormalMap; // BC5/DXT5nm compressed RG map
half4 packedNormal = tex2D(_NormalMap, uv); // Might be xy channels
half3 normal;
normal.xy = packedNormal.xy * 2.0h - 1.0h; // Adjust based on packing
normal.z = sqrt(1.0h - saturate(dot(normal.xy, normal.xy)));
```

## 7. Shader Variant Management

Too many variants increase build time, memory, and potential runtime hitches.

-   **Minimize Keywords:** Reduce `#pragma multi_compile` and `#pragma shader_feature` use.
    -   `shader_feature`: For material-toggled options. Unused variants stripped from builds.
    -   `multi_compile`: For global options (e.g., quality). All variants included unless stripped.
-   **Create Separate Shaders:** Instead of huge "uber-shaders", consider simpler shaders for different quality/features.
-   **Shader Variant Collection:** Create `ShaderVariantCollection` assets to pre-warm needed variants, preventing runtime compilation stalls. Add used shaders/variants.
-   **Stripping:** Unity auto-strips unused `shader_feature` variants. Use `IPreprocessShaders` for advanced `multi_compile` stripping.
-   **Inspect Variants:** Check shader inspector for variant counts per platform.

## 8. Level of Detail (LOD)

LOD systems reduce complexity for distant objects.

### Mesh LOD
-   Create mesh versions with decreasing polygon counts.
-   Focus detail on silhouettes; simplify flat areas.
-   Aim for ~50% triangle reduction per level.
-   Use Unity's LOD Group component.

### Shader LOD
-   Assign LOD values to SubShaders (`LOD 200`).
-   Set `Shader.maximumLOD` (material) or `Shader.globalMaximumLOD` (global) to control usage.
-   Use simpler shaders (fewer features/textures/calcs) for lower LOD levels.
-   Can disable effects like normal mapping at lower LODs.

### LOD System Design
-   Test transitions to avoid popping. Consider cross-fading (adds cost).
-   Balance gains against memory overhead (multiple meshes/shaders).
-   Less effective for static scenes or very low-poly objects.

## 9. Draw Call Reduction Techniques

Reducing draw calls optimizes CPU mainly, but also GPU state changes.

-   **Static Batching:** Combines static meshes sharing materials. Increases memory. Enable in Player Settings.
-   **Dynamic Batching:** Batches small meshes sharing materials. Has limits, CPU overhead. Enable in Player Settings. Often less useful now than instancing.
-   **GPU Instancing:** Renders multiple copies of same mesh/material in one draw call, supports per-instance data (`MaterialPropertyBlock`). Ideal for foliage, rocks. Enable on material.
-   **URP SRP Batcher:** Reduces CPU time for calls using compatible URP shaders/materials. Auto if compatible.
-   **Mesh Combining:** Manually/procedurally combine meshes, especially static ones sharing materials.
-   **Texture Atlasing:** (See Section 6) Enables material sharing, allowing batching/instancing.
-   **Occlusion Culling:** Prevents rendering hidden objects. Setup in Occlusion Culling window. Reduces GPU work *and* draw calls.
-   **Material Management:** Minimize unique materials. Use `MaterialPropertyBlock` for variations (e.g., color) on instanced objects without new materials.

## 10. Rendering Path Considerations

Choice affects performance and features.

-   **Forward Rendering:** Usually preferred for mobile. Renders object-by-object, calculating lights per object. URP offers Forward and Forward+ (more lights).
    -   *Pros:* Lower bandwidth generally, better MSAA, simpler transparency.
    -   *Cons:* Performance drops with many overlapping dynamic lights.
-   **Deferred Shading:** Renders geometry info to G-buffers, then lights screen-space.
    -   *Pros:* Handles many dynamic lights well (cost decoupled from geometry).
    -   *Cons:* Higher bandwidth, complex transparency, often slower on mobile TBR GPUs, limited MSAA. Usually *not* recommended for mobile.

**Mobile Recommendation:** Use **Forward Rendering** (or URP Forward+). Only consider Deferred if profiling shows benefit for light-heavy scenes on target high-end devices.

## 11. Debugging and Profiling Tools & Techniques

Essential for finding and fixing performance problems.

### 11.1. Unity Built-in Tools

-   **Frame Debugger (`Window > Analysis > Frame Debugger`):**
    -   Steps through draw calls.
    -   Inspect state, properties, textures, buffers per draw call.
    -   Identify issues: excessive calls, state changes, wrong shaders.
    -   Connects to mobile device.
-   **Profiler (`Window > Analysis > Profiler`):**
    -   Shows CPU/GPU usage over time.
    -   Connect to target device.
    -   Examine GPU Usage module (time, markers).
    -   Rendering module (draw calls, batches).
    -   Memory module (texture/RT memory).
    -   Use Deep Profile sparingly.
-   **Shader Variant Collection Tool:** (See Section 7) Manage/analyze variants.
-   **Shader Complexity View (Scene View):** Visualizes relative shader cost (URP/HDRP). Spots expensive materials.

### 11.2. Platform-Specific GPU Tools

Provide deep hardware insights.

-   **Arm Mobile Studio (Mali GPUs):**
    -   *Performance Advisor:* Reports with optimization advice.
    -   *Streamline:* Detailed CPU/GPU performance counters.
    -   *Mali Offline Compiler:* Vital. Analyzes shader source offline for estimated cost, register use, bottlenecks (ALU vs texture).
    -   *Graphics Analyzer:* Frame capture/debugging.
-   **Qualcomm Snapdragon Profiler (Adreno GPUs):**
    -   Captures system traces with GPU metrics.
    -   Analyze GPU utilization, shader time, bandwidth, stalls, ALU use.
    -   *Adreno GPU Profiler (SDK):* Deeper GPU analysis tool.
    -   *Adreno Offline Compiler:* Offline shader analysis for Adreno.
-   **Apple Xcode GPU Tools (iOS/Metal):**
    -   *Metal Frame Debugger:* Capture/debug frames on iOS. Inspect state, resources, code.
    -   *Metal Shader Profiler / GPU Counters Instrument:* Detailed shader metrics and counters during run.

### 11.3. Third-Party Tools

-   **RenderDoc:** Free standalone graphics debugger. Integrates with Unity. Great for pipeline state, shader debug (inputs/outputs, intermediates), resource inspection. Mobile support varies.
-   **NVIDIA Nsight Graphics:** For NVIDIA desktop/Tegra. Useful for PC dev debug before mobile deploy.

### 11.4. Code-Based Debugging Techniques

-   **Debug Shader Variants:** Use `#pragma multi_compile` for debug modes visualizing intermediate values (normals, UVs, lighting).

```hlsl
// Example Debug Shader Variant
#pragma multi_compile _ DEBUG_NORMALS DEBUG_UVS DEBUG_ALBEDO

fixed4 frag(v2f i) : SV_Target
{
    // Calculate base values
    half4 albedo = tex2D(_MainTex, i.uv);
    half3 normal = UnpackNormal(tex2D(_BumpMap, i.uv));
    // ... other calculations ...
    half4 finalColor = //...

    #if defined(DEBUG_NORMALS)
        return fixed4(i.normal * 0.5h + 0.5h, 1.0h); // Visualize world normals
    #elif defined(DEBUG_UVS)
        return fixed4(i.uv, 0.0h, 1.0h); // Visualize UVs
    #elif defined(DEBUG_ALBEDO)
        return albedo; // Visualize just albedo texture
    #else
        // Normal rendering output
        return finalColor;
    #endif
}
```

-   **Visual Debugging via Uniforms:** Use a script-controlled uniform int/float to switch visualization modes in shader code.

```hlsl
// Example Visual Debugging with Uniform
uniform int _DebugMode; // Set from C# script

fixed4 frag(v2f i) : SV_Target
{
    half4 albedo = tex2D(_MainTex, i.uv);
    half3 normal = //...
    half specular = //...

    if (_DebugMode == 1) return albedo;
    else if (_DebugMode == 2) return fixed4(normal * 0.5h + 0.5h, 1.0h);
    else if (_DebugMode == 3) return fixed4(specular.xxx, 1.0h);

    // Normal rendering
    return CalculateFinalColor(...);
}
```

-   **DirectX Debug Symbols:** Add `#pragma enable_d3d11_debug_symbols` for better DirectX debugging (remove for release).

### 11.5. Profiling Best Practices & Workflow

1.  **Establish Baselines:** Benchmark scenes. Profile on target devices. Document metrics (FPS, GPU time). Set budgets.
2.  **Identify Bottlenecks:**
    *   *Fillrate Test:* Lower res. Big FPS gain -> Fillrate/Fragment bound.
    *   *Bandwidth Test:* Lower tex quality. Big FPS gain -> Bandwidth bound.
    *   *Draw Call Test:* Reduce objects. Big FPS gain -> CPU bound (draw calls).
    *   *Vertex Test:* Reduce mesh polys. Big FPS gain -> Vertex/Geometry bound.
3.  **Isolate Shaders:** Test problem shaders in simple scenes.
4.  **Compare Variants:** Profile variants/LODs to quantify cost.
5.  **Cross-Platform Profiling:** Profile on different GPUs (Mali, Adreno, etc.) if needed.
6.  **Iterative Optimization:** Profile -> Identify -> Optimize -> Re-Profile -> Verify.
7.  **Development Workflow:**
    *   Initial shader dev/debug on PC.
    *   Use debug visualizations.
    *   Profile early on target devices.
8.  **Optimization Workflow:**
    *   Use platform tools (offline compilers, profilers) for deep analysis.
    *   Optimize based on bottlenecks.
9.  **Release Workflow:**
    *   Final profiling on all targets.
    *   Remove debug code/variants.
    *   Verify variant counts minimal.
    *   Check final memory use.

## 12. Unity Settings and Features Optimization

### Quality Settings
-   **Anti-Aliasing (MSAA):** Disable or use lowest (2x). Very costly.
-   **Real-Time Reflections:** Disable. Use baked probes.
-   **Shadows:** (See Section 5) Lowest settings or disable.
-   **LOD Bias:** Adjust based on tests (e.g., 0.5-1.0). Controls LOD switches.
-   **Texture Quality:** Globally reduces texture res. Tests bandwidth, offers quality tiers.

### Post-Processing
Be extremely selective; most effects are very costly.
-   **Anti-Aliasing (FXAA/SMAA):** If needed, use fastest option (e.g., FXAA Fast). Has cost.
-   **Avoid/Minimize:** AO, Bloom, SSR, Depth of Field, Motion Blur. Usually too expensive.
-   **Color Grading/Tonemapping:** Relatively cheaper, still has cost. Implement efficiently (LUTs).
-   **Layer Cost:** Enabling Post Processing Layer adds overhead, even if no effects active.

### Mobile-Optimized Shaders/Packages
-   **Unity Mobile Shaders (Built-in):** Basic, optimized shaders.
-   **URP Lit/Unlit:** Designed for mobile. Unlit cheapest.
-   **Third-Party Assets (e.g., OmniShade):** Some offer performant mobile shaders.

## 13. GPU Architecture Specifics (Summary)

-   **PowerVR (ImgTec - TBDR):**
    -   Tile-based (16x16).
    -   *Optimizations:* Varyings cheap. Avoid alpha test (very costly). Avoid `ColorMask`. Avoid fragment depth writes. Optimize tile efficiency.
-   **Adreno (Qualcomm - Tiled):**
    -   Tile-based (larger tiles). Can sometimes use direct rendering.
    -   *Optimizations:* Use lowest bit RTs. Optimize bandwidth. Variable bin size helps.
-   **Mali (ARM - Tiled):**
    -   Tile-based (16x16).
    -   *Optimizations:* Optimize tile efficiency. Reduce bandwidth. Minimize fragment complexity. Use Mali Offline Compiler.
-   **Tegra (NVIDIA - Classic/Direct):**
    -   Traditional immediate mode rendering.
    -   *Optimizations:* More like desktop. Reduce overall instruction count.

## 14. Case Studies & Examples

-   **Fragment-Bound Post-Processing:** Slowdown from full-screen effects. Fix: Reduce resolution, combine passes, simplify/disable.
-   **Texture Bandwidth Bottleneck:** Slow shader sampling many textures. Fix: Pack textures, cache reads, mipmaps, reduce resolution/formats.
-   **Complex Lighting:** Slow realistic lighting. Fix: Move calcs to vertex, use LUTs, simplify model for mobile.
-   **"Crash a Car" Demo:** Used feature toggles, Frame Debugger, optimized terrain shader.
-   **Stylized Games:** Often use Unlit shaders for performance.

## Conclusion

Optimizing Unity HLSL/Cg shaders for mobile is a continuous balancing act. It requires understanding mobile GPU limits, using proper data types and math, managing bandwidth via texture/data optimization, minimizing draw calls, and using LOD. Constant profiling on target devices with Unity and platform tools is essential to find bottlenecks and verify optimizations. Applying these comprehensive best practices enables visually appealing mobile games that perform well across diverse hardware.

---