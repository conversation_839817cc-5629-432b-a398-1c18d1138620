---
description: 
globs: 
alwaysApply: false
---
# Odin Inspector Attributes Reference

## Type Specifics
- `[AssetList(Path = null, AutoPopulate = false, Tags = null, LayerNames = null, AssetNamePrefix = null, CustomFilterMethod = null)]` - Shows list of assets
- `[AssetSelector(Paths = null, SearchInFolders = true, Filter = "t:Object", DrawDropdownForListElements = true, IsUniqueList = false, FlattenTreeView = false, ExpandAllMenuItems = false, CustomFilterMethod = null)]` - Asset selection interface
- `[ChildGameObjectsOnly]` - Restricts object references to child GameObjects
- `[ColorPalette(paletteName: string = null, showAlpha: bool = true)]` - Color selection palette
- `[DisplayAsString]` - Displays value as string in inspector
- `[EnumPaging(pageSize: int = 10, pagerStyle: PagerStyle = PagerStyle.Normal)]` - Pages through enum values
- `[EnumToggleButtons(style: ButtonStyle = ButtonStyle.Box)]` - Shows enum as toggle buttons
- `[FilePath(ParentFolder = "", Extensions = "", RequireValidPath = true, RequireExistingPath = false)]` - File path selection
- `[FolderPath(ParentFolder = "", RequireValidPath = true, RequireExistingPath = false)]` - Folder path selection
- `[HideInInlineEditors]` - Hides property in inline editors
- `[HideInTables]` - Hides property when shown in tables
- `[HideMonoScript]` - Hides script field
- `[HideNetworkBehaviourFields]` - Hides NetworkBehaviour fields
- `[HideReferenceObjectPicker(disable: bool = true)]` - Hides object picker
- `[InlineEditor(InlineEditorModes mode = InlineEditorModes.GUIOnly, expanded: InlineEditorObjectFieldMode = InlineEditorObjectFieldMode.Foldout, bool drawHeader = true, bool drawPreview = true, bool drawGUI = true, float previewHeight = 128f, float previewWidth = 128f, bool increased = false)]` - Inline object editor
- `[MultiLineProperty(int lines = 3, float minHeight = 0)]` - Makes a string field multi-line
- `[PreviewField(float height = 128, ObjectFieldAlignment alignment = ObjectFieldAlignment.Left)]` - Shows preview of assets
- `[PolymorphicDrawerSettings(ShowTypeNameAsDropdownTitle = true)]` - Settings for polymorphic type drawers
- `[SceneObjectsOnly]` - Restricts to scene objects
- `[TableList(ShowIndexLabels: bool = false, AlwaysExpanded: bool = false, NumberOfItemsPerPage: int = 0, ShowPaging: bool = false)]` - Displays list as table
- `[TableMatrix(bool IsReadOnly = false, bool ResizableColumns = true, bool SquareCells = false, bool HideColumnIndices = false, bool HideRowIndices = false)]` - Displays 2D array as matrix
- `[Toggle]` - Creates a toggle field
- `[ToggleLeft]` - Creates a left-aligned toggle

## Essentials
- `[AssetsOnly]` - Restricts to asset references
- `[CustomValueDrawer("Method")]` - Custom value drawing
- `[DelayedProperty]` - Delays value changes until Enter or focus loss
- `[DetailedInfoBox("Summary", "Details", messageType: InfoMessageType = InfoMessageType.Info, visibleIfMemberName: string = null)]` - Displays detailed info
- `[EnableGUI]` - Enables the GUI
- `[GUIColor(float r, float g, float b, float a = 1f)]` - Sets GUI color
- `[HideLabel]` - Hides the label of a property
- `[PropertyOrder(int order)]` - Sets the display order of properties
- `[PropertySpace(spaceBefore: float = 0, spaceAfter: float = 0)]` - Adds space before/after property
- `[ReadOnly]` - Makes a property read-only
- `[Required(errorMessage: string = null)]` - Marks property as required
- `[RequiredIn(string mode)]` - Makes property required in specified mode
- `[SearchableContext(includeChildren: bool = true, searchBoxHeight: int = 20)]` - Makes context searchable
- `[ShowInInspector]` - Shows non-serialized properties
- `[Title("Title", bold: bool = true, horizontalLine: bool = true, titleAlignment: TitleAlignments = TitleAlignments.Left)]` - Displays a title header
- `[TypeFilter("TypeFilter")]` - Filters by type
- `[TypeInfoBox(message: string)]` - Type-level info box
- `[ValidateInput("ValidationMethod", errorMessage: string = null, messageType: InfoMessageType = InfoMessageType.Error, rejectedInvalidInput: bool = false)]` - Custom validation
- `[ValueDropdown("Values", IsUniqueList: bool = false, DrawDropdownForListElements: bool = true, DisableListAddButtonBehaviour: bool = false, ExcludeExistingValuesInList: bool = false, DropdownTitle: string = null, DropdownHeight: int = 350, SortDropdownItems: bool = false, HideChildProperties: bool = false)]` - Creates dropdown from values

## Validation
- `[AssetsOnly]` - Restricts to asset references
- `[ChildGameObjectsOnly]` - Restricts object references to child GameObjects
- `[DisallowModificationsIn(string mode)]` - Prevents modifications in specified mode
- `[FilePath]` - File path selection
- `[FolderPath]` - Folder path selection
- `[MaxValue(double max)]` - Sets maximum value
- `[MinMaxSlider(double min, double max, showFields: bool = true)]` - Creates min/max range slider
- `[MinValue(double min)]` - Sets minimum value
- `[PropertyRange(min, max)]` - Sets property range
- `[Range(double min, double max)]` - Creates a slider with range
- `[Required(errorMessage: string = null)]` - Marks property as required
- `[RequiredIn(string mode)]` - Makes property required in specified mode
- `[RequiredListLength(int min, int max, string message = null)]` - Enforces list length requirements
- `[SceneObjectsOnly]` - Restricts to scene objects
- `[ValidateInput("ValidationMethod", errorMessage: string = null, messageType: InfoMessageType = InfoMessageType.Error, rejectedInvalidInput: bool = false)]` - Custom validation

## Groups
- `[BoxGroup("Group Name", showLabel: bool = true, centerLabel: bool = false, order: int = 0)]` - Groups properties in a box
- `[Button(ButtonSizes size = ButtonSizes.Medium, ButtonStyle style = ButtonStyle.Box, Expanded: bool = false)]` - Creates a button
- `[ButtonGroup(groupName: string = null, order: int = 0)]` - Groups buttons together
- `[FoldoutGroup("Group Name", expanded: bool = true, order: int = 0)]` - Creates a foldout group
- `[HideIfGroup("Condition")]` - Hides group based on condition
- `[HorizontalGroup("Group Name", width: float = 0, marginLeft: float = 0, marginRight: float = 0, minWidth: float = 0, maxWidth: float = float.MaxValue, order: int = 0)]` - Arranges properties horizontally
- `[ResponsiveButtonGroup(DefaultButtonSize: ButtonSizes = ButtonSizes.Medium, UniformLayout: bool = true)]` - Creates responsive button layout
- `[ShowIfGroup("Condition")]` - Shows group based on condition
- `[TabGroup("Tab Group Name", "Tab Name", order: int = 0)]` - Creates tabbed groups
- `[TitleGroup("Title")]` - Creates a titled group
- `[ToggleGroup("ToggleName", groupTitle: string = null, order: int = 0)]` - Creates a toggleable group
- `[VerticalGroup("Group Name", order: int = 0)]` - Arranges properties vertically

## Buttons
- `[Button(ButtonSizes size = ButtonSizes.Medium, ButtonStyle style = ButtonStyle.Box, Expanded: bool = false)]` - Creates a button
- `[ButtonGroup(groupName: string = null, order: int = 0)]` - Groups buttons together
- `[EnumPaging(pageSize: int = 10, pagerStyle: PagerStyle = PagerStyle.Normal)]` - Pages through enum values
- `[EnumToggleButtons(style: ButtonStyle = ButtonStyle.Box)]` - Shows enum as toggle buttons
- `[InlineButton("Method", Label: string = null)]` - Adds inline button
- `[ResponsiveButtonGroup(DefaultButtonSize: ButtonSizes = ButtonSizes.Medium, UniformLayout: bool = true)]` - Creates responsive button layout

## Misc
- `[CustomContextMenu(menuItem: string, methodName: string)]` - Custom context menu
- `[DisableContextMenu(disableForMember: bool = true, disableCollectionElements: bool = false)]` - Disables context menu
- `[DrawWithUnity]` - Uses Unity's drawer
- `[HideDuplicateReferenceBox]` - Hides duplicate reference warning box
- `[Indent(int increment = 1)]` - Indents the property
- `[InfoBox("Message", messageType: InfoMessageType = InfoMessageType.Info, visibleIfMemberName: string = null)]` - Displays an info box
- `[InlineProperty(labelWidth: float = 0)]` - Shows child properties inline
- `[LabelText("Custom Label", NicifyText: bool = true)]` - Sets custom label text
- `[LabelWidth(float width)]` - Sets the width of the property label
- `[OnCollectionChanged("CallbackName")]` - Calls method when collection changes
- `[OnInspectorDispose("callback")]` - Called on dispose
- `[OnInspectorGUI("callback", append: bool = false, prepend: bool = false, order: int = 0)]` - Custom inspector GUI
- `[OnInspectorInit("callback")]` - Called on inspector init
- `[OnStateUpdate("callback")]` - Updates on state change
- `[OnValueChanged("Method", includeChildren: bool = true)]` - Calls method on value change
- `[PropertyTooltip(tooltip: string)]` - Adds property tooltip
- `[SuffixLabel(label: string, overlay: bool = false)]` - Adds suffix label

## Collections
- `[DictionaryDrawerSettings(
    KeyLabel: string = null,
    ValueLabel: string = null,
    DisplayMode: DictionaryDisplayOptions = DictionaryDisplayOptions.OneLine,
    IsReadOnly: bool = false,
    KeyColumnWidth: float = 130f,
    ValueColumnWidth: float = 130f
)]` - Customizes dictionary display
- `[ListDrawerSettings(
    ShowIndexLabels: bool = false,
    ShowPaging: bool = true,
    ShowItemCount: bool = true,
    NumberOfItemsPerPage: int = 15,
    DraggableItems: bool = true,
    Expanded: bool = false,
    IsReadOnly: bool = false,
    ShowItemDropdown: bool = true,
    HideRemoveButton: bool = false,
    HideAddButton: bool = false,
    OnBeginListElementGUI: string = null,
    OnEndListElementGUI: string = null,
    OnTitleBarGUI: string = null,
    CustomAddFunction: string = null,
    CustomRemoveIndexFunction: string = null,
    CustomRemoveElementFunction: string = null
)]` - Customizes list drawer
- `[TableColumnWidth(float width)]` - Sets the width of a table column
- `[TableList]` - Displays list as table
- `[TableMatrix]` - Displays 2D array as matrix
- `[ValueDropdown]` - Creates dropdown from values

## Conditionals
- `[DisableIf("MemberName", optionalValue: object = null)]` - Disables based on condition
- `[DisableIn(string mode)]` - Disables in specified mode
- `[DisableInEditorMode]` - Disables in editor mode
- `[DisableInInlineEditors]` - Disables in inline editors
- `[DisableInPlayMode]` - Disables in play mode
- `[EnableIf("MemberName", optionalValue: object = null)]` - Enables based on condition
- `[EnableIn(string mode)]` - Enables in specified mode
- `[HideIf("MemberName", optionalValue: object = null)]` - Hides based on condition
- `[HideIfGroup("Condition")]` - Hides group based on condition
- `[HideIn(string mode)]` - Hides in specified mode
- `[HideInEditorMode]` - Hides in editor mode
- `[HideInPlayMode]` - Hides in play mode
- `[ShowIf("MemberName", optionalValue: object = null)]` - Shows based on condition
- `[ShowIfGroup("Condition")]` - Shows group based on condition
- `[ShowIn(string mode)]` - Shows in specified mode
- `[ShowInInlineEditors]` - Shows in inline editors

## Numbers
- `[MaxValue(double max)]` - Sets maximum value
- `[MinMaxSlider(double min, double max, showFields: bool = true)]` - Creates min/max range slider
- `[MinValue(double min)]` - Sets minimum value
- `[ProgressBar(0, 100, r: float = 0, g: float = 1, b: float = 0, Height = 12)]` - Shows value as progress bar
- `[PropertyRange(min, max)]` - Sets property range
- `[Unit(string unit)]` - Displays unit label next to value
- `[Wrap]` - Wraps values within a specified range

## Unity
- `[Multiline]` - Unity's multiline text area
- `[Range(min, max)]` - Unity's range slider
- `[Space]` - Unity's space attribute
- `[TextArea]` - Unity's text area

## Debug
- `[ShowDrawerChain(expanded: bool = false)]` - Shows property drawer chain
- `[ShowPropertyResolver]` - Shows property resolver

## Meta
- `[SuppressInvalidAttributeError]` - Suppresses invalid attribute errors

## Notes:
1. All attributes are in the `Sirenix.OdinInspector` namespace
2. Expression-based attributes support C# expressions using @ symbol
3. Many attributes can be combined for complex behaviors
4. Parameters marked with `= value` are optional with the specified default value
5. Some attributes have additional parameters not shown here
6. Check the official documentation for complete parameter lists and updates